{"name": "shopware/production", "type": "project", "license": "MIT", "config": {"optimize-autoloader": true, "apcu-autoloader": true, "platform": {"php": "8.0"}, "sort-packages": true, "allow-plugins": {"cweagans/composer-patches": true}}, "prefer-stable": true, "minimum-stability": "stable", "autoload": {"psr-4": {"Shopware\\Production\\": "src/"}}, "repositories": [{"type": "path", "url": "custom/plugins/*/packages/*", "options": {"symlink": true}}, {"type": "path", "url": "custom/plugins/*", "options": {"symlink": true}}, {"type": "git", "url": "**************:shop-ersatzteilshop/aswo-client.git", "no-api": true, "canonical": false}], "require": {"php": "^8.0", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-redis": "*", "ext-tidy": "*", "composer-runtime-api": "^2.0", "acris/cookie-consent": "^5.3.3", "cogi/affiliate": "*", "cweagans/composer-patches": "^1.7", "ecomdd/aswo-eed-client": "v2.9.23", "enqueue/redis": "^0.10.9", "ers/magiclink-plugin": "^1.0", "ers/repairreview": "^1.0", "flink/cms-anchor": "^1.5", "google/apiclient": "^2.11", "jaybizzle/crawler-detect": "^1.2", "k11/erp-connector": "^1.0", "k11/ersatzteilshop-theme": "^1.2", "k11/guestreviews": "^1.0", "k11/normalize-seo-url": "^1.0", "k11/search-v2": "^1.2", "lenz/shopware-plugin-google-shopping": "^1.0", "mossadal/math-parser": "^1.3", "netzp/netzpblog6": "^1.1", "nzo/url-encryptor-bundle": "^6.0", "parsecsv/php-parsecsv": "^1.2", "phpoffice/phpspreadsheet": "^1.24", "pluszwei/back-in-stock-reminder": "^1.2", "pluszwei/conversion-booster": "^2.0", "predis/predis": "^1.1", "sendinblue/api-v3-sdk": "7.x.x", "sendinblue/plugin-shopware-6": "^3.0", "shopware/administration": "~v6.4.0", "shopware/core": "~v6.4.0", "shopware/elasticsearch": "~v6.4.0", "shopware/enterprise-search-platform": "^3.3", "shopware/recovery": "~v6.4.0", "shopware/storefront": "~v6.4.0", "stutt/redirects": "^1.5", "swag/paypal": "^4.1", "swag/platform-security": "^1.0.24", "twig/cache-extra": "^3.3", "twig/extra-bundle": "^3.3", "unzerdev/shopware6": "~5.11", "webkul/akeneo-connector": "^1.0", "webmatch/tag-manager-sw6": "^2.1", "xanten/appliance-relevance-import": "^1.0", "xanten/aswo": "^1.2", "xanten/google-customer-reviews": "^1.0", "xanten/lastseenproduct": "^1.0", "xanten/order": "^1.0", "xanten/rebuyproduct": "^1.0", "xanten/review-migration": "^1.0", "xanten/send-in-blue": "^1.0"}, "require-dev": {"ext-openssl": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "defuse/php-encryption": "~2.2.1", "dms/phpunit-arraysubset-asserts": "^0.2.1", "johnkary/phpunit-speedtrap": "~3.3.0", "league/flysystem-memory": "~1.0.2", "nikic/php-parser": "~4.10.4", "opis/json-schema": "~1.0.19", "oro/twig-inspector": "~1.1.0", "phpunit/php-code-coverage": "~9.2.5", "phpunit/phpunit": "~9.5.2", "smalot/pdfparser": "~0.14.0", "symfony/browser-kit": "~4.4 || ~5.2.3 || ~5.3.0", "symfony/css-selector": "^5.3", "symfony/dependency-injection": "~4.4 || ~5.2.3 || ~5.3.0", "symfony/dom-crawler": "~4.4 || ~5.2.3 || ~5.3.0", "symfony/phpunit-bridge": "~4.4 || ~5.2.3 || ~5.3.0", "symfony/stopwatch": "~4.4 || ~5.2.3 || ~5.3.0", "symfony/var-dumper": "~4.4 || ~5.2.3 || ~5.3.0", "symfony/web-profiler-bundle": "~4.4 || ~5.2.3 || ~5.3.0"}, "extra": {"composer-exit-on-patch-failure": true, "patches": {"shopware/elasticsearch": {"create index with type": "custom/patches/IndexCreator.patch"}, "shopware/core": {"Improve promotion redemption updater performance - LineItemTransformer": "custom/patches/LineItemTransformer.patch", "Improve promotion redemption updater performance - OrderLineItemDefinition": "custom/patches/OrderLineItemDefinition.patch", "Improve promotion redemption updater performance - OrderLineItemEntity": "custom/patches/OrderLineItemEntity.patch", "Improve promotion redemption updater performance - PromotionDefinition": "custom/patches/PromotionDefinition.patch", "Improve promotion redemption updater performance - PromotionEntity": "custom/patches/PromotionEntity.patch", "Improve promotion redemption updater performance - PromotionRedemptionUpdater": "custom/patches/PromotionRedemptionUpdater.patch", "debug blocked payment method": "custom/patches/SalesChannelIndexerDebug.patch"}, "shopware/storefront": {"add passive to touchmove for zoom-modal.plugin": "custom/patches/zoom-modal.plugin.js.patch", "fix vector.helper.js": "custom/patches/vector.helper.js.patch"}, "shopware/administration": {"fix turnover orders on dashboard": "custom/patches/sw-dashboard-index.patch"}}}}