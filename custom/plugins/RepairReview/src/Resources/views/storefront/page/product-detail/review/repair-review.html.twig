{% sw_extends '@Storefront/storefront/utilities/offcanvas.html.twig' %}

{% block utilities_offcanvas_close %}
{% endblock %}

{% block utilities_offcanvas_content %}

    <div class="d-md-block">
        <div type="button"
             class="product-detail-create-repair-review-toggle"
             data-toggle="collapse"
             data-target=".multi-collapse-repair-review-form"
             aria-expanded="false"
             aria-controls="repair-review-form review-list">
            <span class="product-detail-review-teaser-show">
                <div class="delivery-information__badge delivery-information__badge--short k11-pointer w-100" style="height: 42px;">
                    <svg class="k11-m-5"  width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <mask id="mask0_4982_633745" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="24">
                            <rect x="0.236328" width="24" height="24" fill="#D9D9D9"/>
                        </mask>
                        <g mask="url(#mask0_4982_633745)">
                            <path d="M3.23633 21V16.75L16.4363 3.575C16.6363 3.39167 16.8572 3.25 17.0988 3.15C17.3405 3.05 17.5947 3 17.8613 3C18.128 3 18.3863 3.05 18.6363 3.15C18.8863 3.25 19.103 3.4 19.2863 3.6L20.6613 5C20.8613 5.18333 21.0072 5.4 21.0988 5.65C21.1905 5.9 21.2363 6.15 21.2363 6.4C21.2363 6.66667 21.1905 6.92083 21.0988 7.1625C21.0072 7.40417 20.8613 7.625 20.6613 7.825L7.48633 21H3.23633ZM17.8363 7.8L19.2363 6.4L17.8363 5L16.4363 6.4L17.8363 7.8Z" fill="#202E3D"/>
                        </g>
                    </svg>
                    <span class="k11-mr-5 k11-font-size-14 k11-font-weight-900">{{ "ersatzteilshop.productDetail.writeRepairReview"|trans|sw_sanitize }}</span>
                </div>
            </span>
        </div>
    </div>

    <div class="product-detail-repair-review-wrapper {% if ratingSuccess == -1 %}show{% endif %}">
        <div class="collapse multi-collapse-repair-review-form k11-mt-20 k11-border k11-p-20" id="repair-review-form">
            {% sw_include ('@Storefront/storefront/page/product-detail/review/repair-review-form.html.twig') %}
        </div>
    </div>
    <div class="repair-review-wrapper" data-repair-review="true">
        {% block page_product_detail_review_container %}

            {#TODO: replace items per list config value#}
            {% set reviewsPerListPage = 10 %}

            {#TODO: replace current list page value#}
            {% set currentListPage = 1 %}

            {% set productReviewCount = repairReviews|length %}

            {# TODO: replace language flag #}
            {% set foreignReviewsCount = 150 %}
                <div class="sw-card repair-review-list" >
                    {% for repairReview in repairReviews|slice(0, 4) %}
                        <div class="k11-mt-40">
                            <p class="k11-mb-20 k11-line-height-1">{{ repairReview.customerName }} - {{ repairReview.createdAt|date('d.m.Y H:i') }}</p>
                            <p class="k11-mb-20 k11-line-height-1 k11-font-weight-900 text-break">{{ repairReview.description }}</p>
                            <div class="k11-mb-10">
                                <img class="k11-mr-10" id="clockImage" class="img-fluid" src="{{ asset('bundles/repairreview/assets/img/icon_clock_w.svg') }}"/>
                                <span>{{ repairReview.requiredTime }}</span>
                            </div>

                            <div class="k11-mb-10">
                                <img class="k11-mr-10" id="ciImage" class="img-fluid" src="{{ asset('bundles/repairreview/assets/img/icon_difficulty.svg') }}"/>
                                <span>{{ repairReview.difficulty }}</span>
                            </div>

                            {% if repairReview.requiredTools %}
                                <div class="k11-mb-10">
                                    <img class="k11-mr-10" id="ciImage" class="img-fluid" src="{{ asset('bundles/repairreview/assets/img/icon_es_ci.svg') }}"/>
                                    <span class="text-break">{{ repairReview.requiredTools }}</span>
                                </div>
                            {% endif %}
                        </div>
                        <br>
                    {% endfor %}
                </div>
                {% if repairReviews|length > 4 %}
                    <div class="k11-border d-flex justify-content-center">
                        <button
                                class="k11-black-link k11-line-height-1 loadReviewsButton k11-py-5"
                                id="loadReviewsButton"
                                data-product-Id="{{ repairReviews.productId }}"
                                data-limit="4"
                                data-total-reviews="{{ repairReviews|length }}"
                                >
                            {{ "reviewCollection.loadMore"|trans|sw_sanitize }}
                        </button>
                    </div>
                {% endif %}
        {% endblock %}

    </div>
{% endblock %}
