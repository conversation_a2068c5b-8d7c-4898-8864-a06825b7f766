<?php declare(strict_types=1);

namespace XantenAswo\Service;

use K11\AswoEedClient\DataProvider\EedApi;
use K11\AswoEedClient\Gateways;
use K11\AswoEedClient\ClientService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;

final class ClientFactory
{
    /**
     * @var Config
     */
    private Config $config;

    /**
     * @var AswoSession
     */
    private AswoSession $session;

    /**
     * @var Request|null
     */
    private ?Request $request;

    /**
     * @var ClientService|null
     */
    private ?ClientService $client = null;

    /**
     * ClientFactory constructor.
     *
     * @param Config $config
     * @param AswoSession $session
     * @param RequestStack $requestStack
     */
    public function __construct(Config $config, AswoSession $session, RequestStack $requestStack)
    {
        $this->config = $config;
        $this->session = $session;
        $this->request = $requestStack->getCurrentRequest();
    }

    /**
     *
     */
    public function initialize(): void
    {
        if ($this->client) {
            return;
        }

        $this->client = new ClientService(
            $this->config->getConfig($this->request),
            $this->session->getSessionId()
        );
    }

    /**
     * @return Gateways\ArticleGateway
     */
    public function getArticleGateway(): Gateways\ArticleGateway
    {
        $this->initialize();

        return $this->client->getArticleGateway();
    }

    /**
     * @return Gateways\DeviceGateway
     */
    public function getDeviceGateway(): Gateways\DeviceGateway
    {
        $this->initialize();

        return $this->client->getDeviceGateway();
    }

    /**
     * @return Gateways\BasketGateway
     */
    public function getBasketGateway(): Gateways\BasketGateway
    {
        $this->initialize();

        return $this->client->getBasketGateway();
    }

    /**
     * @return Gateways\OrderGateway
     */
    public function getOrderGateway(): Gateways\OrderGateway
    {
        $this->initialize();

        return $this->client->getOrderGateway();
    }

    /**
     * @return Gateways\GroupGateway
     */
    public function getGroupGateway(): Gateways\GroupGateway
    {
        $this->initialize();

        return $this->client->getGroupGateway();
    }

    public function getApi(): EedApi
    {
        $this->initialize();

        return $this->client->getApi();
    }
}
