<?php declare(strict_types=1);

namespace XantenAswo\Service;

use K11\AswoEedClient\ClientService;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\ConnectException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use K11\AswoEedClient\Exceptions\AswoEedClientException;

final class AswoSession
{
    private const SESSION_NAME = 'AWSO_SESSION_ID';

    private const SESSION_DEFAULT_VALUE = 'auto';

    /**
     * @var Config
     */
    private Config $config;

    /**
     * @var Request|null
     */
    private ?Request $request;

    /**
     * @var string|null
     */
    private ?string $sessionId;

    /**
     * CartConvertedSubscriber constructor.
     *
     * @param Config $config
     * @param RequestStack $requestStack
     */
    public function __construct(Config $config, RequestStack $requestStack)
    {
        $this->config = $config;
        $this->request = $requestStack->getCurrentRequest();
    }

    /**
     * @return string|null
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \JsonException
     */
    private function initSession(): ?string
    {
        $client = new ClientService($this->config->getConfig($this->request), self::SESSION_DEFAULT_VALUE);
        try {
            if (null === ($sessionId = $client->getSessionId())) {
                $client->startNewSession();

                $sessionId = $client->getSessionId();
            }
        } catch (GuzzleException|AswoEedClientException|ConnectException $e) {
            $sessionId = null;
        }

        return $sessionId;
    }

    /**
     *
     */
    public function setRequestSessionId()
    {
        $sessionId = $this->initSession();

        $this->sessionId = $sessionId;

        $this->request && $this->request->getSession()->set(self::SESSION_NAME, $sessionId);
    }

    /**
     *
     */
    public function setPluginSessionId()
    {
        $this->config->setPluginConfig(self::SESSION_NAME, $this->initSession());
    }

    /**
     * @return bool
     */
    public function hasRequestSessionId(): bool
    {
        return true;
//        return ($this->request && $this->request->getSession()->has(self::SESSION_NAME)) || $this->sessionId;
    }

    /**
     * @return string|null
     * @throws \XantenAswo\Exception\InvalidConfigurationException
     */
    public function getSessionId(): ?string
    {
        return self::SESSION_DEFAULT_VALUE;
//        if (!$this->hasRequestSessionId()) {
//            $this->setRequestSessionId();
//        }
//
//        return $this->request ? $this->request->getSession()->get(self::SESSION_NAME) : $this->sessionId;
        /*if ($this->hasRequestSessionId()) {
            return $this->request->getSession()->get(self::SESSION_NAME);
        }

        return $this->config->getPluginConfig(self::SESSION_NAME);*/
    }
}
