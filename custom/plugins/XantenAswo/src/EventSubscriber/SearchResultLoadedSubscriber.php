<?php declare(strict_types=1);

namespace XantenAswo\EventSubscriber;

use XantenAswo\Service\Aswo;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Shopware\Core\Content\Product\Events\ProductSearchResultEvent;
use Ersatzteilshop\Core\Content\Appliance\Event\SearchResultLoadedEvent;

class SearchResultLoadedSubscriber implements EventSubscriberInterface
{
    private const MIN_TERM_LENGTH = 3;

    public function __construct(
        private Aswo $aswo
    ) {
    }

    public static function getSubscribedEvents()
    {
        return [
            SearchResultLoadedEvent::class => [
                'applianceLoaded'
            ],
            ProductSearchResultEvent::class => [
                'productLoaded'
            ]
        ];
    }

    public function applianceLoaded(SearchResultLoadedEvent $event): void
    {

        if ($event->getRequest()->getRequestUri() !== '/suggest') {
            if ($event->getResult()->getTotal()) {
                return;
            }
        } else {
            if ($event->getResult()->getTotal() >= 5) {
                return;
            }
        }
        if (strlen($event->getRequest()->get('search')) < self::MIN_TERM_LENGTH) {
            return;
        }

        $event->getResult()->addState('ASWOResults');

        if ($event->getRequest()->getRequestUri() === '/suggest') {
            $limit = 5 - $event->getResult()->getTotal();
            $event->getResult()->merge(
                $this->aswo->searchDevices(
                    $event->getRequest()->get('search'),
                    1,
                     $limit
                )->getEntities()
            );
        } else {
            $event->getResult()->merge(
                $this->aswo->searchDevices(
                    $event->getRequest()->get('search')
                )->getEntities()
            );
        }

        $event->getResult()->assign(['total' => $event->getResult()->getEntities()->count()]);
    }

    public function productLoaded(ProductSearchResultEvent $event): void
    {
        if ($event->getRequest()->getRequestUri() !== '/suggest') {
            if ($event->getResult()->getTotal()) {
                return;
            }
        } else {
            if ($event->getResult()->getTotal() >= 5) {
                return;
            }
        }

        if (strlen($event->getRequest()->get('search')) < self::MIN_TERM_LENGTH) {
            return;
        }

        if ($event->getResult()->getCriteria()->hasState('SearchTools')) {
            return;
        }

        $event->getResult()->addState('ASWOResults');

        if ($event->getRequest()->getRequestUri() === '/suggest') {
            $limit = 5 - $event->getResult()->getTotal();
            $event->getResult()->merge(
                $this->aswo->searchProducts(
                    $event->getRequest()->get('search'),
                    $event->getSalesChannelContext(),
                    1,
                    $limit,
                    false
                )->getEntities()
            );
        } else {
            $event->getResult()->merge(
                $this->aswo->searchProducts(
                    $event->getRequest()->get('search'),
                    $event->getSalesChannelContext(),
                    1,
                    15,
                    false
                )->getEntities()
            );
        }
        $event->getResult()->assign(['total' => $event->getResult()->getEntities()->count()]);
    }
}
