<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <imports>
        <import resource="cart.xml" />
        <import resource="controllers.xml" />
        <import resource="decorators.xml" />
        <import resource="definitions.xml" />
        <import resource="cache.xml" />
        <import resource="message_handlers.xml" />
        <import resource="pages.xml"/>
        <import resource="subscribers.xml" />
        <import resource="transformers.xml" />
    </imports>

    <services>
        <service id="XantenAswo\Command\ImportCommand">
            <argument type="service" id="xanten_aswo.repository"/>
            <argument type="service" id="product.repository"/>
            <tag name="console.command"/>
        </service>

        <service id="XantenAswo\Command\AswoSessionCommand">
            <argument type="service" id="XantenAswo\Service\AswoSession"/>
            <tag name="console.command"/>
        </service>

        <service id="XantenAswo\Command\RefreshImageCommand">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="messenger.bus.aswo" />
            <tag name="console.command"/>
        </service>

        <service id="K11\AswoEedClient\Interfaces\ProvidesLogging" class="XantenAswo\Service\AswoCloudLogger">
            <argument type="service" id="monolog.logger.aswo_protocol"/>
        </service>

        <service id="XantenAswo\Service\Config">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService" />
            <argument type="service" id="K11\AswoEedClient\Interfaces\ProvidesCaching" />
            <argument type="service" id="K11\AswoEedClient\Interfaces\ProvidesLogging" />
            <argument type="service" id="tax.repository" />
        </service>

        <service id="XantenAswo\Service\ClientFactory">
            <argument type="service" id="XantenAswo\Service\Config" />
            <argument type="service" id="XantenAswo\Service\AswoSession" />
            <argument type="service" id="request_stack" />
        </service>

        <service id="K11\AswoEedClient\Gateways\ArticleGateway">
            <factory service="XantenAswo\Service\ClientFactory" method="getArticleGateway" />
        </service>

        <service id="K11\AswoEedClient\DataProvider\EedApi">
            <factory service="XantenAswo\Service\ClientFactory" method="getApi" />
        </service>

        <service id="K11\AswoEedClient\Gateways\DeviceGateway">
            <factory service="XantenAswo\Service\ClientFactory" method="getDeviceGateway" />
        </service>

        <service id="K11\AswoEedClient\Gateways\BasketGateway">
            <factory service="XantenAswo\Service\ClientFactory" method="getBasketGateway" />
        </service>

        <service id="K11\AswoEedClient\Gateways\OrderGateway">
            <factory service="XantenAswo\Service\ClientFactory" method="getOrderGateway" />
        </service>

        <service id="K11\AswoEedClient\Gateways\GroupGateway">
            <factory service="XantenAswo\Service\ClientFactory" method="getGroupGateway" />
        </service>

        <service id="XantenAswo\Service\Aswo" lazy="true">
            <argument type="service" id="K11\AswoEedClient\Gateways\DeviceGateway" />
            <argument type="service" id="K11\AswoEedClient\Gateways\ArticleGateway" />
            <argument type="service" id="K11\AswoEedClient\Gateways\GroupGateway" />
            <argument type="service" id="XantenAswo\Service\AswoAllocatedProduct" />
            <argument type="service" id="XantenAswo\Service\AswoBlacklistedProduct" />
            <argument type="service" id="XantenAswo\Service\AswoImageService" />
            <argument type="service" id="XantenAswo\Transformer\ProductTransformer"/>
            <argument type="service" id="XantenAswo\Transformer\ProductCollectionTransformer"/>
            <argument type="service" id="XantenAswo\Transformer\ApplianceTransformer"/>
            <argument type="service" id="XantenAswo\Transformer\ApplianceCollectionTransformer"/>
            <argument type="service" id="Shopware\Core\Content\Product\SalesChannel\Price\ProductPriceCalculator"/>
            <argument type="service" id="XantenAswo\Service\Config"/>
            <call method="setLogger">
                <argument type="service" id="logger"/>
            </call>
        </service>

        <service id="XantenAswo\Service\AswoAllocatedProduct">
            <argument type="service" id="xanten_aswo.repository" />
            <argument type="service" id="sales_channel.product.repository"/>
        </service>

        <service id="XantenAswo\Service\AswoBlacklistedProduct">
            <argument type="service" id="XantenAswo\Service\Config" />
        </service>

        <service id="XantenAswo\Service\AswoImageService">
            <argument type="service" id="shopware.filesystem.public" />
            <argument type="service" id="xanten_aswo_image.repository" />
            <argument type="string">%shopware.filesystem.public.url%</argument>
            <argument type="service" id="event_dispatcher" />
            <call method="setLogger">
                <argument type="service" id="logger"/>
            </call>
        </service>

        <service id="XantenAswo\Service\AswoSession">
            <argument type="service" id="XantenAswo\Service\Config" />
            <argument type="service" id="request_stack" />
        </service>

        <service id="XantenAswo\Service\DownloadImageContainer" />

        <service id="XantenAswo\Service\Product">
            <argument type="service" id="xanten_aswo_product.repository" />
            <argument type="service" id="Doctrine\DBAL\Connection" />
            <argument type="service" id="cache.aswo" />
            <argument type="service" id="messenger.bus.aswo" />
        </service>

        <service id="XantenAswo\Service\ProductCaching">
            <argument type="service" id="xanten_aswo_product.repository" />
            <argument type="service" id="xanten_aswo_product_to_appliance.repository" />
            <argument type="service" id="XantenAswo\Service\Config" />
        </service>

        <service id="XantenAswo\Service\AswoCloudLogger">
            <argument type="service" id="monolog.logger.aswo_protocol"/>
        </service>
    </services>
</container>
