<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>
        <service id="XantenAswo\EventSubscriber\LoadAswoApplianceProductSubscriber">
            <argument type="service" id="XantenAswo\Service\Aswo" />
            <tag name="kernel.event_subscriber" />
        </service>

        <service id="XantenAswo\EventSubscriber\LoadTotalAswoApplianceProductSubscriber">
            <argument type="service" id="XantenAswo\Service\Aswo" />
            <tag name="kernel.event_subscriber" />
        </service>

        <service id="XantenAswo\EventSubscriber\CartConvertedSubscriber">
            <argument type="service" id="XantenAswo\Service\Aswo" />
            <tag name="kernel.event_subscriber" />
        </service>

        <service id="XantenAswo\EventSubscriber\ClearApplianceProductIdCaching">
            <argument type="service" id="appliance_product_id_caching.repository"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="XantenAswo\EventSubscriber\DownloadImageSubscriber">
            <argument type="service" id="XantenAswo\Service\DownloadImageContainer" />
            <argument type="service" id="messenger.bus.aswo_image" />
            <tag name="kernel.event_subscriber" />
        </service>

        <service id="XantenAswo\EventSubscriber\ProductListingCriteriaSubscriber">
            <argument type="service" id="XantenAswo\Service\Config" />
            <tag name="kernel.event_subscriber" />
        </service>

        <service id="XantenAswo\EventSubscriber\ApplianceProductCategoriesLoadedSubscriber">
            <argument type="service" id="XantenAswo\Service\Config" />
            <tag name="kernel.event_subscriber" />
        </service>

        <service id="XantenAswo\EventSubscriber\SearchResultLoadedSubscriber">
            <argument type="service" id="XantenAswo\Service\Aswo" />
            <tag name="kernel.event_subscriber" />
        </service>

        <service id="XantenAswo\EventSubscriber\ClearProductCacheSubscriber">
            <argument type="service" id="cache.aswo" />
            <argument type="service" id="cache.http"/>
            <tag name="kernel.event_subscriber" />
        </service>
    </services>
</container>
