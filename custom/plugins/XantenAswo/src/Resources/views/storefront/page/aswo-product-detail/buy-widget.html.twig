{% sw_extends '@Storefront/storefront/page/product-detail/buy-widget.html.twig' %}

{% block page_product_detail_buy_container %}

    {% if page.product.calculatedPrices|length > 1 %}
        {% set displayPrice = false %}

        {% for price in page.product.calculatedPrices %}
            {% if not displayPrice or price.unitPrice < displayPrice %}
                {% set displayPrice = price.unitPrice %}
            {% endif %}
        {% endfor %}

    {% elseif page.product.calculatedPrices|length == 1 %}
        {% set displayPrice = page.product.calculatedPrices.first %}
    {% else %}
        {% set displayPrice = page.product.calculatedPrice %}
    {% endif %}
    <div itemprop="offers" itemscope itemtype="{% if page.product.calculatedPrices|length > 1 %}http://schema.org/AggregateOffer{% else %}http://schema.org/Offer{% endif %}" class="d-grid k11-grid-template-1 k11-md-grid-template-2 k11-column-gap-20 {%if outOfStock %}hidden{% endif %}">
        {% block page_product_detail_data %}
            {% block page_product_detail_data_rich_snippet_url %}
                <meta itemprop="url"
                      content="{{ seoUrl('frontend.detail.page', { productId: page.product.id }) }}"/>
            {% endblock %}

            {% block page_product_detail_data_rich_snippet_price_range %}
                {% if page.product.calculatedPrices|length > 1 %}
                    {% set lowestPrice = false %}
                    {% set highestPrice = false %}

                    {% for price in page.product.calculatedPrices %}
                        {% if not lowestPrice or price.unitPrice < lowestPrice %}
                            {% set lowestPrice = price.unitPrice %}
                        {% endif %}
                        {% if not highestPrice or price.unitPrice > highestPrice %}
                            {% set highestPrice = price.unitPrice %}
                        {% endif %}
                    {% endfor %}

                    <meta itemprop="lowPrice" content="{{ lowestPrice }}"/>
                    <meta itemprop="highPrice" content="{{ highestPrice }}"/>
                    <meta itemprop="offerCount" content="{{ page.product.calculatedPrices|length }}"/>
                {% endif %}
            {% endblock %}

            {% block page_product_detail_data_rich_snippet_price_currency %}
                <meta itemprop="priceCurrency"
                      content="{{ context.currency.translated.shortName }}"/>
            {% endblock %}

            {% block page_product_detail_delivery_informations %}
                {% sw_include '@Storefront/storefront/page/product-detail/delivery-information.html.twig' %}
            {% endblock %}

            <div class="product_detail_information_wrapper k11-border k11-p-20">
                <div class="d-flex flex-column h-100">
                    {% block page_product_at_ot_badge %}
                        {% sw_include "@Storefront/storefront/component/product/aswo_atot_badge.html.twig" with {product: page.product} only %}
                    {% endblock %}

                    {% block page_product_cross_selling_link %}
                        {% if crossSellingProduct is not null %}
                            {% set atotPropertyGroupId = config('Ersatzteilshop.config.atotPropertyGroup') %}
                            {% if atotPropertyGroupId %}
                                {% set productProperty = page.product.properties.filterByProperty('groupId', atotPropertyGroupId) | first %}
                                {% if productProperty %}
                                    {% set translationKey = 'product.property.atot.' ~ productProperty.translated.name %}
                                    {% set propertyName = translationKey|trans|sw_sanitize %}
                                    {% if propertyName is same as(translationKey) %}
                                        {% set propertyName = productProperty.translated.name %}
                                    {% endif %}
                                    {% if propertyName %}
                                        {% if propertyName is same as ('Alternativteil') %}
                                            <div class="k11-mb-20">
                                                <a class="k11-black-link k11-w-100 k11-md-w-auto" href="{{ path('frontend.detail.page', {'productId': crossSellingProduct.id}) }}">
                                                    <u>
                                                        auch als Originalteil erhältlich
                                                    </u>
                                                </a>
                                            </div>
                                        {% else %}
                                            <div class="k11-mb-20">
                                                <a class="k11-black-link k11-w-100 k11-md-w-auto" href="{{ path('frontend.detail.page', {'productId': crossSellingProduct.id}) }}">
                                                    <u>
                                                        x€ sparen mit Alternativteil
                                                    </u>
                                                </a>
                                            </div>
                                        {% endif %}
                                    {% endif %}
                                {% endif %}
                            {% endif %}
                        {% endif %}
                    {% endblock %}

                    {% block page_product_detail_reviews %}
                        <div {% if outOfStock %} class="hidden" {% endif %}>
                            {% if page.product.ratingAverage > 0 and config('core.listing.showReview') %}
                                <a href="#review" class="k11-black-link star-rating" onclick="document.getElementById('review').classList.remove('hide')">
                                    {% set rating = page.product.ratingAverage %}
                                    {% set full_stars = rating|round(0, 'floor') %}
                                    {% set partial_fill = (rating - full_stars) * 100 %}

                                    {% for i in 1..5 %}
                                        {% if i <= full_stars %}
                                            {# Full star #}
                                            <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <mask id="mask0_4790_28545_{{ i }}" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="25">
                                                    <rect x="0.526367" y="0.231689" width="24" height="24" fill="#D9D9D9"/>
                                                </mask>
                                                <g mask="url(#mask0_4790_28545_{{ i }})">
                                                    <path d="M12.5264 2.23169L15.3264 8.85669L22.5264 9.48169L17.0764 14.2067L18.7014 21.2317L12.5264 17.5067L6.35137 21.2317L7.97637 14.2067L2.52637 9.48169L9.72637 8.85669L12.5264 2.23169Z" fill="#202E3D"/>
                                                </g>
                                            </svg>
                                        {% elseif i == full_stars + 1 and partial_fill > 0 %}
                                            {# Partially filled star #}
                                            <svg width="25" height="25" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 25">
                                                <mask id="mask0_4790_28547_{{ i }}" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="25">
                                                    <rect x="0.526367" y="0.231689" width="24" height="24" fill="#D9D9D9"/>
                                                </mask>
                                                <g mask="url(#mask0_4790_28547_{{ i }})">
                                                    <path d="M12.5264 2.23169L15.3264 8.85669L22.5264 9.48169L17.0764 14.2067L18.7014 21.2317L12.5264 17.5067L6.35137 21.2317L7.97637 14.2067L2.52637 9.48169L9.72637 8.85669L12.5264 2.23169ZM12.5264 6.55669L10.9514 10.3317L6.77637 10.7817L9.67637 13.2067L8.80137 17.3067L12.5264 15.1817L16.2514 17.3067L15.3764 13.2067L18.2764 10.7817L14.1014 10.3317L12.5264 6.55669Z" fill="#202E3D"/>
                                                </g>
                                                <clipPath id="star-clip-{{ i }}">
                                                    {% set adjustedWidth = 21 * partial_fill / 100 %}
                                                    {% set adjustedX = 2.526367 - (adjustedWidth * 0.05) %}
                                                    <rect x="{{ adjustedX }}" y="0.231689" width="{{ adjustedWidth }}" height="24"/>
                                                </clipPath>
                                                <path clip-path="url(#star-clip-{{ i }})" d="M12.5264 2.23169L15.3264 8.85669L22.5264 9.48169L17.0764 14.2067L18.7014 21.2317L12.5264 17.5067L6.35137 21.2317L7.97637 14.2067L2.52637 9.48169L9.72637 8.85669L12.5264 2.23169Z" fill="#202E3D"/>
                                            </svg>
                                        {% else %}
                                            {# Empty star #}
                                            <svg width="25" height="25" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 25">
                                                <mask id="mask0" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="25">
                                                    <rect x="0.526367" y="0.231689" width="24" height="24" fill="#D9D9D9"/>
                                                </mask>
                                                <g mask="url(#mask0)">
                                                    <path d="M12.5264 2.23169L15.3264 8.85669L22.5264 9.48169L17.0764 14.2067L18.7014 21.2317L12.5264 17.5067L6.35137 21.2317L7.97637 14.2067L2.52637 9.48169L9.72637 8.85669L12.5264 2.23169ZM12.5264 6.55669L10.9514 10.3317L6.77637 10.7817L9.67637 13.2067L8.80137 17.3067L12.5264 15.1817L16.2514 17.3067L15.3764 13.2067L18.2764 10.7817L14.1014 10.3317L12.5264 6.55669Z" fill="#202E3D"/>
                                                </g>
                                            </svg>
                                        {% endif %}
                                    {% endfor %}

                                    <u>({{ page.reviews.total }})</u>
                                </a>
                            {% endif %}
                        </div>
                    {% endblock %}

                    <div class="product-detail-gpsr-artikelinfos-link">
                        <p><a href="#gpsr">GPSR Artikelinfos anzeigen</a></p>
                    </div>

                    <div class="mt-auto">
                        <p class="k11-mb-10">{{ 'ersatzteilshop.productDetail.paymentMethodsLabel'|trans|sw_sanitize }}</p>
                        <img src="{{ asset('bundles/ersatzteilshop/assets/icons/icon-pdp-payment-methods.png') }}"
                             class="product-detail-payment-methods-image"
                             alt="icon-pdp-payment-methods"
                             title="icon-pdp-payment-methods"
                             width="266px"
                             height="42px"
                             loading="eager"
                        >
                    </div>
                </div>
            </div>

        {% endblock %}

        <div class="product_detail_buy_wrapper k11-border k11-p-20">
            {% block page_product_detail_buy_form %}

                {% block page_product_detail_price_tax %}
                    <div class="{% if page.product.calculatedPrices|length <= 1 %}is-single-price k11-mb-10{% endif %} ">
                        {% sw_include '@Storefront/storefront/page/product-detail/buy-widget-price.html.twig' %}
                    </div>

                    {% if context.taxState == "gross" %}
                        {% if displayPrice.unitPrice > 30 %}
                            {% set taxText = "general.grossTaxInformationFreeShip"|trans %}
                        {% else %}
                            {% set taxText = "general.grossTaxInformation"|trans %}
                        {% endif %}

                        {% if page.product.customFields.es_product_free_shipping_de %}
                            {% set taxText = "general.grossFreeShipping"|trans %}
                        {% endif %}

                    {% else %}
                        {% set taxText = "general.netTaxInformation"|trans %}
                    {% endif %}

                    <p class="k11-mb-0 k11-pb-20 k11-pointer k11-line-height-1" data-toggle="modal"
                       data-url="{{ path('frontend.widget.countryShippingCost') }}">
                        <u>{{ taxText|raw }}</u>
                    </p>
                {% endblock%}

                {% block page_product_detail_delivery_time %}
                    <div>
                        {% set product = page.product %}
                        {% sw_include '@Storefront/storefront/component/product/delivery-time.html.twig' with {
                            enableNotification: false
                        } %}
                    </div>
                {% endblock %}

                {% block page_product_detail_buy_widget_form %}
                    {% if page.product.active %}
                        <div>
                            {% sw_include '@Storefront/storefront/page/product-detail/v3/buy-widget-form.html.twig' %}
                        </div>
                    {% endif %}
                {% endblock %}
            {% endblock %}
        </div>

        {% block page_product_detail_paypal_installmentBanner %}
            {# @var \Swag\PayPal\Installment\Banner\BannerData installmentBanner #}
            {% set installmentBanner = page.extensions[constant('Swag\\PayPal\\Installment\\Banner\\InstallmentBannerSubscriber::PAYPAL_INSTALLMENT_BANNER_DATA_EXTENSION_ID')] %}
            {% if installmentBanner is not null %}
                {% if app.request.session.get(constant('Ersatzteilshop\\Util\\ABTesting::LAZY_PAYPAL_BUTTON')) is same as(constant('Ersatzteilshop\\Util\\ABTesting::LAZY_PAYPAL_BUTTON_VERSION_A')) %}
                    <div class="form-row mt-3 mb-4">
                        <div data-swag-paypal-installment-banner="true"
                             class="col"
                             data-swag-pay-pal-installment-banner-options="{{ installmentBanner|json_encode }}">
                        </div>
                    </div>
                {% else %}
                    <div>
                        <img id="paypal-banner" width="60%" src="{{ asset('bundles/ersatzteilshop/assets/img/paypal-banner.png') }}" />
                    </div>
                {% endif %}
            {% endif %}
        {% endblock %}

        {% block page_product_detail_configurator_include %}
        {% endblock %}
    </div>
{% endblock %}

{% block page_product_detail_item_props %}
    {% block page_product_detail_ordernumber_label %}
    {% endblock %}
    {% block page_product_detail_ordernumber_container %}
        <meta itemprop="productID"
              content="{{ page.product.id }}"/>
        <meta itemprop="sku"
              content="{{ page.product.productNumber }}">
        <meta itemprop="description"
              content="{{ page.product.metaDescription }}">
    {% endblock %}
{% endblock %}
