{% sw_extends '@Storefront/storefront/page/_page.html.twig' %}
{% set appliance = page.appliance %}

{% set modelName = appliance.modelName is not same as ('(to be decided)') ? appliance.modelName : '' %}
{% set categoryName = appliance.category.translated.name is not same as ('(<PERSON><PERSON><PERSON>)') ? appliance.category.translated.name : '' %}
{% set modelCode = appliance.modelCode %}
{% set manufacturerName = appliance.manufacturer.name %}

{% set totalProduct = appliance.getCachedProductTotal() %}

{% set applianceGroup = page.applianceGroup %}
{% set applianceGroupAppliances = page.applianceGroupAppliances %}

{% block base_head %}
    {{ parent() }}
    <script type="text/javascript" data-cfasync="false">
        window.applianceCategories = {
            url: "{{ seoUrl('frontend.xanten-aswo.appliance.categories', {applianceId: appliance.id})|lower }}",
            title: "{{ 'ersatzteilshop.appliance.filteredByCategory'|trans|sw_sanitize }}"
        };
        window.productsUrl = "{{ seoUrl('frontend.xanten-aswo.appliance.products', {applianceId: appliance.id})|lower }}";
    </script>
    <script type="text/javascript" async src="{{ asset('bundles/ersatzteilshop/assets/js/a-appliance-products.js') }}" data-cfasync="false"></script>

    {#
    <script type="text/javascript" async src="{{ asset('bundles/ersatzteilshop/assets/js/appliance-cached-products.js') }}" data-cfasync="false"></script>
    <script type="text/javascript" async src="{{ asset('bundles/ersatzteilshop/assets/js/a-appliance-products.js') }}" data-cfasync="false"></script>
    #}
{% endblock %}

{% block page_sidebar %}
{% endblock %}

{# main content #}
{% block page_main_content %}
    <div class="col-md-12">
        <main class="page-main-content">
            {% block page_breadcrumb %}
                {% sw_include '@Ersatzteilshop/storefront/page/appliance/components/breadcrumb.html.twig' with {
                    modelName,
                    modelCode
                } %}
            {% endblock %}

            {% block page_content %}
                {% block appliance_detail_title %}
                    <div class="k11-pt-30 k11-pb-40 k11-md-py-20">
                        {% sw_include '@Ersatzteilshop/storefront/page/appliance/components/headline.html.twig' %}
                    </div>
                {% endblock %}

                {% block appliance_detail_headbox %}
                    {% sw_include '@Ersatzteilshop/storefront/page/appliance/components/headbox.html.twig' %}
                {% endblock %}

                <div class="appliance-tab">

                    {% block appliance_tab %}
                        <ul class="nav k11-gap-10" role="tablist">
                            <li class="tab">
                                <a class="tab-head active"
                                   id="product-list-tab"
                                   data-hash="product-list"
                                   data-toggle="tab"
                                   href="#product-list"
                                   role="tab"
                                   aria-controls="product-list"
                                   aria-selected="true">
                                    <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <mask id="mask0_4516_21367" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="24">
                                            <rect x="0.121094" width="24" height="24" fill="#D9D9D9"/>
                                        </mask>
                                        <g mask="url(#mask0_4516_21367)">
                                            <path d="M9.37129 22L8.97129 18.8C8.75462 18.7167 8.55046 18.6167 8.35879 18.5C8.16712 18.3833 7.97962 18.2583 7.79629 18.125L4.82129 19.375L2.07129 14.625L4.64629 12.675C4.62962 12.5583 4.62129 12.4458 4.62129 12.3375V11.6625C4.62129 11.5542 4.62962 11.4417 4.64629 11.325L2.07129 9.375L4.82129 4.625L7.79629 5.875C7.97962 5.74167 8.17129 5.61667 8.37129 5.5C8.57129 5.38333 8.77129 5.28333 8.97129 5.2L9.37129 2H14.8713L15.2713 5.2C15.488 5.28333 15.6921 5.38333 15.8838 5.5C16.0755 5.61667 16.263 5.74167 16.4463 5.875L19.4213 4.625L22.1713 9.375L19.5963 11.325C19.613 11.4417 19.6213 11.5542 19.6213 11.6625V12.3375C19.6213 12.4458 19.6046 12.5583 19.5713 12.675L22.1463 14.625L19.3963 19.375L16.4463 18.125C16.263 18.2583 16.0713 18.3833 15.8713 18.5C15.6713 18.6167 15.4713 18.7167 15.2713 18.8L14.8713 22H9.37129ZM12.1713 15.5C13.138 15.5 13.963 15.1583 14.6463 14.475C15.3296 13.7917 15.6713 12.9667 15.6713 12C15.6713 11.0333 15.3296 10.2083 14.6463 9.525C13.963 8.84167 13.138 8.5 12.1713 8.5C11.188 8.5 10.3588 8.84167 9.68379 9.525C9.00879 10.2083 8.67129 11.0333 8.67129 12C8.67129 12.9667 9.00879 13.7917 9.68379 14.475C10.3588 15.1583 11.188 15.5 12.1713 15.5Z" fill="#202E3D"/>
                                        </g>
                                    </svg>
                                    <span class="nav-link-text-md k11-pr-10">Ersatzteile</span>
                                </a>
                            </li>
                        </ul>
                    {% endblock %}

                    {% block appliance_content %}
                        <template id="no-products">
                            {% if category == "" %}
                                <div class="content-heading k11-mx-15" style="margin-top: 0px;">
                                    <div class="col text-left k11-px-20 k11-py-17">
                                        <span>
                                            <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <mask id="mask0_3754_3663" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="25">
                                                    <rect y="0.5" width="24" height="24" fill="#D9D9D9"/>
                                                </mask>
                                                <g mask="url(#mask0_3754_3663)">
                                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9 20.5L12 23.5L15 20.5H19C19.55 20.5 20.0208 20.3042 20.4125 19.9125C20.8042 19.5208 21 19.05 21 18.5V4.5C21 3.95 20.8042 3.47917 20.4125 3.0875C20.0208 2.69583 19.55 2.5 19 2.5H5C4.45 2.5 3.97917 2.69583 3.5875 3.0875C3.19583 3.47917 3 3.95 3 4.5V18.5C3 19.05 3.19583 19.5208 3.5875 19.9125C3.97917 20.3042 4.45 20.5 5 20.5H9ZM12 8.5C12.2833 8.5 12.5208 8.40417 12.7125 8.2125C12.9042 8.02083 13 7.78333 13 7.5C13 7.21667 12.9042 6.97917 12.7125 6.7875C12.5208 6.59583 12.2833 6.5 12 6.5C11.7167 6.5 11.4792 6.59583 11.2875 6.7875C11.0958 6.97917 11 7.21667 11 7.5C11 7.78333 11.0958 8.02083 11.2875 8.2125C11.4792 8.40417 11.7167 8.5 12 8.5ZM11 9.5V16.5H13V9.5H11Z" fill="#202E3D"/>
                                                </g>
                                            </svg>
                                        </span>
                                        <span>
                                            {{ 'ersatzteilshop.appliance.search.empty.description'|trans|sw_sanitize }}
                                        </span>
                                    </div>
                                </div>
                            {% endif %}
                        </template>
                        <div class="tab-content search-result-tab-content" id="searchResultTabContent">
                            <div class="tab-pane fade show active"
                                 id="product-list"
                                 role="tabpanel"
                                 aria-labelledby="product-list-tab">

                                <div class="k11-border-green d-flex align-items-center k11-mt-20 k11-mb-40" id="appliance-search">
                                    <div class="input-group search-input border-0 k11-border-radius-8 k11-pl-15 form-control" style="line-height: 1;">
                                        <input type="text" placeholder="Suchbegriff" name="search" id="appliance-search-input" class="input-group search-input border-0 outline-none bg-none w-100" autocomplete="off"/>
                                        <input type="text" class="d-none" name="category"/>
                                    </div>
                                    <div class="svg-container">
                                        <button class="search-input--btn border-0 p-0 outline-none bg-none" type="submit">
                                            <svg class="search-input--btn border-0 outline-none bg-none" style="margin: 2px;" width="35" height="34" viewBox="0 0 35 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <rect x="0.473633" width="34" height="34" rx="4" fill="#74CB7B"/>
                                                <mask id="mask0_4504_106546" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="5" y="5" width="25" height="24">
                                                    <rect x="5.47363" y="5" width="24" height="24" fill="#D9D9D9"/>
                                                </mask>
                                                <g mask="url(#mask0_4504_106546)">
                                                    <path d="M25.0736 26L18.7736 19.7C18.2736 20.1 17.6986 20.4167 17.0486 20.65C16.3986 20.8833 15.707 21 14.9736 21C13.157 21 11.6195 20.3708 10.3611 19.1125C9.1028 17.8542 8.47363 16.3167 8.47363 14.5C8.47363 12.6833 9.1028 11.1458 10.3611 9.8875C11.6195 8.62917 13.157 8 14.9736 8C16.7903 8 18.3278 8.62917 19.5861 9.8875C20.8445 11.1458 21.4736 12.6833 21.4736 14.5C21.4736 15.2333 21.357 15.925 21.1236 16.575C20.8903 17.225 20.5736 17.8 20.1736 18.3L26.4736 24.6L25.0736 26ZM14.9736 19C16.2236 19 17.2861 18.5625 18.1611 17.6875C19.0361 16.8125 19.4736 15.75 19.4736 14.5C19.4736 13.25 19.0361 12.1875 18.1611 11.3125C17.2861 10.4375 16.2236 10 14.9736 10C13.7236 10 12.6611 10.4375 11.7861 11.3125C10.9111 12.1875 10.4736 13.25 10.4736 14.5C10.4736 15.75 10.9111 16.8125 11.7861 17.6875C12.6611 18.5625 13.7236 19 14.9736 19Z" fill="#1C1B1F"/>
                                                </g>
                                            </svg>
                                        </button>
                                    </div>
                                </div>

                                <div class="cms-element-product-listing">
                                    <div class="cms-listing-row js-listing-wrapper">
                                        <table id="a-appliance-products" class="a-appliance-products" style="width: 100%;">
                                            <thead>
                                                <tr><th style="display: none">Product</th></tr>
                                            </thead>
                                            <tbody>
                                            {% for i in 1..10 %}
                                                <tr class="odd has-element-loader loading-placeholder">
                                                    <td>
                                                        <div class="cms-element-product-listing">
                                                            <div class="cms-listing-col col-md-12" data-product-type="aswo" data-product-id="9516443">
                                                                <div class="card product-box box-full box-loading">
                                                                    <div class="card-body badge-container-mobile">
                                                                        <a class="card-body badge-container-mobile-content" title="Platzhalterbox">
                                                                            <div class="product-badges">
                                                                            </div>
                                                                            <div class="product-image">
                                                                            </div>
                                                                            <div class="product-info k11-mb-10">
                                                                                <div class="product-data-at-ot k11-my-10 k11-md-mt-0">
                                                                                </div>
                                                                                <div class="product-data-name">
                                                                                </div>
                                                                                <div class="product-data-detail k11-mb-10">
                                                                                </div>
                                                                            </div>
                                                                        </a>
                                                                        <div class="product-price-wrapper">
                                                                            <div class="product-data-price">
                                                                            </div>
                                                                            <div class="product-data-delivery">
                                                                            </div>
                                                                            <div class="product-data-button">
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endblock %}
                </div>
            {% endblock %}

            {% block base_script_hmr_mode %}
                <script type="text/javascript" src="{{ asset('bundles/ersatzteilshop/assets/js/vendor-node.js') }}"></script>
                <script type="text/javascript" src="{{ asset('bundles/ersatzteilshop/assets/js/vendor-shared.js') }}"></script>
                <script type="text/javascript" src="{{ asset('bundles/ersatzteilshop/assets/js/runtime.js') }}"></script>
                <script type="text/javascript" src="{{ asset('bundles/ersatzteilshop/assets/js/appliances.js') }}"></script>
            {% endblock %}
        </main>
    </div>
{% endblock %}
