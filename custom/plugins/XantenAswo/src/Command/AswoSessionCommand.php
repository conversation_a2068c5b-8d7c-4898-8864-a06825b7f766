<?php declare(strict_types=1);

namespace XantenAswo\Command;

use XantenAswo\Service\AswoSession;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Class ImportCommand
 * @package XantenAswo\Command
 */
class AswoSessionCommand extends Command
{
    protected static $defaultName = 'xanten-aswo:session';

    /**
     * @var AswoSession
     */
    private AswoSession $session;

    /**
     * ImportCommand constructor.
     *
     * @param AswoSession $session
     */
    public function __construct(AswoSession $session)
    {
        $this->session = $session;
        parent::__construct();
    }

    /**
     *
     */
    protected function configure()
    {
        $this
            ->setDescription('ASWO session initialize');
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     *
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->session->setPluginSessionId();

        return 0;
    }
}
