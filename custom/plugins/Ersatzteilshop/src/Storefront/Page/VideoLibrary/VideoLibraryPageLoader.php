<?php declare(strict_types=1);

namespace Ersatzteilshop\Storefront\Page\VideoLibrary;

use Ersatzteilshop\Core\Content\VideoLibrary\Exception\VideoLibraryNotFoundException;
use Ersatzteilshop\Core\Content\VideoLibrary\SalesChannel\AbstractVideoLibraryRoute;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Sorting\FieldSorting;
use Shopware\Core\Framework\Routing\Exception\MissingRequestParameterException;
use Shopware\Storefront\Page\GenericPageLoaderInterface;
use Shopware\Core\Framework\Uuid\Uuid;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Ersatzteilshop\Event\VideoLibraryPageLoadedEvent;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepositoryInterface;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\NotFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\ContainsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\OrFilter;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Shopware\Core\Content\Cms\SalesChannel\SalesChannelCmsPageLoaderInterface;

class VideoLibraryPageLoader
{
    private const APPLIANCE_DISPLAY_NAMES = [
        'waschmaschine' => 'Waschmaschine',
        'kuehlschrank' => 'Kühlschrank',
        'saugroboter' => 'Saugroboter',
        'geschirrspueler' => 'Geschirrspüler',
        'gefrierschrank' => 'Gefrierschrank',
        'kaffeemaschine' => 'Kaffeemaschine',
        'mikrowelle' => 'Mikrowelle',
        'sodastream' => 'Sodastream',
        'trockner' => 'Trockner',
        'backofen' => 'Backofen',
        'kochfeld' => 'Kochfeld',
        'staubsauger' => 'Staubsauger',
        'herd' => 'Herd',
        'dunstabzugshaube' => 'Dunstabzugshaube',
        'side-by-side' => 'Side-by-Side',
        'sonstiges' => 'Sonstiges',
    ];

    private const VIDEO_TYPES = [
        'reparatur' => 'Reparatur',
        'fehlerdiagnose' => 'Fehlerdiagnose',
        'fehlercode' => 'Fehlercode',
        'wissenswert' => 'Wissenswert',
        'pflege' => 'Pflege',
    ];

    public function __construct(
        private GenericPageLoaderInterface $genericLoader,
        private EntityRepositoryInterface $videoLibraryRepository,
        private EventDispatcherInterface  $dispatcher,
        private AbstractVideoLibraryRoute $videoLibraryRoute,
        private SalesChannelCmsPageLoaderInterface $cmsPageLoader,
    ) {}

    public function load($request, SalesChannelContext $salesChannelContext): VideoLibraryPage
    {
        $page = $this->genericLoader->load($request, $salesChannelContext);
        /** @var VideoLibraryPage $page */
        $page = VideoLibraryPage::createFrom($page);

        $videoLibraryId = $request->attributes->get('videoLibraryId');
        if (!$videoLibraryId) {
            throw new NotFoundHttpException();
        }

        $criteria = new Criteria([$videoLibraryId]);
        $criteria->addAssociation('manufacturers');
        $criteria->addAssociation('translations');

        $videoLibrary = $this->videoLibraryRepository
            ->search($criteria, $salesChannelContext->getContext())
            ->first();

        if (!$videoLibrary) {
            throw new VideoLibraryNotFoundException($videoLibraryId);
        }

        if ($videoLibrary->getCmsPageId() !== null) {
            $criteria = new Criteria([$videoLibrary->getCmsPageId()]);
            $cmsPage = $this->cmsPageLoader->load(
                $request,
                $criteria,
                $salesChannelContext
            )->first();
            $videoLibrary->setCmsPage($cmsPage);
        }

        $page->setVideoLibrary($videoLibrary);
        $relatedCriteria = new Criteria();
        $relatedCriteria->addFilter(new EqualsFilter('hide', false));
        $relatedCriteria->addFilter(new EqualsFilter('videoType', $videoLibrary->getVideoType()));
        $relatedCriteria->addFilter(new NotFilter(NotFilter::CONNECTION_AND, [
            new EqualsFilter('id', $videoLibraryId)
        ]));
        $relatedCriteria->addSorting(
            new FieldSorting('cmsPageId', FieldSorting::DESCENDING, true),
            new FieldSorting('createdAt', FieldSorting::DESCENDING)
        );
        
        $appliances = $videoLibrary->getAppliances();
        if (!empty($appliances)) {
            if (in_array('alle', $appliances)) {
            } else {
                $applianceFilters = array_map(
                    fn($appliance) => new ContainsFilter('appliances', sprintf('"%s"', $appliance)),
                    array_filter($appliances)
                );
                
                if (!empty($applianceFilters)) {
                    $relatedCriteria->addFilter(new OrFilter($applianceFilters));
                }
            }
        }
        
        $manufacturers = $videoLibrary->getManufacturers();
        if (!empty($manufacturers)) {
            $manufacturerFilters = array_map(
                fn($manufacturer) => new ContainsFilter('manufacturers', sprintf('"%s"', $manufacturer)),
                array_filter($manufacturers)
            );
            if (!empty($manufacturerFilters)) {
                $relatedCriteria->addFilter(new OrFilter($manufacturerFilters));
            }
        }

        $relatedCriteria->addSorting(new FieldSorting('createdAt', FieldSorting::DESCENDING));

        $relatedVideos = $this->videoLibraryRepository->search($relatedCriteria, $salesChannelContext->getContext());
        $page->setRelatedVideos($relatedVideos);

        $metaTitle = $videoLibrary->getMetaTitle();
        if (!empty($metaTitle)) {
            $page->getMetaInformation()->setMetaTitle($metaTitle);
        } else {
            $page->getMetaInformation()->setMetaTitle($videoLibrary->getTitle());
        }

        $metaDescription = $videoLibrary->getMetaDescription();
        if (!empty($metaDescription)) {
            $page->getMetaInformation()->setMetaDescription($metaDescription);
        } else {
            $description = $videoLibrary->getDescription();
            if (!empty($description)) {
                $truncatedDescription = mb_substr($description, 0, 160);
                $page->getMetaInformation()->setMetaDescription($truncatedDescription . '...');
            }
        }
        $this->dispatcher->dispatch(new VideoLibraryPageLoadedEvent($page, $salesChannelContext, $request));
        return $page;
    }

    public function loadOverview($request, SalesChannelContext $salesChannelContext): VideoLibraryOverviewPage
    {
        $page = $this->genericLoader->load($request, $salesChannelContext);
        $page = VideoLibraryOverviewPage::createFrom($page);

        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('hide', false));

        $criteria->addAssociation('cmsPage');
        $criteria->addSorting(new FieldSorting('customFields.ersatzteilshop_video_library_volume', FieldSorting::DESCENDING));
        $criteria->addSorting(new FieldSorting('createdAt', FieldSorting::DESCENDING));

        $videos = $this->videoLibraryRepository->search($criteria, $salesChannelContext->getContext());

        $page->setVideos($videos);

        $page->setNoResultVideoIds([
            '91b2388c1a084ce8884213d18306630b',
            '1faf434b1f1f4631afa5cc61637a724c',
            '0c5b1112a75f4d8d90d3161d76e9f65a',
            '205e78a5bbb243acbf00c98892a42b7e',
            'accacced706440328491f9345fddf44d',
            'a56aa9652e2d4df48721a424807e488b'
        ]);

        $allAppliances = [];
        foreach ($videos->getEntities() as $video) {
            if ($videoAppliances = $video->getAppliances()) {
                foreach ($videoAppliances as $appliance) {
                    if (!empty($appliance) && $appliance !== 'alle') {
                        $allAppliances[] = $appliance;
                    }
                }
            }
        }
        $uniqueAppliances = array_unique($allAppliances);
        sort($uniqueAppliances);
        $appliancesMapped = array_map(static function ($appliance) {
            return [
                'name' => self::APPLIANCE_DISPLAY_NAMES[$appliance] ?? $appliance,
                'value' => $appliance
            ];
        }, $uniqueAppliances);

        $videoTypes = [];
        foreach ($videos->getEntities() as $video) {
            $videoTypes[] = $video->getVideoType();
        }
        $uniqueVideoTypes = array_unique($videoTypes);
        $videoTypesMapped = array_map(static function ($videoType) {
            return [
                'name' => self::VIDEO_TYPES[$videoType] ?? $videoType,
                'value' => $videoType
            ];
        }, $uniqueVideoTypes);

        $allManufacturers = [];
        foreach ($videos as $element) {
            if (property_exists($element, 'manufacturers') && is_array($element->manufacturers)) {
                foreach ($element->manufacturers as $manufacturer) {
                    if (!empty($manufacturer)) {
                        $allManufacturers[] = $manufacturer;
                    }
                }
            }
        }

        $uniqueManufacturers = array_unique($allManufacturers);
        sort($uniqueManufacturers);
        $reIndexedManufacturers = array_values($uniqueManufacturers);
        $page->setVideoTypes($videoTypesMapped);
        $page->setApplianceTypes($appliancesMapped);
        $page->setManufacturerTypes($reIndexedManufacturers);
        $page->getMetaInformation()->setMetaTitle('Unsere Videoanleitungen für die Reparatur Ihrer Haushaltsgeräte');
        $page->getMetaInformation()->setMetaDescription(
            'Finden Sie hier unsere Videoanleitungen für die Reparatur Ihrer Haushalts- und Küchengeräte  | Siemens, Bosch, Miele, AEG, Beko'
        );

        return $page;
    }}