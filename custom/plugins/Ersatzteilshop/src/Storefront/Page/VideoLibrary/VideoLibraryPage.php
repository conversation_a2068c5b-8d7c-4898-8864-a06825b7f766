<?php declare(strict_types=1);

namespace Ersatzteilshop\Storefront\Page\VideoLibrary;

use Ersatzteilshop\Core\Content\VideoLibrary\VideoLibraryEntity;
use Shopware\Core\Framework\DataAbstractionLayer\Search\EntitySearchResult;
use Shopware\Storefront\Page\Page;

/**
 * Class VideoLibraryPage
 * @package Ersatzteilshop\Storefront\Page\VideoLibrary
 */
class VideoLibraryPage extends Page
{
    /**
     * @var VideoLibraryEntity
     */
    private $videoLibraryEntity;

    /**
     * @var EntitySearchResult|null
     */
    protected ?EntitySearchResult $relatedVideos = null;

    /**
     * @return mixed
     */
    public function getVideoLibrary(): VideoLibraryEntity
    {
        return $this->videoLibraryEntity;
    }

    /**
     * @param mixed $videoLibraryEntity
     */
    public function setVideoLibrary(VideoLibraryEntity $videoLibraryEntity)
    {
        $this->videoLibraryEntity = $videoLibraryEntity;
    }

    public function getRelatedVideos(): ?EntitySearchResult
    {
        return $this->relatedVideos;
    }

    public function setRelatedVideos(?EntitySearchResult $relatedVideos): void
    {
        $this->relatedVideos = $relatedVideos;
    }
}