<?php declare(strict_types=1);

namespace Ersatzteilshop\Storefront\Controller;

use Ersatzteilshop\Core\Content\Appliance\SalesChannel\AbstractApplianceRoute;
use Ersatzteilshop\MessageQueue\Handler\ApplianceProductIdCachingHandler;
use Ersatzteilshop\MessageQueue\Message\ApplianceProductIdCachingMessage;
use Ersatzteilshop\Service\Appliance\ApplianceAggregatorInterface;
use Ersatzteilshop\Service\VisitedApplianceService;
use Ersatzteilshop\Service\ImageSearch\ApplianceAnalyzer;
use Ersatzteilshop\Service\ImageSearch\ImageSearchResult;
use Ersatzteilshop\Service\ImageSearch\ImageSearchAnalytics;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepositoryInterface;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\SalesChannelRequest;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Shopware\Storefront\Controller\StorefrontController;
use Ersatzteilshop\Core\Content\Appliance\ApplianceEntity;
use Shopware\Core\Framework\Routing\Annotation\RouteScope;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Storefront\Framework\Cache\Annotation\HttpCache;
use Ersatzteilshop\Storefront\Page\Appliance\AppliancePageLoader;
use Ersatzteilshop\Core\Content\Appliance\SalesChannel\AbstractApplianceProductRoute;


/**
 * @RouteScope(scopes={"storefront"})
 */
class ApplianceController extends StorefrontController
{

    public function __construct(
        protected AppliancePageLoader           $pageLoader,
        protected AbstractApplianceProductRoute $applianceProductRoute,
        protected AbstractApplianceRoute        $applianceRoute,
        protected VisitedApplianceService       $visitedApplianceService,
        private ApplianceAnalyzer               $applianceAnalyzer,
        private ImageSearchAnalytics            $imageSearchAnalytics,
        private ApplianceAggregatorInterface    $applianceAggregator
    )
    {
    }

    /**
     * @Route("/appliance/{applianceId}", name="frontend.appliance.page", options={"seo"="true"}, methods={"GET"})
     * @HttpCache()
     */
    public function detail(Request $request, SalesChannelContext $context): Response
    {
        $page = $this->pageLoader->load($request, $context);
        $appliance = $page->getAppliance();
        if (!$appliance->hasCachedProducts()) {
            $handler = $this->container->get(ApplianceProductIdCachingHandler::class);
            $handler->handle(new ApplianceProductIdCachingMessage(
                [$appliance->getId()],
                $context->getSalesChannel()->getId(),
                $context->getContext()
            ));

            $page = $this->pageLoader->load($request, $context);
        }

        return $this->renderStorefront(
            '@Ersatzteilshop/storefront/page/appliance/detail.html.twig',
            ['page' => $page]
        );
    }

    /**
     * @Route(
     *     "/appliance/{applianceId}/categories",
     *     name="frontend.appliance.categories",
     *     methods={"GET"},
     *     defaults={"XmlHttpRequest"=true}
     * )
     */
    public function loadCategories(Request $request, SalesChannelContext $salesChannelContext): Response
    {
        if($request->get('html')) {
            return $this->renderStorefront(
                '@Ersatzteilshop/storefront/page/appliance/components/product/categories.html.twig',
                [
                    'categories' => $this->pageLoader->loadCategories($request, $salesChannelContext),
                    'applianceId' => $request->get('applianceId'),
                    'containerId' => $request->get('containerId'),
                ]
            );
        }

        $salesChannelContext->addState('ResponseJson');

        return $this->json([
            'categories' => $this->pageLoader->loadCategories($request, $salesChannelContext),
        ]);
    }

    /**
     * @Route(
     *     "/appliance/{applianceId}/product-total",
     *     name="frontend.appliance.product-total",
     *     methods={"GET"},
     *     defaults={"XmlHttpRequest"=true}
     * )
     * @HttpCache()
     */
    public function loadProductCount(Request $request, SalesChannelContext $salesChannelContext): Response
    {
        return $this->json([
            'total' => $this->pageLoader->getProductTotal($request, $salesChannelContext),
        ]);
    }

    /**
     * @Route(
     *     "/appliance/{applianceId}/products",
     *     name="frontend.appliance.products",
     *     methods={"GET"},
     *     defaults={"XmlHttpRequest"=true}
     * )
     * @HttpCache()
     */
    public function loadProduct(Request $request, SalesChannelContext $context): Response
    {
        return $this->renderStorefront(
            '@Ersatzteilshop/storefront/page/appliance/products.html.twig',
            [
                'page'  => $this->pageLoader->loadProducts($request, $context),
                'query' => $request->get('query'),
            ]
        );
    }

    /**
     * @Route(
     *     "/appliance/product/{productId}/manfacturers",
     *     name="frontend.appliance.product.manfacturers",
     *     methods={"GET"},
     *     defaults={"XmlHttpRequest"=true}
     * )
     * @HttpCache()
     */
    public function manfacturersFromProduct(string $productId, SalesChannelContext $context): Response
    {
        return $this->renderStorefront(
            '@Ersatzteilshop/storefront/page/product-detail/off-canvas/appliance-table.html.twig',
            ['manufacturers' => $this->applianceProductRoute->loadManufacturersFromProduct($productId, $context)]
        );
    }

    /**
     * @Route(
     *     "/appliance/product/{productId}",
     *     name="frontend.appliance.product",
     *     methods={"GET"},
     *     defaults={"XmlHttpRequest"=true},
     *     format="json"
     * )
     * @HttpCache()
     */
    public function fromProduct(string $productId, Request $request, SalesChannelContext $context): Response
    {
        $router = $this->container->get('router');

        $appliances = $this->applianceProductRoute->loadFromProductWithPagination(
            $productId,
            $request,
            $context
        );

        $saleChannelId = $context->getSalesChannel()->getId();

        return $this->json(array_values($appliances->map(static function (ApplianceEntity $appliance) use ($request, $saleChannelId, $router) {
            if ($appliance->getSeoUrls()
                && $seoUrl = $appliance->getSeoUrls()->filterBySalesChannelId($saleChannelId)->first()) {
                $seoUrl = $seoUrl->getSeoUrl();
            } else {
                $seoUrl = $router->generate('frontend.appliance.page', ['applianceId' => $appliance->getId()]);
            }

            return [
                'manufacturer' => $appliance->getManufacturer()->getTranslated()['name'],
                'modelName' => $appliance->getModelName(),
                'modelCode' => $appliance->getModelCode(),
                'period' => $appliance->getPeriod(),
                'seoUrl' => sprintf(
                    '%s/%s',
                    $request->attributes->get(SalesChannelRequest::ATTRIBUTE_STOREFRONT_URL),
                    $seoUrl
                )
            ];
        })));
    }

    /**
     * @Route(
     *     "/ajax/appliance/product/{productId}.json",
     *     name="frontend.appliance.product.json",
     *     methods={"GET"},
     *     defaults={"XmlHttpRequest"=true},
     *     format="json"
     * )
     * @HttpCache()
     */
    public function fromProductAll(string $productId, SalesChannelContext $context): Response
    {
        return $this->json($this->applianceProductRoute->loadAllFromProduct(
            $productId,
            $context,
        ));
    }

    /**
     * @Route(
     *     "/appliance-input-search-old/",
     *     name="frontend.appliance.input.search-old",
     *     options={"seo"="false"},
     *     methods={"POST"},
     *     defaults={"csrf_protected"=false, "XmlHttpRequest"=true}
     * )
     */
    public function searchApplianceByInputOld(Request $request, SalesChannelContext $context): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        $searchQuery = $data['searchQuery'] ?? '';
        $searchTerm = $data['searchTerm'] ?? '';
        $productId = $data['productId'] ?? null;
        $loadProducts = $productId !== null;
        $appliances = $this->applianceRoute->lookupAppliancesByInput($searchQuery, $context, $loadProducts);
        if ($appliances === null || $appliances->getTotal() === 0) {
            return new JsonResponse(['success' => false]);
        }

        $templateData = [
            'appliances' => $appliances,
            'searchTerm' => $searchTerm,
            'searchQuery' => $searchQuery,
        ];
        if ($loadProducts) {
            $templateData['applianceFits'] = $this->checkApplianceFits($appliances, $productId);
            $templateData['productId'] = $productId;
            $template = '@Ersatzteilshop/storefront/page/product-detail/template/appliance-search-widget-results.html.twig';
        } else {
            $template = '@Ersatzteilshop/storefront/page/product-detail/special-dummy-product/appliance-listing.html.twig';
        }

        $html = $this->renderView($template, $templateData);

        return new JsonResponse([
            'success' => true,
            'html' => $html,
        ]);
    }

    /**
     * @Route(
     *     "/appliance-input-search/",
     *     name="frontend.appliance.input.search",
     *     options={"seo"="false"},
     *     methods={"POST"},
     *     defaults={"csrf_protected"=false, "XmlHttpRequest"=true}
     * )
     */
    public function searchApplianceByInput(Request $request, SalesChannelContext $context): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        $searchQuery = $data['searchQuery'] ?? '';
        $searchTerm  = $data['searchTerm']  ?? '';
        $productId   = $data['productId']   ?? null;
        $loadProducts = ($productId !== null);

        $request->query->set('search', $searchQuery);
        $request->query->set('limit', $data['limit'] ?? 15);
        $request->query->set('p',     $data['page']  ?? 1);

        $appliances = $this->applianceAggregator->searchByInput($request, $context);

        if ($appliances === null || $appliances->getTotal() === 0) {
            return new JsonResponse(['success' => false]);
        }

        $templateData = [
            'appliances' => $appliances,
            'searchTerm' => $searchTerm,
            'searchQuery' => $searchQuery,
        ];

        if ($loadProducts) {
            $templateData['productId'] = $productId;
            $template = '@Ersatzteilshop/storefront/page/product-detail/template/appliance-search-widget-results.html.twig';
        } else {
            $template = '@Ersatzteilshop/storefront/page/product-detail/special-dummy-product/appliance-listing.html.twig';
        }

        $html = $this->renderView($template, $templateData);

        return new JsonResponse([
            'success' => true,
            'html'    => $html,
        ]);
    }


    /**
     * @Route(
     *     "/appliance-check-compatibility/",
     *     name="frontend.appliance.check.compatibility",
     *     options={"seo"="false"},
     *     methods={"POST"},
     *     defaults={"csrf_protected"=false, "XmlHttpRequest"=true}
     * )
     */
    public function checkCompatibility(Request $request, SalesChannelContext $context): JsonResponse
    {
        $fits = $this->applianceAggregator->checkCompatible($request, $context);
        return new JsonResponse(['success' => true, 'fits' => $fits]);
    }

    /**
     * @Route(
     *     "/store-appliance-id",
     *     name="frontend.appliance.id.store",
     *     options={"seo"="false"},
     *     methods={"POST"},
     *     defaults={"csrf_protected"=false, "XmlHttpRequest"=true}
     * )
     */
    public function storeApplianceId(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        $applianceId = $data['applianceId'] ?? null;

        if (!$applianceId) {
            return new JsonResponse(['success' => false, 'message' => 'No appliance ID provided.'], 400);
        }

        $this->visitedApplianceService->storeApplianceId($applianceId);

        return new JsonResponse(['success' => true]);
    }

    /**
     * @Route(
     *     "/appliance-image-search/",
     *     name="frontend.appliance.image.search",
     *     defaults={
     *         "XmlHttpRequest"=true,
     *         "_httpCache"=false
     *     },
     *     methods={"POST"}
     * )
     */
    public function searchFromImage(Request $request, SalesChannelContext $context): JsonResponse
    {
        try {
            $content = json_decode($request->getContent(), true);

            if (!isset($content['imageData'])) {
                throw new \InvalidArgumentException('No image data provided');
            }

            $fullImageData = 'data:image/jpeg;base64,' . $content['imageData'];
            $request->getSession()->set('uploaded_search_image', $fullImageData);

            // Start AI tracking
            $this->imageSearchAnalytics->startTracking('ai');
            $modelGuess = $this->applianceAnalyzer->extractModelGuesses($content['imageData']);
            $this->imageSearchAnalytics->endTracking('ai');

            // Start lookup tracking
            $this->imageSearchAnalytics->startTracking('lookup');
            $result = $this->applianceAnalyzer->searchWithModelGuesses($modelGuess, $context);
            $this->imageSearchAnalytics->endTracking('lookup');

            $this->imageSearchAnalytics->trackSearch(
                $result->getType(),
                $modelGuess,
                $context,
                null,
                $fullImageData,
                $result->getApplianceId()
            );

            $searchTerm = $modelGuess;
            if (strpos($modelGuess, 'NO_VALID_MODEL_NUMBER') !== false ||
                strpos($modelGuess, 'INVALID_IMAGE_TYPE') !== false) {
                $searchTerm = 'geräte';
            }

            switch ($result->getType()) {
                case ImageSearchResult::TYPE_SINGLE_MATCH:
                    return new JsonResponse([
                        'success'      => true,
                        'modelGuesses' => $modelGuess,
                        'redirect'     => $this->generateUrl('frontend.appliance.page', [
                            'applianceId' => $result->getSingleAppliance()->getId(),
                        ]),
                    ]);

                case ImageSearchResult::TYPE_MULTIPLE_MATCHES:
                    return new JsonResponse([
                        'success'          => true,
                        'modelGuesses'     => $modelGuess,
                        'multiple_matches' => true,
                        'redirect'         => $this->generateUrl('frontend.search.page', [
                            'search' => $result->getMatchedGuess(),
                        ]),
                    ]);

                default:
                    return new JsonResponse([
                        'success'     => true,
                        'modelGuesses'=> $modelGuess,
                        'no_matches'  => true,
                        'redirect'    => $this->generateUrl('frontend.search.page', [
                            'search' => $searchTerm,
                        ]),
                    ]);
            }
        } catch (\Exception $e) {
            $this->imageSearchAnalytics->trackSearch(
                'ERROR',
                null,
                $context,
                $e instanceof \InvalidArgumentException ? 'VALIDATION_ERROR' : 'BACKEND_ERROR',
                $fullImageData
            );

            return new JsonResponse([
                'success' => false,
                'error'   => $e->getMessage(),
                'redirect'=> $this->generateUrl('frontend.search.page', [
                    'search' => 'geräte',
                    'error'  => 'NO_VALID_MODEL_NUMBER',
                ]),
            ]);
        }
    }

    /**
     * @Route(
     *     "/appliance-image-search/track-failure",
     *     name="frontend.appliance.image.track_failure",
     *     defaults={
     *         "XmlHttpRequest"=true,
     *         "_httpCache"=false,
     *         "csrf_protected"=false
     *     },
     *     methods={"POST"}
     * )
     */
    public function trackImageSearchFailure(Request $request, SalesChannelContext $context): JsonResponse
    {
        try {
            $content = json_decode($request->getContent(), true);

            if (!isset($content['failureType'])) {
                throw new \InvalidArgumentException('No failure type provided');
            }

            $imageData = $request->getSession()->get('uploaded_search_image');

            $this->imageSearchAnalytics->trackSearch(
                'ERROR',
                null,
                $context,
                $content['failureType'],
                $imageData
            );

            return new JsonResponse(['success' => true]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error'   => $e->getMessage()
            ], 400);
        }
    }

    /**
     * @Route("/generate-token", name="frontend.appliance.csrf.generate", defaults={"csrf_protected"=false, "XmlHttpRequest"=true}, methods={"POST"})
     */
    public function generateToken(Request $request): JsonResponse
    {
        $content = json_decode($request->getContent(), true);
        $intent = $content['intent'] ?? 'frontend.appliance.image.extract';

        return new JsonResponse([
            'token' => $this->container->get('security.csrf.token_manager')
                ->getToken($intent)
                ->getValue()
        ]);
    }

    /**
     * @Route("/clear-search-image", name="frontend.appliance.clear.search.image", defaults={"csrf_protected"=false, "XmlHttpRequest"=true})
     */
    public function clearSearchImage(Request $request): JsonResponse
    {
        $request->getSession()->remove('uploaded_search_image');
        return new JsonResponse(['success' => true]);
    }
}
