<?php declare(strict_types=1);

namespace Ersatzteilshop\Storefront\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Shopware\Storefront\Controller\StorefrontController;
use Shopware\Core\Framework\Routing\Annotation\RouteScope;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Ersatzteilshop\Storefront\Page\VideoLibrary\VideoLibraryPageLoader;

/**
 * @RouteScope(scopes={"storefront"})
 */
class VideosController extends StorefrontController
{
    public function __construct(
        private VideoLibraryPageLoader $pageLoader
    ) {
    }

    /**
     * @Route(
     *     "/videos/uebersicht",
     *     name="frontend.video.library.overview",
     *     options={"seo"="false"},
     *     methods={"GET"}
     * )
     */
    public function overview(Request $request, SalesChannelContext $context): Response
    {
        return $this->renderStorefront(
            '@Storefront/storefront/page/video-library/overview.html.twig',
            ['page' => $this->pageLoader->loadOverview($request, $context)]
        );
    }


    /**
     * @Route(
     *     "/videos/{videoLibraryId}",
     *     name="frontend.video.library.page",
     *     options={"seo"="true"},
     *     methods={"GET"}
     * )
     */
    public function detail(Request $request, SalesChannelContext $context): Response
    {
        $page = $this->pageLoader->load($request, $context);
        $video = $page->getVideoLibrary();

        if ($video->getCmsPage() !== null) {
            return $this->renderStorefront(
                '@Ersatzteilshop/storefront/page/video-library/detail-cms.html.twig',
                [
                    'page' => $page,
                    'video' => $video
                ]
            );
        }

        return $this->renderStorefront(
            '@Ersatzteilshop/storefront/page/video-library/detail.html.twig',
            [
                'page' => $page,
                'video' => $video
            ]
        );
    }
}
