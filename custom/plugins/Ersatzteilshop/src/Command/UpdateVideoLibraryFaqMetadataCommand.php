<?php declare(strict_types=1);

namespace Ersatzteilshop\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepositoryInterface;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Ersatzteilshop\Service\ChatGPT4Client;

class UpdateVideoLibraryFaqMetadataCommand extends Command
{
    protected static $defaultName = 'eshop:library:generate-faq-markup';

    private EntityRepositoryInterface $videoLibraryRepository;
    private ChatGPT4Client $chatGptClient;

    private const FAQ_TRANSLATION_FIELD = 'faqMetadata';
    private const MIN_DESCRIPTION_LENGTH = 100;

    public function __construct(
        EntityRepositoryInterface $videoLibraryRepository,
        ChatGPT4Client $chatGptClient
    ) {
        parent::__construct();
        $this->videoLibraryRepository = $videoLibraryRepository;
        $this->chatGptClient = $chatGptClient;
    }

    protected function configure(): void
    {
        $this->setDescription('Generates FAQ Schema.org markup data for library entries without CMS pages using AI.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Generating FAQ data for library entries');

        $context = Context::createDefaultContext();
        $defaultLanguageId = $this->getDefaultLanguageId();

        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('cmsPageId', null));

        $libraryEntries = $this->videoLibraryRepository->search($criteria, $context);

        if ($libraryEntries->count() === 0) {
            $io->info('No library entries found without an associated CMS page to process.');
            return Command::SUCCESS;
        }

        $io->progressStart($libraryEntries->count());
        $updated = 0;
        $skipped = 0;
        $errors = [];

        foreach ($libraryEntries as $entry) {
            $description = $entry->getDescription();
            $title = $entry->getTitle() ?? 'Videobeitrag';

            if ($description === null) {
                $io->progressAdvance();
                $skipped++;
                $errors[] = "Skipped entry {$entry->getId()}: Description is null (check default translation).";
                continue;
            }

            $cleanDescription = strip_tags($description);

            if (empty($cleanDescription) || strlen($cleanDescription) < self::MIN_DESCRIPTION_LENGTH) {
                $io->progressAdvance();
                $skipped++;
                $errors[] = "Skipped entry {$entry->getId()}: Description too short or empty.";
                continue;
            }

            try {
                $faqData = $this->generateFaqDataFromDescription($title, $cleanDescription);

                if ($faqData === null || empty($faqData)) {
                    $io->progressAdvance();
                    $skipped++;
                    $errors[] = "Skipped entry {$entry->getId()}: AI failed to generate valid FAQ data or returned empty.";
                    continue;
                }

                $updatePayload = [
                    'id' => $entry->getId(),
                    'translations' => [
                        [
                            'languageId' => $defaultLanguageId,
                            self::FAQ_TRANSLATION_FIELD => $faqData
                        ]
                    ]
                ];
                $this->videoLibraryRepository->update([$updatePayload], $context);
                $updated++;

            } catch (\Exception $e) {
                $errors[] = "Error processing entry {$entry->getId()}: {$e->getMessage()}";
                $skipped++;
            }

            $io->progressAdvance();
            usleep(200000);
        }

        $io->progressFinish();

        if (count($errors) > 0) {
            $io->section('Errors & Skipped Reasons');
            $io->listing($errors);
        }

        $io->success("Generated FAQ data for $updated entries, skipped $skipped entries");

        return Command::SUCCESS;
    }

    private function getDefaultLanguageId(): string
    {
        return '2fbb5fe2e29a4d70aa5854ce7ce3e20b';
    }

    private function generateFaqDataFromDescription(string $title, string $description): ?array
    {
        $prompt = <<<EOT
Du bist ein Experte für die Erstellung von FAQ-Listen aus Texten. Deine Aufgabe ist es, den folgenden Text zu analysieren und eine Liste von häufig gestellten Fragen (FAQs) und den entsprechenden Antworten zu erstellen, die *direkt* aus den Informationen im Text abgeleitet sind.

Titel des Beitrags: "$title"

Textinhalt:
"""
$description
"""

Regeln für die FAQ-Erstellung:
1. Identifiziere relevante Fragen, die typischerweise zu diesem Thema gestellt werden könnten und deren Antworten klar im Text zu finden sind.
2. Formuliere die Antworten prägnant und basierend *ausschließlich* auf den Informationen im bereitgestellten Text. Füge keine externen Informationen hinzu.
3. Erstelle mindestens 3, aber nicht mehr als 7 FAQ-Paare, falls genügend relevante Informationen im Text vorhanden sind. Wenn weniger als 3 sinnvolle Paare gefunden werden können, gib ein leeres JSON-Array zurück.
4. Die Fragen und Antworten müssen auf Deutsch sein.
5. Gib das Ergebnis als JSON-Array zurück. Jedes Element im Array muss ein JSON-Objekt mit genau zwei Schlüsseln sein: "question" (string) und "answer" (string).
6. Stelle sicher, dass das JSON gültig ist und keine zusätzlichen Zeichen oder Text vor oder nach dem JSON-Array stehen.
7. Gib NUR das JSON-Array zurück.

Beispiel für das gewünschte Format:
[
  {
    "question": "Wie repariere ich X?",
    "answer": "Die Reparatur von X erfolgt durch..."
  },
  {
    "question": "Welche Werkzeuge brauche ich?",
    "answer": "Sie benötigen Werkzeug A und B."
  }
]
EOT;

        try {
            $rawResponse = $this->chatGptClient->getResponse($prompt);
            $cleanedResponse = preg_replace('/^```json\s*|```\s*$/', '', $rawResponse);
            $cleanedResponse = trim($cleanedResponse);
            $faqArray = json_decode($cleanedResponse, true, 512, JSON_THROW_ON_ERROR);

            if (!is_array($faqArray)) {
                return null;
            }

            foreach ($faqArray as $item) {
                if (!is_array($item) || !isset($item['question']) || !isset($item['answer']) || !is_string($item['question']) || !is_string($item['answer'])) {
                    return null;
                }
            }

            return $faqArray;

        } catch (\JsonException $e) {
            return null;
        } catch (\Exception $e) {
            return null;
        }
    }
}