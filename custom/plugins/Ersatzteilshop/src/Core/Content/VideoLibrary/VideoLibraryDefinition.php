<?php declare(strict_types=1);

namespace Ersatzteilshop\Core\Content\VideoLibrary;

use Ersatzteilshop\Core\Content\VideoLibrary\Aggregate\VideoLibraryTranslation\VideoLibraryTranslationDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\EntityDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\Field\CustomFields;
use Shopware\Core\Framework\DataAbstractionLayer\Field\DateField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\PrimaryKey;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\Required;
use Shopware\Core\Framework\DataAbstractionLayer\Field\IdField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\StringField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\TranslatedField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\TranslationsAssociationField;
use Shopware\Core\Framework\DataAbstractionLayer\FieldCollection;
use Shopware\Core\Content\Cms\CmsPageDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\Field\FkField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\ReferenceVersionField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\ManyToOneAssociationField;

class VideoLibraryDefinition extends EntityDefinition
{
    public const ENTITY_NAME = 'video_library';

    public function getEntityName(): string
    {
        return self::ENTITY_NAME;
    }

    public function getEntityClass(): string
    {
        return VideoLibraryEntity::class;
    }

    public function getCollectionClass(): string
    {
        return VideoLibraryCollection::class;
    }

    protected function defineFields(): FieldCollection
    {
        return new FieldCollection([
            (new IdField('id', 'id'))->addFlags(new Required(), new PrimaryKey()),
            (new FkField('cms_page_id', 'cmsPageId', CmsPageDefinition::class)),
            (new ReferenceVersionField(CmsPageDefinition::class, 'cms_page_version_id')),
            new ManyToOneAssociationField('cmsPage', 'cms_page_id', CmsPageDefinition::class, 'id', false),
            new TranslatedField('title'),
            new TranslatedField('url'),
            new TranslatedField('description'),
            new TranslatedField('appliances'),
            new TranslatedField('videoType'),
            new TranslatedField('tags'),
            new TranslatedField('hide'),
            new TranslatedField('manufacturers'),
            new DateField('fake_updated_at', 'fakeUpdatedAt'),
            new TranslatedField('metaTitle'),
            new TranslatedField('metaDescription'),
            new TranslatedField('seoShortDescription'),
            new TranslatedField('questionInput'),
            new CustomFields(),
            new TranslationsAssociationField(VideoLibraryTranslationDefinition::class, 'video_library_id'),
        ]);
    }
}