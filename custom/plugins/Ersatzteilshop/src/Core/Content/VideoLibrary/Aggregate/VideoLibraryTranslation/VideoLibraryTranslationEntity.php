<?php declare(strict_types=1);

namespace Ersatzteilshop\Core\Content\VideoLibrary\Aggregate\VideoLibraryTranslation;

use Ersatzteilshop\Core\Content\VideoLibrary\VideoLibraryEntity;
use Shopware\Core\Framework\DataAbstractionLayer\TranslationEntity;

class VideoLibraryTranslationEntity extends TranslationEntity
{
    /**
     * @var string
     */
    protected $videoLibraryId;

    /**
     * @var string
     */
    protected $name;

    /**
     * @var string
     */
    protected $title;

    /**
     * @var string|null
     */
    protected $url;

    /**
     * @var string
     */
    protected $description;


    /**
     * @var array|null
     */
    protected $appliances;

    /**
     * @var string
     */
    protected $videoType;

    /**
     * @var array|null
     */
    protected $tags;

    /**
     * @var bool
     */
    protected $hide;

    /**
     * @var array|null
     */
    protected $manufacturers;

    /**
     * @var VideoLibraryEntity
     */
    protected $videoLibrary;

    /**
     * @var string
     */
    protected $metaTitle;

    /**
     * @var string
     */
    protected $metaDescription;

    /**
     * @var string|null
     */
    protected $seoShortDescription;

    /**
     * @var string|null
     */
    protected $questionInput;

    /**
     * @var array|null
     */
    protected $youtubeMetadata;

    /**
     * @var array|null
     */
    protected $faqMetadata;

    /**
     * @return VideoLibraryEntity
     */
    public function getVideoLibrary(): VideoLibraryEntity
    {
        return $this->videoLibrary;
    }

    /**
     * @param VideoLibraryEntity $videoLibrary
     */
    public function setVideoLibrary(VideoLibraryEntity $videoLibrary): void
    {
        $this->videoLibrary = $videoLibrary;
    }

    /**
     * @return string
     */
    public function getVideoLibraryId(): string
    {
        return $this->videoLibraryId;
    }

    /**
     * @param string $videoLibraryId
     */
    public function setVideoLibraryId(string $videoLibraryId): void
    {
        $this->videoLibraryId = $videoLibraryId;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @param string $name
     */
    public function setName(string $name): void
    {
        $this->name = $name;
    }

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return $this->title;
    }

    /**
     * @param string $title
     */
    public function setTitle(string $title): void
    {
        $this->title = $title;
    }

    /**
     * @return string|null
     */
    public function getUrl(): ?string
    {
        return $this->url;
    }

    /**
     * @param string|null $url
     */
    public function setUrl(?string $url): void
    {
        $this->url = $url;
    }

    /**
     * @return string
     */
    public function getDescription(): string
    {
        return $this->description;
    }

    /**
     * @param string $description
     */
    public function setDescription(string $description): void
    {
        $this->description = $description;
    }

    /**
     * @return array|null
     */
    public function getAppliances(): ?array
    {
        return $this->appliances;
    }

    /**
     * @param array|null $appliances
     */
    public function setAppliances(?array $appliances): void
    {
        $this->appliances = $appliances;
    }

    /**
     * @return string
     */
    public function getVideoType(): string
    {
        return $this->videoType;
    }

    /**
     * @param string $videoType
     */
    public function setVideoType(string $videoType): void
    {
        $this->videoType = $videoType;
    }

    /**
     * @return array|null
     */
    public function getTags(): ?array
    {
        return $this->tags;
    }

    /**
     * @param array|null $tags
     */
    public function setTags(?array $tags): void
    {
        $this->tags = $tags;
    }

    /**
     * @return bool
     */
    public function getHide(): bool
    {
        return $this->hide;
    }

    /**
     * @param bool $hide
     */
    public function setHide(bool $hide): void
    {
        $this->hide = $hide;
    }

    /**
     * @return array|null
     */
    public function getManufacturers(): ?array
    {
        return $this->manufacturers;
    }

    /**
     * @param array|null $manufacturers
     */
    public function setManufacturers(?array $manufacturers): void
    {
        $this->manufacturers = $manufacturers;
    }

    /**
     * @return string
     */
    public function getMetaTitle(): string
    {
        return $this->metaTitle;
    }

    /**
     * @param string $metaTitle
     */
    public function setMetaTitle(string $metaTitle): void
    {
        $this->metaTitle = $metaTitle;
    }

    /**
     * @return string
     */
    public function getMetaDescription(): string
    {
        return $this->metaDescription;
    }

    /**
     * @param string $metaDescription
     */
    public function setSeoDescription(string $metaDescription): void
    {
        $this->metaDescription = $metaDescription;
    }

    /**
     * @return string|null
     */
    public function getSeoShortDescription(): ?string
    {
        return $this->seoShortDescription;
    }

    /**
     * @param string|null $seoShortDescription
     */
    public function setSeoShortDescription(?string $seoShortDescription): void
    {
        $this->seoShortDescription = $seoShortDescription;
    }

    /**
     * @return string|null
     */
    public function getQuestionInput(): ?string
    {
        return $this->questionInput;
    }

    /**
     * @param string|null $questionInput
     */
    public function setQuestionInput(?string $questionInput): void
    {
        $this->questionInput = $questionInput;
    }

    /**
     * @return array|null
     */
    public function getYoutubeMetadata(): ?array
    {
        return $this->youtubeMetadata;
    }

    /**
     * @param array|null $youtubeMetadata
     */
    public function setYoutubeMetadata(?array $youtubeMetadata): void
    {
        $this->youtubeMetadata = $youtubeMetadata;
    }

    /**
     * @return array|null
     */
    public function getFaqMetadata(): ?array
    {
        return $this->faqMetadata;
    }

    /**
     * @param array|null $faqMetadata
     */
    public function setFaqMetadata(?array $faqMetadata): void
    {
        $this->faqMetadata = $faqMetadata;
    }
}
