<?php

namespace Ersatzteilshop\Core\Content\VideoLibrary\Aggregate\VideoLibraryTranslation;

use Ersatzteilshop\Core\Content\VideoLibrary\VideoLibraryDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\EntityTranslationDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\Field\BoolField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\AllowHtml;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\Required;
use Shopware\Core\Framework\DataAbstractionLayer\Field\JsonField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\LongTextField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\StringField;
use Shopware\Core\Framework\DataAbstractionLayer\FieldCollection;

class VideoLibraryTranslationDefinition extends EntityTranslationDefinition
{
    public const ENTITY_NAME = 'video_library_translation';

    public function getEntityName(): string
    {
        return self::ENTITY_NAME;
    }

    public function getCollectionClass(): string
    {
        return VideoLibraryTranslationCollection::class;
    }

    public function getEntityClass(): string
    {
        return VideoLibraryTranslationEntity::class;
    }

    protected function defineFields(): FieldCollection
    {
        return new FieldCollection(
            [
                (new StringField('title', 'title'))
                    ->addFlags(new Required()),

                (new StringField('url', 'url')),

                (new LongTextField('description', 'description'))
                    ->addFlags(new AllowHtml()),

                (new StringField('videoType', 'videoType'))
                    ->addFlags(new Required()),

                (new JsonField('appliances', 'appliances'))
                    ->addFlags(new Required()),

                (new JsonField('tags', 'tags')),

                (new BoolField('hide', 'hide')),

                (new JsonField('manufacturers', 'manufacturers'))
                    ->addFlags(new Required()),

                (new StringField('metaTitle', 'metaTitle'))
                    ->addFlags(new Required()),

                (new LongTextField('metaDescription', 'metaDescription'))
                    ->addFlags(new Required()),

                (new LongTextField('seoShortDescription', 'seoShortDescription'))
                    ->addFlags(new AllowHtml()),

                (new StringField('questionInput', 'questionInput'))
                    ->addFlags(new Required()),

                (new JsonField('youtube_metadata', 'youtubeMetadata'))
                    ->addFlags(new AllowHtml()),

                (new JsonField('faq_metadata', 'faqMetadata'))
                    ->addFlags(new AllowHtml())
            ]
        );
    }

    public function getParentDefinitionClass(): string
    {
        return VideoLibraryDefinition::class;
    }
}