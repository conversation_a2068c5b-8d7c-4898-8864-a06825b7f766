<?php

namespace Ersatzteilshop\Core\Content\VideoLibrary;

use DateTimeInterface;
use Shopware\Core\Framework\DataAbstractionLayer\Entity;
use Shopware\Core\Framework\DataAbstractionLayer\EntityIdTrait;
use Shopware\Core\Content\Cms\CmsPageEntity;

class VideoLibraryEntity extends Entity
{
    use EntityIdTrait;

    /**
     * @var string
     */
    protected $videoType;

    /**
     * @var string
     */
    protected $title;

    /**
     * @var string|null
     */
    protected $url;

    /**
     * @var string
     */
    protected $description;

    /**
     * @var DateTimeInterface|null
     */
    protected $fakeUpdatedAt;

    /**
     * @var string
     */
    protected $metaTitle;

    /**
     * @var string
     */
    protected $metaDescription;

    /**
     * @var string
     */
    protected $seoShortDescription;

    /**
     * @var string
     */
    protected $questionInput;

    /**
     * @var string|null
     */
    protected ?string $cmsPageId = null;

    /**
     * @var string|null
     */
    protected ?string $cmsPageVersionId = null;

    /**
     * @var CmsPageEntity|null
     */
    protected ?CmsPageEntity $cmsPage = null;

    /**
     * @var array|null
     */
    protected ?array $manufacturers = null;

    /**
     * @var array|null
     */
    protected ?array $appliances = null;

    /**
     * @var array|null
     */
    protected $customFields;

    /**
     * @return string
     */
    public function getVideoType(): string
    {
        return $this->videoType;
    }

    /**
     * @param string $videoType
     */
    public function setVideoType(string $videoType): void
    {
        $this->videoType = $videoType;
    }

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return $this->title;
    }

    /**
     * @param string $title
     */
    public function setTitle(string $title): void
    {
        $this->title = $title;
    }

    /**
     * @return string|null
     */
    public function getUrl(): ?string
    {
        return $this->url;
    }

    /**
     * @param string|null $url
     */
    public function setUrl(?string $url): void
    {
        $this->url = $url;
    }

    /**
     * @return string|null
     */
    public function getDescription(): ?string
    {
        return $this->description;
    }

    /**
     * @param string $description
     */
    public function setDescription(string $description): void
    {
        $this->description = $description;
    }

    public function getYoutubeId(): ?string
    {
        $translated = $this->getTranslated();
        $url = $translated['url'] ?? null;

        if (!$url) {
            return null;
        }

        preg_match("/^(?:http(?:s)?:\/\/)?(?:www\.)?(?:m\.)?(?:youtu\.be\/|youtube\.com\/(?:(?:watch)?\?(?:.*&)?v(?:i)?=|(?:embed|v|vi|user)\/))([^\?&\"'>]+)/", $url, $matches);
        return $matches[1] ?? null;
    }

    public function getPreviewImage(): ?string
    {
        $youtubeId = $this->getYoutubeId();
        return $youtubeId ? "https://i3.ytimg.com/vi/" . $youtubeId . "/hqdefault.jpg" : null;
    }

    /**
     * @return DateTimeInterface|null
     */
    public function getFakeUpdatedAt(): ?DateTimeInterface
    {
        return $this->fakeUpdatedAt;
    }

    /**
     * @param DateTimeInterface|null $fakeUpdatedAt
     */
    public function setFakeUpdatedAt(?DateTimeInterface $fakeUpdatedAt): void
    {
        $this->fakeUpdatedAt = $fakeUpdatedAt;
    }

    /**
     * @return string|null
     */
    public function getMetaTitle(): ?string
    {
        return $this->metaTitle;
    }

    /**
     * @param string $metaTitle
     */
    public function setMetaTitle(string $metaTitle): void
    {
        $this->metaTitle = $metaTitle;
    }

    /**
     * @return string|null
     */
    public function getMetaDescription(): ?string
    {
        return $this->metaDescription;
    }

    /**
     * @param string $metaDescription
     */
    public function setMetaDescription(string $metaDescription): void
    {
        $this->metaDescription = $metaDescription;
    }

    /**
     * @return string|null
     */
    public function getSeoShortDescription(): ?string
    {
        return $this->seoShortDescription;
    }

    /**
     * @param string $seoShortDescription
     */
    public function setSeoShortDescription(string $seoShortDescription): void
    {
        $this->seoShortDescription = $seoShortDescription;
    }

    /**
     * @return string|null
     */
    public function getQuestionInput(): ?string
    {
        return $this->questionInput;
    }

    /**
     * @param string $questionInput
     */
    public function setQuestionInput(string $questionInput): void
    {
        $this->questionInput = $questionInput;
    }

    /**
     * @return string|null
     */
    public function getCmsPageId(): ?string
    {
        return $this->cmsPageId;
    }

    /**
     * @param string|null $cmsPageId
     */
    public function setCmsPageId(?string $cmsPageId): void
    {
        $this->cmsPageId = $cmsPageId;
    }

    /**
     * @return string|null
     */
    public function getCmsPageVersionId(): ?string
    {
        return $this->cmsPageVersionId;
    }

    /**
     * @param string|null $cmsPageVersionId
     */
    public function setCmsPageVersionId(?string $cmsPageVersionId): void
    {
        $this->cmsPageVersionId = $cmsPageVersionId;
    }

    /**
     * @return CmsPageEntity|null
     */
    public function getCmsPage(): ?CmsPageEntity
    {
        return $this->cmsPage;
    }

    /**
     * @param CmsPageEntity|null $cmsPage
     */
    public function setCmsPage(?CmsPageEntity $cmsPage): void
    {
        $this->cmsPage = $cmsPage;
    }

    /**
     * @return array|null
     */
    public function getManufacturers(): ?array
    {
        $translated = $this->getTranslated();
        return $translated['manufacturers'] ?? null;
    }

    /**
     * @param array|null $manufacturers
     */
    public function setManufacturers(?array $manufacturers): void
    {
        $this->manufacturers = $manufacturers;
    }
    
    /**
     * @return array|null
     */
    public function getAppliances(): ?array
    {
        $translated = $this->getTranslated();
        return $translated['appliances'] ?? null;
    }

    /**
     * @param array|null $appliances
     */
    public function setAppliances(?array $appliances): void
    {
        $this->appliances = $appliances;
    }

   /**
     * @return array|null
     */
    public function getCustomFields(): ?array
    {
        return $this->customFields;
    }

    /**
     * @param array|null $customFields
     *
     * @return $this
     */
    public function setCustomFields(?array $customFields): self
    {
        $this->customFields = $customFields;
        return $this;
    }
}