import template from "./sw-cms-create-wizard.html.twig";

const { Component } = Shopware;

Component.override('sw-cms-create-wizard', {
    template,

    data() {
        const pageTypeNames = {
            page: this.$tc('sw-cms.detail.label.pageTypeShopPage'),
            landingpage: this.$tc('sw-cms.detail.label.pageTypeLandingpage'),
            product_list: this.$tc('sw-cms.detail.label.pageTypeCategory'),
            blog: this.$tc('sw-cms.detail.label.pageTypeBlog')
        };

        const pageTypeIcons = {
            page: 'default-object-lightbulb',
            landingpage: 'default-web-dashboard',
            product_list: 'default-shopping-basket',
            blog: 'default-web-dashboard'
        };

        if (this.feature.isActive('FEATURE_NEXT_10078')) {
            pageTypeNames.product_detail = this.$tc('sw-cms.detail.label.pageTypeProduct');
            pageTypeIcons.product_detail = 'default-action-tags';
        }

        return {
            step: 1,
            pageTypeNames,
            pageTypeIcons,
            steps: {
                pageType: 1,
                sectionType: 2,
                pageName: 3
            }
        };
    }
})
