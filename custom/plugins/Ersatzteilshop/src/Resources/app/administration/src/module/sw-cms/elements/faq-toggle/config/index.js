import template from './sw-cms-el-config-faq-toggle.html.twig';
import './sw-cms-el-config-faq-toggle.scss';

const { Mixin } = Shopware;
Shopware.Component.register('sw-cms-el-config-faq-toggle', {
    template,

    mixins: [
        Mixin.getByName('cms-element')
    ],

    data() {
        return {
            faqItems: []
        };
    },

    computed: {
        faqItemsData: {
            get() {
                try {
                    if (this.element.config.faqItems && this.element.config.faqItems.value) {
                        return JSON.parse(this.element.config.faqItems.value);
                    }
                } catch (e) {
                    console.error('Error parsing FAQ items:', e);
                }
                return [];
            },
            set(value) {
                this.element.config.faqItems.value = JSON.stringify(value);
                this.$emit('element-update', this.element);
            }
        }
    },

    created() {
        this.createdComponent();
    },

    methods: {
        createdComponent() {
            this.initElementConfig('faq-toggle');

            if (!this.element.config.headingFontSize) {
                this.element.config.headingFontSize = {
                    source: 'static',
                    value: '20'
                };
            }

            if (!this.element.config.heading) {
                this.element.config.heading = {
                    source: 'static',
                    value: 'Hier bitte die Überschrift angeben'
                };
            }

            if (!this.element.config.faqItems) {
                this.element.config.faqItems = {
                    source: 'static',
                    value: JSON.stringify([
                        {
                            question: "Warum backt mein Backofen ungleichmäßig?",
                            answer: "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>"
                        },
                        {
                            question: "Wie kann ich den Heißluftventilator überprüfen?",
                            answer: "<p>Ut enim ad minim veniam, quis nostrud exercitation.</p>"
                        }
                    ])
                };
            }
        },

        onBlur() {
            this.$emit('element-update', this.element);
        },

        addFaqItem() {
            const items = [...this.faqItemsData];
            items.push({
                question: "Neue Frage",
                answer: "<p>Neue Antwort</p>"
            });
            this.faqItemsData = items;
        },

        removeFaqItem(index) {
            const items = [...this.faqItemsData];
            items.splice(index, 1);
            this.faqItemsData = items;
        },

        updateFaqItem(index, field, value) {
            const items = [...this.faqItemsData];
            items[index][field] = value;
            this.faqItemsData = items;
            
            this.$emit('element-update', this.element);
        }
    }
});
