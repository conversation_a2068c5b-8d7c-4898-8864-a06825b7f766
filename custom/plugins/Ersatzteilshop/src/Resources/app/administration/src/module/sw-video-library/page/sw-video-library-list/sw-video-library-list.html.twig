{% block sw_video_library %}
    <sw-page class="sw-video-library">
        
        {% block sw_video_library_search_bar %}
            <template #search-bar>
                <sw-search-bar
                        :initialSearchType="$tc('sw-video-library.general.typeSearchBar')"
                        :placeholder="$tc('sw-video-library.general.placeholderSearchBar')"
                        :initialSearch="term"
                        @search="onSearch"
                >
                </sw-search-bar>
            </template>
        {% endblock %}

        {% block sw_video_library_sidebar %}
            <template #sidebar>
                <sw-sidebar class="sw-customer-list__sidebar sw-video-library__sidebar">
                    <sw-sidebar-item icon="default-action-filter" title="Filter">
                        <sw-single-select
                                v-model="cmsFilter"
                                :options="cmsOptions"
                                label="CMS vergeben/leer"
                                labelProperty="label"
                                valueProperty="value"
                                @change="onFilterChange">
                        </sw-single-select>
                        <sw-single-select
                                v-model="keywordFilter"
                                :options="keywordOptions"
                                label="Keyword vergeben/leer"
                                labelProperty="label"
                                valueProperty="value"
                                @change="onFilterChange">
                        </sw-single-select>
                        <sw-single-select
                                v-model="youtubeUrlFilter"
                                :options="youtubeUrlOptions"
                                label="Youtube URL vergeben/leer"
                                labelProperty="label"
                                valueProperty="value"
                                @change="onFilterChange">
                        </sw-single-select>
                        <sw-single-select
                                v-model="seoUrlUniqueFilter"
                                :options="seoUrlUniqueOptions"
                                label="SEO URL unique/nicht unique"
                                labelProperty="label"
                                valueProperty="value"
                                @change="onFilterChange">
                        </sw-single-select>
                    </sw-sidebar-item>
                </sw-sidebar>
            </template>
        {% endblock %}
        
        {% block sw_video_library_smart_bar_header %}
            <template #smart-bar-header>
                {% block sw_video_library_smart_bar_header_title %}
                    <h2>
                        {% block sw_video_library_smart_bar_header_title_text %}
                            {{ $tc('sw-video-library.general.menuItem') }}
                        {% endblock %}
                        
                        {% block sw_video_library_smart_bar_header_amount %}
                            <span v-if="!isLoading" class="sw-page__smart-bar-amount">
                                ({{ total }})
                            </span>
                        {% endblock %}
                    </h2>
                {% endblock %}
            </template>
        {% endblock %}
        
        {% block sw_video_library_smart_bar_actions %}
            <template #smart-bar-actions>
                <div class="sw-video-library__button-group">
                    <sw-button
                            :routerLink="{ name: 'sw.video.library.create' }"
                            variant="primary"
                    >
                        {{ $tc('sw-video-library.list.action.buttonAddVideo') }}
                    </sw-button>
                    
                    <sw-context-button>
                        <template #button>
                            <sw-button
                                    variant="primary"
                                    square
                                    :disabled="isLoading"
                            >
                                <sw-icon name="small-arrow-medium-down" size="16" />
                            </sw-button>
                        </template>
                        
                        <sw-context-menu-item @click="exportKeywords">
                            {{ $tc('sw-video-library.list.action.buttonExportData') }}
                        </sw-context-menu-item>
                        <sw-context-menu-item @click="onImportVolumeDataClick">
                            {{ $tc('sw-video-library.list.action.buttonImportData') }}
                        </sw-context-menu-item>
                    </sw-context-button>
                </div>
                
                <input
                        type="file"
                        ref="importFileInput"
                        style="display: none;"
                        accept=".csv"
                        @change="onImportVolumeDataFileChange"
                />
            </template>
        {% endblock %}
        
        {% block sw_video_library_language_switch %}
            <template #language-switch>
                <sw-language-switch @on-change="onChangeLanguage"></sw-language-switch>
            </template>
        {% endblock %}
        
        <template slot="content">
            {% block sw_video_library_content %}
                <sw-entity-listing
                        v-if="videos"
                        identifier="sw-video-library"
                        :items="videos"
                        :repository="repository"
                        :showSelection="false"
                        :columns="columns"
                        detailRoute="sw.video.library.detail"
                        :isLoading="isLoading"
                        @inline-edit-save="onInlineEditSave"
                        @update-records="updateTotal"
                >
                    <template #column-thumb="{ item }">
                        <img
                                height="40"
                                class="sw-video-library__thumb"
                                :src="getVideoThumb(item)"
                                :alt="item.url"
                        >
                    </template>
                    
                    {% block sw_video_library_empty_state %}
                        <sw-empty-state
                                v-if="!isLoading && !total"
                                :title="$tc('sw-video-library.list.messageEmpty')"
                        ></sw-empty-state>
                    {% endblock %}
                    
                    {% block sw_video_library_grid_loader %}
                        <sw-loader v-if="isLoading"></sw-loader>
                    {% endblock %}
                </sw-entity-listing>
            {% endblock %}
        </template>
    </sw-page>
{% endblock %}
