{"sw-cms": {"elements": {"entityMapping": {"label": "Entity Mapping", "config": {"labelEntityType": "Entity Type", "labelCategory": "Category", "labelProduct": "Product", "labelManufacturer": "Manufacturer", "labelCategoryManufacturer": "Category Manufacturer", "labelIdInput": "Add Entity IDs", "placeholderIdInput": "Enter ID and press Enter...", "labelSelectedItems": "Selected Items", "headerDisplaySettings": "Display Settings", "labelItemsPerRow": "Items per row", "labelDisplayMode": "Display mode", "displayModeGrid": "Grid", "displayModeStandard": "Standard", "labelBoxLayout": "Box Layout", "boxLayoutImageText": "Image & Text", "boxLayoutImageOnly": "Image Only", "boxLayoutTextOnly": "Text Only", "loadingText": "Loading...", "labelPredefinedCategories": "Predefined Categories"}, "preview": {"configNeeded": "Please configure", "itemCount": "{count} item(s) selected", "itemDetailPlaceholder": "Details for ID {id}", "noItemsSelected": "No items selected yet."}}}}}