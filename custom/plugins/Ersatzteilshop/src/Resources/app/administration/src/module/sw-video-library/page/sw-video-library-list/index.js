import template from "./sw-video-library-list.html.twig";
import "./sw-video-library-list.scss";

const { Component, Mixin, Context } = Shopware;
const { Criteria } = Shopware.Data;

function debounce(func, wait) {
    let timeout;
    return function(...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), wait);
    };
}

Component.register("sw-video-library-list", {
    template,

    inject: [
        "repositoryFactory"
    ],

    mixins: [
        Mixin.getByName("notification"),
        Mixin.getByName("listing"),
        Mixin.getByName("placeholder")
    ],

    data() {
        return {
            videos: null,
            isLoading: false,
            total: 0,
            sortBy: "createdAt",
            sortDirection: "DESC",
            naturalSorting: true,
            term: "",
            page: 1,
            limit: 100,
            cmsFilter: '',
            keywordFilter: '',
            youtubeUrlFilter: '',
            seoUrlUniqueFilter: '',
            seoUrlRepository: null,
            nonUniqueVideoIds: []
        };
    },

    computed: {
        repository() {
            return this.repositoryFactory.create("video_library");
        },

        cmsOptions() {
            return [
                { label: this.$tc('sw-video-library.filter.all'), value: '' },
                { label: this.$tc('sw-video-library.filter.cmsAssigned'), value: 'assigned' },
                { label: this.$tc('sw-video-library.filter.cmsEmpty'), value: 'empty' }
            ];
        },

        keywordOptions() {
            return [
                { label: this.$tc('sw-video-library.filter.all'), value: '' },
                { label: this.$tc('sw-video-library.filter.keywordAssigned'), value: 'assigned' },
                { label: this.$tc('sw-video-library.filter.keywordEmpty'), value: 'empty' }
            ];
        },

        youtubeUrlOptions() {
            return [
                { label: this.$tc('sw-video-library.filter.all'), value: '' },
                { label: this.$tc('sw-video-library.filter.urlAssigned'), value: 'assigned' },
                { label: this.$tc('sw-video-library.filter.urlEmpty'), value: 'empty' }
            ];
        },

        seoUrlUniqueOptions() {
            return [
                { label: this.$tc('sw-video-library.filter.all'), value: '' },
                { label: this.$tc('sw-video-library.filter.unique'), value: 'unique' },
                { label: this.$tc('sw-video-library.filter.notUnique'), value: 'notUnique' }
            ];
        },

        columns() {
            return [
                {
                    property: "thumb",
                    label: this.$tc("sw-video-library.list.columns.thumb"),
                    allowResize: true,
                    routerLink: "sw.video.library.detail",
                    sortable: false,
                },
                {
                    property: "title",
                    dataIndex: "title",
                    label: this.$tc("sw-video-library.list.columns.title"),
                    allowResize: true,
                    routerLink: "sw.video.library.detail",
                },
                {
                    property: "url",
                    dataIndex: "url",
                    label: this.$tc("sw-video-library.list.columns.url"),
                    allowResize: true,
                },

                {
                    property: "questionInput",
                    dataIndex: "questionInput",
                    label: this.$tc("sw-video-library.list.columns.questionInput"),
                    allowResize: true,
                    sortable: true
                },

                {
                    property: "appliance",
                    dataIndex: "appliance",
                    label: this.$tc("sw-video-library.list.columns.appliance"),
                    allowResize: true,
                }
            ];
        }
    },

    created() {
        this.seoUrlRepository = this.repositoryFactory.create('seo_url');
        this.debouncedOnFilterChange = debounce(() => {
            this.onFilterChange();
        }, 300);
        this.getList();
        this.findNonUniqueSeoUrls();
    },

    watch: {
        cmsFilter() {
            this.onFilterChange();
        },
        keywordFilter() {
            this.onFilterChange();
        },
        youtubeUrlFilter() {
            this.onFilterChange();
        },
        seoUrlUniqueFilter() {
            this.onFilterChange();
        }
    },

    methods: {
        findNonUniqueSeoUrls() {
            const criteria = new Criteria(1, 1000);
            criteria.addFilter(Criteria.equals('routeName', 'frontend.video.library.page'));
            criteria.addFilter(Criteria.equals('isCanonical', 1));
            
            this.seoUrlRepository.search(criteria, Context.api).then((seoUrls) => {
                const pathInfoBySalesChannel = {};
                
                seoUrls.forEach(url => {
                    if (url.seoPathInfo && url.salesChannelId) {
                        const key = `${url.seoPathInfo}|${url.salesChannelId}|${url.languageId}`;
                        if (!pathInfoBySalesChannel[key]) {
                            pathInfoBySalesChannel[key] = [];
                        }
                        pathInfoBySalesChannel[key].push(url.foreignKey);
                    }
                });
                
                const nonUniqueIds = [];
                Object.keys(pathInfoBySalesChannel).forEach(key => {
                    if (pathInfoBySalesChannel[key].length > 1) {
                        nonUniqueIds.push(...pathInfoBySalesChannel[key]);
                    }
                });
                
                this.nonUniqueVideoIds = [...new Set(nonUniqueIds)];
                console.log('Non-unique SEO URL video IDs:', this.nonUniqueVideoIds);
            });
        },

        getList() {
            this.isLoading = true;

            const criteria = new Criteria(this.page, this.limit);
            this.naturalSorting = (this.sortBy === "createdAt");
            criteria.setTerm(this.term);

            criteria.addSorting(
                Criteria.sort(this.sortBy, this.sortDirection, this.naturalSorting)
            );

            console.log('Current filters:', {
                cmsFilter: this.cmsFilter,
                keywordFilter: this.keywordFilter,
                youtubeUrlFilter: this.youtubeUrlFilter,
                seoUrlUniqueFilter: this.seoUrlUniqueFilter
            });

            if (this.cmsFilter === 'assigned') {
                criteria.addFilter(Criteria.not('AND', [
                    Criteria.equals('cmsPageId', null)
                ]));
            } else if (this.cmsFilter === 'empty') {
                criteria.addFilter(Criteria.equals('cmsPageId', null));
            }

            if (this.keywordFilter === 'assigned') {
                criteria.addFilter(Criteria.not('AND', [
                    Criteria.equals('customFields.ersatzteilshop_video_library_keyword', null)
                ]));
            } else if (this.keywordFilter === 'empty') {
                criteria.addFilter(Criteria.equals('customFields.ersatzteilshop_video_library_keyword', null));
            }

            if (this.youtubeUrlFilter === 'assigned') {
                criteria.addFilter(Criteria.not('AND', [
                    Criteria.equals('url', null)
                ]));
            } else if (this.youtubeUrlFilter === 'empty') {
                criteria.addFilter(Criteria.equals('url', null));
            }

            if (this.seoUrlUniqueFilter === 'unique') {
                if (this.nonUniqueVideoIds.length > 0) {
                    criteria.addFilter(Criteria.not('OR', [
                        Criteria.equalsAny('id', this.nonUniqueVideoIds)
                    ]));
                }
            } else if (this.seoUrlUniqueFilter === 'notUnique') {
                if (this.nonUniqueVideoIds.length > 0) {
                    criteria.addFilter(Criteria.equalsAny('id', this.nonUniqueVideoIds));
                }
            }

            console.log('Final criteria filters:', criteria.filters);

            this.repository
                .search(criteria, Shopware.Context.api)
                .then(result => {
                    this.videos = result;
                    this.total = result.total;
                    this.isLoading = false;
                })
                .catch(() => {
                    this.isLoading = false;
                });
        },

        onFilterChange() {
            this.page = 1;
            this.getList();
        },

        onSearch(value) {
            this.term = value;
            this.page = 1;
            this.getList();
        },

        updateTotal({ total }) {
            this.total = total;
        },

        onInlineEditSave(savePromise, video) {
            this.isLoading = true;
            const videoName = video.name || this.placeholder(video, "name");

            return savePromise
                .then(() => {
                    this.createNotificationSuccess({
                        title: this.$tc("sw-video-library.list.titleSaveSuccess"),
                        message: this.$tc(
                            "sw-video-library.list.messageSaveSuccess",
                            0,
                            { name: videoName }
                        )
                    });
                    this.isLoading = false;
                })
                .catch(() => {
                    this.getList();
                    this.createNotificationError({
                        title: this.$tc("global.default.error"),
                        message: this.$tc(
                            "global.notification.notificationSaveErrorMessage",
                            0,
                            { entityName: videoName }
                        )
                    });
                    this.isLoading = false;
                });
        },

        getVideoThumb(video) {
            if (!video?.url) {
                return null;
            }
            try {
                const url = new URL(video.url);
                const youtubeId = url.searchParams.get("v");
                return "https://i3.ytimg.com/vi/" + youtubeId + "/default.jpg";
            } catch {
                return "https://i3.ytimg.com/vi/";
            }
        },

        onChangeLanguage(/* languageId */) {
            this.getList();
        },

        exportKeywords() {
            const criteria = new Criteria(1, 10000);
            criteria.addFilter(
                Criteria.not("AND", [
                    Criteria.equals("customFields.ersatzteilshop_video_library_keyword", null)
                ])
            );

            this.isLoading = true;

            this.repository.search(criteria, Shopware.Context.api)
                .then((result) => {
                    this.isLoading = false;

                    if (!result.length) {
                        this.createNotificationError({
                            title: this.$tc("sw-video-library.detail.errorTitle"),
                            message: "No videos found with a keyword set."
                        });
                        return;
                    }

                    let csvContent = "Keyword\n";

                    result.forEach((video) => {
                        const keyword = video?.customFields?.ersatzteilshop_video_library_keyword ?? "";
                        csvContent += `${keyword}\n`;
                    });

                    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
                    const url = URL.createObjectURL(blob);

                    const link = document.createElement("a");
                    link.href = url;
                    link.setAttribute("download", "keywords.csv");
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                })
                .catch((error) => {
                    this.isLoading = false;
                    this.createNotificationError({
                        title: this.$tc("sw-video-library.detail.errorTitle"),
                        message: error.message
                    });
                });
        },

        onImportVolumeDataClick() {
            if (this.isLoading) {
                return;
            }
            this.$refs.importFileInput.click();
        },

        onImportVolumeDataFileChange(event) {
            const file = event.target.files[0];
            if (!file) {
                return;
            }
            event.target.value = "";

            this.isLoading = true;

            const reader = new FileReader();
            reader.onload = async (e) => {
                try {
                    await this.processImportCsv(e.target.result);
                    this.createNotificationSuccess({
                        title: "Import successful",
                        message: "Volume data was updated successfully."
                    });
                } catch (err) {
                    this.createNotificationError({
                        title: this.$tc("sw-video-library.detail.errorTitle"),
                        message: err.message
                    });
                } finally {
                    this.isLoading = false;
                    this.getList();
                }
            };

            reader.readAsText(file, "UTF-8");
        },

        async processImportCsv(csvString) {
            const allLines = csvString
                .split("\n")
                .map(line => line.trim())
                .filter(line => line.length > 0);

            const dataLines = allLines.slice(3);

            if (!dataLines.length) {
                throw new Error("No data lines found after skipping headers.");
            }

            for (const line of dataLines) {
                const columns = line.split("\t");
                if (columns.length < 4) {
                    continue;
                }

                const keyword = columns[0].trim();
                const volumeStr = columns[3].trim();
                if (!keyword || !volumeStr) {
                    continue;
                }

                const volumeFloat = parseFloat(volumeStr);
                if (isNaN(volumeFloat)) {
                    continue;
                }

                const volumeInt = Math.round(volumeFloat);

                const matchingCriteria = new Criteria(1, 1);
                matchingCriteria.addFilter(
                    Criteria.equals("customFields.ersatzteilshop_video_library_keyword", keyword)
                );

                const searchResult = await this.repository.search(matchingCriteria, Shopware.Context.api);
                if (!searchResult.length) {
                    continue;
                }

                const entity = searchResult[0];
                if (!entity.customFields) {
                    entity.customFields = {};
                }
                entity.customFields.ersatzteilshop_video_library_volume = volumeInt;

                await this.repository.save(entity, Shopware.Context.api);
            }
        }

    }
});
