import template from './sw-cms-el-config-video-library-component.html.twig';
import './sw-cms-el-config-video-library-component.scss';

const { Component, Mixin } = Shopware;
const { Criteria, EntityCollection } = Shopware.Data;

Component.register('sw-cms-el-config-video-library-component', {
    template,

    inject: ['repositoryFactory'],

    mixins: [
        Mixin.getByName('cms-element')
    ],

    data() {
        return {
            manufacturerOptions: [
                { value: "Alle", label: "Alle" },
                { value: "AEG", label: "AEG" },
                { value: "Amica", label: "Amica" },
                { value: "<PERSON><PERSON>", label: "<PERSON><PERSON>" },
                { value: "<PERSON>uknecht", label: "<PERSON>uknecht" },
                { value: "Be<PERSON>", label: "<PERSON><PERSON>" },
                { value: "Blomberg", label: "Blomberg" },
                { value: "<PERSON><PERSON>", label: "<PERSON><PERSON>" },
                { value: "<PERSON>sch", label: "<PERSON><PERSON>" },
                { value: "<PERSON>", label: "<PERSON>" },
                { value: "Comfee", label: "Comfee" },
                { value: "Constructa", label: "Constructa" },
                { value: "De<PERSON><PERSON><PERSON>", label: "DeLonghi" },
                { value: "Dyson", label: "Dyson" },
                { value: "Ecovacs", label: "Ecovacs" },
                { value: "Electrolux", label: "Electrolux" },
                { value: "Exquisit", label: "Exquisit" },
                { value: "Gorenje", label: "Gorenje" },
                { value: "Haier", label: "Haier" },
                { value: "Hanseatic", label: "Hanseatic" },
                { value: "Hoover", label: "Hoover" },
                { value: "Ikea", label: "Ikea" },
                { value: "Ignis", label: "Ignis" },
                { value: "Indesit", label: "Indesit" },
                { value: "Jura", label: "Jura" },
                { value: "Lg", label: "Lg" },
                { value: "Miele", label: "Miele" },
                { value: "Neff", label: "Neff" },
                { value: "Ok", label: "Ok" },
                { value: "Pkm", label: "Pkm" },
                { value: "Premiere", label: "Premiere" },
                { value: "Privileg", label: "Privileg" },
                { value: "Respekta", label: "Respekta" },
                { value: "Samsung", label: "Samsung" },
                { value: "Siemens", label: "Siemens" },
                { value: "Smeg", label: "Smeg" },
                { value: "Sharp", label: "Sharp" },
                { value: "Whirlpool", label: "Whirlpool" },
                { value: "Zanker", label: "Zanker" },
                { value: "Zanussi", label: "Zanussi" }
            ],
            videoTypeOptions: [
                { value: "reparatur", label: "Reparatur" },
                { value: "fehlerdiagnose", label: "Fehlerdiagnose" },
                { value: "fehlercode", label: "Fehlercode" },
                { value: "wissenswert", label: "Wissenswert" },
                { value: "pflege", label: "Pflege" }
            ],
            applianceTypeOptions: [
                { value: "waschmaschine", label: "Waschmaschine" },
                { value: "kuehlschrank", label: "Kuehlschrank" },
                { value: "saugroboter", label: "Saugroboter" },
                { value: "geschirrspueler", label: "Geschirrspueler" },
                { value: "gefrierschrank", label: "Gefrierschrank" },
                { value: "kaffeemaschine", label: "Kaffeemaschine" },
                { value: "mikrowelle", label: "Mikrowelle" },
                { value: "sodastream", label: "Sodastream" },
                { value: "trockner", label: "Trockner" },
                { value: "backofen", label: "Backofen" },
                { value: "kochfeld", label: "Kochfeld" },
                { value: "staubsauger", label: "Staubsauger" },
                { value: "herd", label: "Herd" },
                { value: "dunstabzugshaube", label: "Dunstabzugshaube" },
                { value: "side-by-side", label: "Side-by-Side" },
                { value: "sonstiges", label: "Sonstiges" }
            ],
            element: {
                data: {
                    videoLibrary: null,
                    available: 0
                }
            },
            videoLibraryCollection: null,
        };
    },

    computed: {
        videoLibraryRepository() {
            return this.repositoryFactory.create('video_library');
        }
    },

    created() {
        this.createdComponent();
    },

    methods: {
        createdComponent() {
            this.initElementConfig('video-library-component');
            this.videoLibraryCollection = new EntityCollection(
                '/video_library',
                'video_library',
                Shopware.Context.api
            );
            this.getList();
        },

        getList() {
            console.log(
                this.element.config.applianceType.value,
                this.element.config.videoType.value,
                this.element.config.manufacturer.value
            )
            const criteria = new Criteria();

            criteria.setLimit(this.element.config.limit.value || 8);

            if (this.element.config.applianceType.value && this.element.config.applianceType.value.length > 0) {
                const applianceFilters = this.element.config.applianceType.value.map(appliance =>
                    Criteria.contains('appliances', JSON.stringify(appliance))
                );
                if (applianceFilters.length > 0) {
                    criteria.addFilter(Criteria.multi('OR', applianceFilters));
                }
            }

            if (this.element.config.videoType.value && this.element.config.videoType.value.length > 0) {
                criteria.addFilter(Criteria.equalsAny('videoType', this.element.config.videoType.value));
            }

            if (this.element.config.manufacturer.value && this.element.config.manufacturer.value.length > 0) {
                const manufacturerFilters = this.element.config.manufacturer.value.map(manufacturer =>
                    Criteria.contains('manufacturers', JSON.stringify(manufacturer))
                );
                if (manufacturerFilters.length > 0) {
                    criteria.addFilter(Criteria.multi('OR', manufacturerFilters));
                }
            }

            const videoLibraryRepository = this.repositoryFactory.create('video_library');

            videoLibraryRepository.search(criteria, Shopware.Context.api)
                .then((result) => {
                    this.videoLibraryCollection = result;
                    this.$set(this.element.data, 'videoLibrary', this.videoLibraryCollection);
                    this.$set(this.element.data, 'available', result.total);
                }).catch((error) => {
                console.error('Error fetching video library:', error);
            });

        },
        onSelectionChange() {
            this.getList();
        }
    }
});
