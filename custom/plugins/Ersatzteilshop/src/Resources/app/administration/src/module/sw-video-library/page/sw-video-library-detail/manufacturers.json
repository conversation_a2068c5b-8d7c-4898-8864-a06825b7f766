[{"label": "alle", "value": "alle"}, {"label": "aeg", "value": "aeg"}, {"label": "amica", "value": "amica"}, {"label": "ariston", "value": "ariston"}, {"label": "bauknecht", "value": "bauknecht"}, {"label": "beko", "value": "beko"}, {"label": "bomann", "value": "bomann"}, {"label": "bosch", "value": "bosch"}, {"label": "blomberg", "value": "blomberg"}, {"label": "candy", "value": "candy"}, {"label": "comfee", "value": "comfee"}, {"label": "constructa", "value": "constructa"}, {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"label": "dyson", "value": "dyson"}, {"label": "ecovacs", "value": "ecovacs"}, {"label": "electrolux", "value": "electrolux"}, {"label": "exquisit", "value": "exquisit"}, {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"label": "haier", "value": "haier"}, {"label": "hanse<PERSON>", "value": "hanse<PERSON>"}, {"label": "hoover", "value": "hoover"}, {"label": "ikea", "value": "ikea"}, {"label": "ignis", "value": "ignis"}, {"label": "indesit", "value": "indesit"}, {"label": "jura", "value": "jura"}, {"label": "lg", "value": "lg"}, {"label": "miele", "value": "miele"}, {"label": "neff", "value": "neff"}, {"label": "ok", "value": "ok"}, {"label": "pkm", "value": "pkm"}, {"label": "premiere", "value": "premiere"}, {"label": "privileg", "value": "privileg"}, {"label": "respekta", "value": "respekta"}, {"label": "samsung", "value": "samsung"}, {"label": "sharp", "value": "sharp"}, {"label": "siemens", "value": "siemens"}, {"label": "smeg", "value": "smeg"}, {"label": "whirlpool", "value": "whirlpool"}, {"label": "zanker", "value": "zanker"}, {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}]