import "./app/decorator/condition-type-data-provider.decorator";
import "./app/init/api-service.init";
import "./app/component/rule/sw-condition-base";
import "./app/component/form/select/base/sw-select-selection-list";
import "./app/component/form/sw-custom-field-set-renderer";
import "./module/sw-category/page/sw-category-detail";
import "./module/sw-category/component/sw-category-tree";
import "./module/sw-category/component/sw-category-seo-form";
import "./module/sw-order";
// import "./module/sw-settings-payment";
// Components
import "./module/sw-cms/component/sw-cms-tab-preview";
import "./module/sw-cms/component/sw-cms-create-wizard";
import "./module/sw-cms/component/sw-cms-detail";
import "./module/sw-cms/component/sw-cms-sidebar";

/* CMS Blocks */
import "./module/sw-cms/blocks/text-image/image-text-button";
import "./module/sw-cms/blocks/commerce/advanced-product-slider";
import "./module/sw-cms/blocks/text/divider";
import "./module/sw-cms/blocks/text/text-tab";
import "./module/sw-cms/blocks/text/testimonial-two-column";
import "./module/sw-cms/blocks/commerce/top-brands";
import "./module/sw-cms/blocks/sidebar/side-navigation";
import "./module/sw-cms/blocks/commerce/categories-overview";
import "./module/sw-cms/blocks/commerce/search-widget";
import "./module/sw-cms/blocks/commerce/popular-repair-guide-category-slider";
import "./module/sw-cms/blocks/commerce/most-common-symptoms";
import './module/sw-cms/blocks/text-image/author-reading-time';
import './module/sw-cms/blocks/commerce/video-library-component';
import './module/sw-cms/blocks/commerce/entity-mapping'; // Added Entity Mapping Block
import './module/sw-cms/blocks/commerce/type-plate-scanner';
import "./module/sw-cms/blocks/text-image/image-text-row-column";
import "./module/sw-cms/blocks/text-image/image-text-row-double-column";
import "./module/sw-cms/blocks/ers-blog/blog-listing";
import "./module/sw-cms/blocks/image/downloadable-image";
import "./module/sw-cms/blocks/text/toggle";
import "./module/sw-cms/blocks/text/faq-toggle";
import "./module/sw-cms/blocks/commerce/appliance-search-widget";

/* CMS Elements */
import "./module/sw-cms/elements/banner";
import "./module/sw-cms/elements/advanced-product-slider";
import "./module/sw-cms/elements/divider";
import "./module/sw-cms/elements/tab";
import "./module/sw-cms/elements/top-brands";
import "./module/sw-cms/elements/side-navigation";
import "./module/sw-cms/elements/testimonial";
import "./module/sw-cms/elements/categories-overview";
import "./module/sw-cms/elements/search-widget";
import "./module/sw-cms/elements/popular-repair-guide-category-slider";
import "./module/sw-cms/elements/most-common-symptoms";
import "./module/sw-cms/page";
import "./module/sw-cms/elements/author-reading-time";
import "./module/sw-cms/elements/video-library-component";
import "./module/sw-cms/elements/entity-mapping"; // Added Entity Mapping Element
import "./module/sw-cms/elements/appliance-search-widget";
import "./module/sw-cms/elements/type-plate-scanner";
import "./module/sw-cms/elements/image-text-row-column";
import "./module/sw-cms/elements/blog-listing";
import './module/sw-cms/elements/toggles';
import './module/sw-cms/elements/faq-toggle';


/* Modules */
import "./module/sw-category";
import "./module/sw-manufacturer";
import "./module/sw-resource-rule";
import "./module/sw-video";
import "./module/sw-video-library";
import "./module/sw-category-manufacturer";
import "./module/sw-repair-guide";
import "./module/sw-appliance";
import "./module/sw-error";
import "./module/sw-repair-instruction";
import "./module/sw-product";
import "./module/sw-review";
import "./module/sw-dashboard";
import "./module/sw-appliance-group";
import "./module/sw-seo";
import "./module/sw-image-search-analytics";

/* Snippets */
import deDE from "./module/sw-cms/snippet/de-DE.json";
import enGB from "./module/sw-cms/snippet/en-GB.json";

Shopware.Locale.extend("de-DE", deDE);
Shopware.Locale.extend("en-GB", enGB);
