const { Component } = Shopware;

Component.override('sw-cms-list', {
    computed: {
        sortPageTypes() {
            sortPageTypes = this.$super('sortPageTypes');
            sortPageTypes.push({ value: 'blog', name: this.$tc('sw-cms.sorting.labelSortByBlog') });
            return sortPageTypes;
        },

        pageTypes() {
            pageTypes = this.$super('pageTypes');
            pageTypes.blog = this.$tc('sw-cms.sorting.labelSortByBlog');
            return pageTypes;
        }
    }
})
