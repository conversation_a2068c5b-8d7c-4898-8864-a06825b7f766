{% block sw_cms_element_faq_toggle_config %}
    <div class="sw-cms-el-config-faq-toggle">
        <sw-text-field
            :label="$tc('sw-cms.elements.faqToggle.config.label.headingFontSize')"
            :placeholder="$tc('sw-cms.elements.faqToggle.config.placeholder.headingFontSize')"
            v-model="element.config.headingFontSize.value">
        </sw-text-field>
        
        <sw-text-field
            :label="$tc('sw-cms.elements.faqToggle.config.label.heading')"
            :placeholder="$tc('sw-cms.elements.faqToggle.config.placeholder.heading')"
            v-model="element.config.heading.value"
            @change="onBlur">
        </sw-text-field>
        
        <div class="sw-cms-el-config-faq-toggle__faq-items">
            <div class="sw-cms-el-config-faq-toggle__faq-items-header">
                <h3>{{ $tc('sw-cms.elements.faqToggle.config.label.faqItems') }}</h3>
                <sw-button @click="addFaqItem" size="small" variant="ghost">
                    {{ $tc('sw-cms.elements.faqToggle.config.button.addFaqItem') }}
                </sw-button>
            </div>
            
            <div v-for="(item, index) in faqItemsData" :key="index" class="sw-cms-el-config-faq-toggle__faq-item">
                <div class="sw-cms-el-config-faq-toggle__faq-item-header">
                    <h4>{{ $tc('sw-cms.elements.faqToggle.config.label.faqItem') }} {{ index + 1 }}</h4>
                    <sw-button @click="removeFaqItem(index)" size="small" variant="ghost" class="sw-cms-el-config-faq-toggle__delete-button">
                        {{ $tc('sw-cms.elements.faqToggle.config.button.deleteFaqItem') }}
                    </sw-button>
                </div>
                
                <sw-text-field
                    :label="$tc('sw-cms.elements.faqToggle.config.label.question')"
                    :placeholder="$tc('sw-cms.elements.faqToggle.config.placeholder.question')"
                    v-model="item.question"
                    @change="updateFaqItem(index, 'question', $event)">
                </sw-text-field>
                
                <sw-text-editor
                    :label="$tc('sw-cms.elements.faqToggle.config.label.answer')"
                    :placeholder="$tc('sw-cms.elements.faqToggle.config.placeholder.answer')"
                    v-model="item.answer"
                    @change="updateFaqItem(index, 'answer', $event)"
                    @input="updateFaqItem(index, 'answer', $event)"
                    @blur="updateFaqItem(index, 'answer', $event)">
                </sw-text-editor>
            </div>
        </div>
    </div>
{% endblock %}
