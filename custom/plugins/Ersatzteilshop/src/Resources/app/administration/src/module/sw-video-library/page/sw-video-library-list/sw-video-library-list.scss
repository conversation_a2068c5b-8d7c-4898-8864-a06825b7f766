@import "~scss/variables";

$sw-video-library-color-error: $color-crimson-500;
$sw-video-library-color-success: $color-emerald-500;

.sw-video-library {
  .sw-video-library__content {
    width: 100%;
    height: 100%;
    position: absolute;
  }

  .is--inactive {
    color: $sw-video-library-color-error;
  }

  .is--active {
    color: $sw-video-library-color-success;
  }

  .sw-video-library__thumb {
    height: 40px;
  }

  .sw-video-library__button-group > * + * {
    margin-left: 10px;
  }

    .sw-video-library__sidebar {
    .sw-sidebar-item__content {
      width: 100%;
      max-width: 400px;
      box-sizing: border-box;
    }

    .sw-field {
      margin-bottom: 20px;
      width: 100%;
    }

    .sw-single-select {
      margin-bottom: 20px;
      width: 100%;
    }
  }
}
