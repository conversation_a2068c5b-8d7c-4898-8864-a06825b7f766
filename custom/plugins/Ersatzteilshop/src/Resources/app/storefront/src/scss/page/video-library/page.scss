.content-main-video {
  overflow-x: auto !important;
}

.video-header {
  padding-top: 1rem;
  display: flex;
  flex-direction: column-reverse;
  gap: 0.5rem;
  margin-bottom: 1rem;

  h1 {
    margin: 0;
  }

  .go-back-link {
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  @media (min-width: 768px) {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    h1 {
      order: 1;
    }

    .go-back-link {
      order: 2;
    }
  }
}

.video-library-bg {
  background: #202E3D;
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100px;
  justify-content: center;
  align-items: center;

  h1 {
    color: #FFF;
    text-align: center;
    margin-bottom: 0;
    line-height: 1;
    font-size: 30px;
  }
}

.container-main.video-library {
  padding-top: 60px;
  @include media-breakpoint-down(sm) {
    padding-top: 0;
    margin-top: 40px;
  }
  .table {
    margin-bottom: 0;
    h2 {
      margin-bottom: 0;
    }
    .mobile-toggle {
      border-radius: 8px;
      padding: 9px 10px;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      margin-bottom: 20px;
      -ms-flex-pack: justify;
      justify-content: space-between;
      border: 2px solid #e0e0e0;
      font-weight: 400;
      @include media-breakpoint-down(sm) {
        margin-top: 0;
      }
    }

    .mobile-toggle .toggle-icon {
      transition: transform 0.3s ease-in-out;
      transform: rotate(90deg);
    }
    .mobile-toggle.collapsed .toggle-icon {
      transform: rotate(0deg);
    }
    .mobile-toggle:not(.collapsed) {
      border: 2px solid #202e3d;
      font-weight: 700;
    }
    .video-library_table_paragraph {
      font-size: 16px;
      font-style: normal;
      font-weight: 900;
      line-height: 100%;
    }
    .result-list {
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      justify-content: left;
      list-style: none;
      padding-left: 0;
      margin-bottom: 40px;
      white-space: nowrap;
      -ms-overflow-style: none;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
      gap: 10px;
      overflow-wrap: break-word;
    }
  }
}

.filter-selected {
  display: inline;
  background-color: #FFF;
  border: 2px solid #202E3D;
  height: 28px;
  padding: 0;
  border-radius: 10px;
  &:hover {
    cursor: pointer;
  }
  .link-selected {
    color: #222f3e;
    display: block;
    width: 100%;
    text-align: center;
    min-width: 64px;
    padding-top: 2px;
    padding-left: 5px;
    padding-right: 5px;
    font-weight: 900;
    position: relative;
    top: -1px;
  }
}

.filter-deselected {
  display: inline;
  background-color: #FFF;
  border: 2px solid #D8D8D8;
  height: 28px;
  padding: 0;
  border-radius: 10px;
  @media (min-width: 768px) {
    &:hover {
      cursor: pointer;
      border: 2px solid #202E3D;
    }
  }
  .link-deselected {
    color: #222f3e;
    display: block;
    width: 100%;
    text-align: center;
    min-width: 64px;
    padding-top: 2px;
    padding-left: 8px;
    padding-right: 8px;
  }
}

.yt-thumbnail-placeholder {
  visibility: visible;
  opacity: 1;
}

.ytp-gradient-top {
  height: 48px;
  top: 0;
  z-index: 25;
  width: 100%;
  position: absolute;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.5) 0%, transparent 100%);
  pointer-events: none;
}

.load-more-videos {
  display: inline;
  background-color: #FFF;
  border: 2px solid #D8D8D8;
  border-radius: 10px;
  color: #222f3e;
  height: 28px;
  text-align: center;
  min-width: 64px;
  padding: 0 8px;
  &:hover {
    cursor: pointer;
    display: inline;
    background-color: #FFF;
    border: 2px solid #202E3D;
    border-radius: 10px;
    padding: 0 5px;
    font-weight: 900;
  }
  &:focus {
      background-color: #FFF;
      border: 2px solid #202E3D;
      border-radius: 10px;
      padding: 0 5px;
      font-weight: 900;
      box-shadow: unset !important;
      outline: none;
  }
}

.video-heading {
  font-size: 16px;
  font-style: normal;
  font-weight: 900;
  line-height: 100%;
  margin-top: 40px;
  @include media-breakpoint-down(sm) {
    margin-top: 20px;
  }
}

.video-paragraph {
  margin-top: 10px;
  margin-bottom: 20px;
}
.chapter-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  background-color: #f1f1f1;
  padding: 10px;
  height: 250px;
  margin-top: 20px;
  overflow: hidden;
}

.chapter-scroll-area {
  white-space: nowrap;
  overflow-x: auto;
  overflow-y: hidden;
}

.chapter {
  display: inline-block;
  vertical-align: top;
  margin-right: 20px;
  cursor: pointer;
}

.chapter-title-text {
  font-size: 14px;
  text-align: left;
  max-width: 150px;
  font-weight: 500;
  overflow-wrap: break-word;
  white-space: normal;
}

.chapter-thumbnail {
  width: 145px;
  height: 90px;
  border-radius: 8px;
}

.chapter-time {
  position: relative;
  bottom: 25px;
  right: -90px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  width: 45px;
  border-radius: 5px;
  padding-left: 10px;
  padding-right: 10px;
}

.chapter-content {
  display: inline-block;
  width: 165px;
  padding: 10px;
  height: 175px;
  border-radius: 8px;
  background-color: #fff;
}

.chapter-content:hover {
  background-color: #cfcfcf;
}

.chapter.active .chapter-content {
  background-color: #cfcfcf;
}

.videoTag {
  font-size: small;
  font-weight: bold;
  background-color: #f1f1f1;
  border-radius: 10px;
  padding: 6px 10px;
  margin: 4px;
  display: inline-block;
}

.youtube_thumbnail {
  position: absolute;
  height: 551px;
  object-fit: cover;
  z-index: 5;
  display: block;
  width: 100%
}

.youtube_thumbnail_overview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.playbutton {
  width: 66px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0.5;
}

.no-results-info-container {
  gap: 20px;
  margin-bottom: 20px;
  .no-results-info-container_information{
    display: flex;
    width: 375px;
    height: 225px;
    padding: 16px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;
    flex-shrink: 0;
    border-radius: 8px;
    border: 2px solid #E0E0E0;
    background: #FFF;
    @include media-breakpoint-down(sm) {
      width: 100%;
      margin-top: 20px;
    }
  }
}

.no-results-video-container {
  display: grid;
  grid-template-columns: repeat(auto-fill,minmax(280px,1fr));
  column-gap: 40px;
  row-gap: 10px;
  padding: 0;
  margin: 0;
}



@include media-breakpoint-down(sm) {
  .chapter-container {
    display: block;
  }
  .video-library-bg {
    height: 150px;
  }
  .youtube_thumbnail {
    position: absolute;
    height: 215px;
    object-fit: cover;
    z-index: 5;
    display: block;
    width: 100%
  }
}

@keyframes scale-out {
  to {
    scale: 0;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    scale: 0;
  }

  to {
    opacity: 1;
    scale: 1;
  }
}

::view-transition-group(*) {
  animation-duration: 0.35s;
}

::view-transition-new(*):only-child {
  animation-name: fade-in;
}

::view-transition-old(*):only-child {
  animation-name: scale-out;
}
