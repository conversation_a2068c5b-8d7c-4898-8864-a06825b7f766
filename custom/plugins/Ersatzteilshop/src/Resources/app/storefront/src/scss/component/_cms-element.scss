/*
CMS elements
==============================================
General styling for cms elements
*/



.cms-element-banner {
  position: relative;
  cursor: pointer;
  height: 100%;

  .cms-element-banner__image {
    display: block;
    position: relative;
    width: 100%;

    .cms-image {
      display: block;
      max-width: 100%;
    }

    .cms-image-container {
      display: block;
      max-width: 100%;
      position: relative;

      &.is-cover {
        object-fit: contain;
        font-family: 'object-fit: contain;'; // IE polyfill
        height: 100%;

        .cms-image {
          object-fit: cover;
          font-family: 'object-fit: cover;'; // IE polyfill
          height: 100%;
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
        }
      }

      &.is-cover,
      &.is-stretch {
        .cms-image {
          width: 100%;
        }
      }
    }

    .cms-image-link {
      display: block;
    }

    &.is-cover,
    &.is-stretch {
      .cms-image {
        width: 100%;
      }
    }
  }

  .cms-element-banner__header {
    width: 75%;
    position: absolute;
    top: 0;
    display: block;
    color: #fff;
    background-color: rgba(34, 47, 62, .9);
    padding: 15px;

    p {
      margin: 0;
    }
  }

  .cms-element-banner__button {
    position: absolute;
    top: 50%;
    left: 10px;
  }
}

.text-full-width {
  .cms-element-banner__header {
    width: 100%;
  }
}

.text-align-bottom {
  .cms-element-banner__header {
    bottom: 0;
    top: unset;
  }
}

.button-right-bottom {
  .cms-element-banner__button {
    right: 10px;
    bottom: 10px;
    top: unset;
    left: unset;
  }
}

.cms-element-product-slider {
  .cms-element-title {
    font-size: 18px;
    color: $headings-color;
    font-weight: 500;
    margin-bottom: 1.5rem;
  }

  &.has-border {
    .cms-element-title {
      margin-top: $spacer-sm;
    }
  }

  .product-box {
    border: none;
  }

  .product-image-wrapper {
    height: 150px;
  }

  .product-image {
    max-height: 150px;
  }

  .product-name {
    font-size: 14px;
    font-weight: 400;
    height: 60px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .product-price-info {
    margin-top: 0;
  }

  .product-price {
    margin-top: 0;
    font-size: 14px;
    font-weight: 400;
  }

  .product-review-rating {
    margin-right: 0;
    margin-left: -2px;

    .icon-star, .icon-star-half {
      width: 15px;
      height: 15px;
      margin-right: -4px;
    }
  }

  .base-slider-controls-prev, .base-slider-controls-next {
    font-size: 21px;
    color: rgba(0, 0, 0, .85);
    opacity: 1;
  }
}

.cms-element-tab {
  .nav-tabs {
    border-bottom-width: 2px;

    .nav-item {
      margin-bottom: -2px;
    }

    .nav-link {
      padding: 0 0 3px 0;
      border: none;
      border-bottom: 4px solid transparent;
      color: #495057;
      font-size: 18px;
      font-weight: 500;

      span {
        display: block;
        padding: 0 25px 5px 25px;
        //border-right: 1px solid #495057;
      }
    }

    .nav-link.active, .nav-item.show {
      border-bottom-color: $primary;
      font-weight: 500;

      span {
        //border-right: none;
      }
    }

    .nav-item:first-of-type {
      .nav-link {
        span {
          padding-left: 0px;
        }
      }
    }

    .nav-item:last-of-type {
      .nav-link {
        span {
          //border-right: none;
        }
      }
    }
  }

  .tab-content {
    margin-top: 10px;
    font-size: 14px;
  }
}

.cms-element-divider {
  hr {
    &.is--dashed {
      border-style: dashed;
    }

    &.is--dotted {
      border-style: dotted;
    }
  }
}

.cms-element-testimonial {
  blockquote {
    padding: 10px 40px;

    &:before {
      content: " ";
      background-image: url("/bundles/ersatzteilshop/static/img/cms/icon_quotation.svg");
      display: block;
      width: 28px;
      height: 28px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
      position: absolute;
      left: 5px;
      top: 5px;
    }

    footer {
      &:before {
        content: " ";
        background-image: url("/bundles/ersatzteilshop/static/img/cms/icon_quotation.svg");
        display: block;
        width: 28px;
        height: 28px;
        background-repeat: no-repeat;
        background-position: center;
        background-size: contain;
        margin-left: 15%;
        margin-top: 10px;
        margin-bottom: 10px;
      }
    }
  }
}

.cms-element-categories-overview {
  padding: 10px 0;

  a {
    color: $sw-text-color;

    &:hover {
      color: $primary;
    }
  }

  .categories-overview__table {
    padding: 10px 0;
    margin-bottom: 20px;
    margin-left: 10px;
    margin-right: 10px;
    border: 1px solid $border-color;

    .categories-overview__table__sub {
      max-width: 650px;
      //display: grid;
      //grid-template-rows: 1fr;
      //grid-gap: 10px;
      //grid-auto-flow: row;

      @include media-breakpoint-up(sm) {
        //grid-template-rows: repeat(5, 1fr);
        //grid-template-columns: repeat(3, 1fr);
        //grid-auto-flow: column;
      }
    }

    &__child, &__parent {
      padding: 10px;
      width: 100%;
      position: relative;
    }

    &__parent {
      border-bottom: 1px solid $border-color;
      margin-bottom: 20px;
    }

    &__child {
      text-decoration: underline;

      &:after {
        font-family: 'Font Awesome 5 Free';
        content: "\f054";
        color: $primary;
        float: right;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }

  .categories-overview__footer {
    padding: 0 10px;

    .categories-overview__link-detail {
      margin: 0 10px;
      float: right;
    }
  }
}

.category-icon {
  max-width: 20px;
  max-height: 20px;
}

.cms-element-search-widget {
  .cms-element-search-widget-container {
    background-color: #222f3e;
    color: $white;

    .cms-element-search-widget-content {
      padding: 25px 55px 25px 35px;

      .cms-element-search-widget-title {
        border-bottom: 1px solid $white;
        width: 75%;
        margin-bottom: 15px;
        font-size: 17px;
        color: $white;
        font-weight: 400;
      }

      .cms-element-search-widget-text {
        font-size: 12px;
      }

      .cms-element-search-widget-form {
        margin: 20px 0px;

        .form-control {
          border-radius: 0;
          font-size: 12px;
        }

        .input-group > .input-group-append > .btn {
          border-radius: 0;
          font-size: 12px;
          font-weight: 400;
        }
      }

      .cms-element-search-widget-link {
        color: $white;
        font-size: 13px;
        text-decoration: underline;
      }
    }

    .cms-element-search-widget-image {
      padding: 25px 30px 25px 0px;

      img {
        width: 250px;
        height: 150px;
      }
    }
  }
}
.address-validate-modal {
  .modal-title {
    color: white;
  }

  .modal-dialog {
    height: 600px;
    display: flex;
    align-items: center;
    min-height: calc(100% - 3.5rem);
  }

  .modal-header {
    display: none;
  }

  .modal-content {
    overflow: auto;
    background-color: #222f3e;
    color: $white;
    padding: 12px;
    position: relative;
    box-shadow: 0 0 0 20px rgba(0, 0, 0, .5);
    border-radius: 0;
  }
  .modal-close {
    position: absolute;
    top: 0;
    right: 0;
    color: #ddd;
  }

  .btn-wrapper {
    a {
      width: 100%
    }
    button {
      width: 100%
    }
  }
  .address-info {
    padding-left: 45px
  }
}

.search-widget-modal {
  .modal-dialog {
    max-width: 900px;
    height: 600px;
    display: flex;
    align-items: center;
    min-height: calc(100% - 3.5rem);
  }

  .modal-header {
    display: none;
  }

  .modal-content {
    overflow:auto;
    background-color: #222f3e;
    color: $white;
    padding: 12px;
    position: relative;
    box-shadow: 0 0 0 20px rgba(0, 0, 0, .5);
    border-radius: 0;

    .type-number-content {
      padding: 20px;

      h1 {
        font-size: 20px;
        font-weight: 500;
        color: $white;
      }

      hr {
        background: $white;
      }

      .type-number-select {
        width: 100%;
        border: 1px solid #eee;
        background: #222f3e;
        box-shadow: none;
        border-radius: 0px;
        color: $white;
        padding: 4px;
        height: 28px;
        outline-color: $white;
        margin-top: 25px;
      }

      .js-type-number-description {
        margin-top: 25px;
      }


      img {
        width: 300px;
        height: 225px;
      }

      p {
        margin-bottom: 25px;
      }

      a {
        text-decoration: underline;
        color: $white;

        &:hover {
          color: $white;
        }
      }
    }

  }

  .modal-close {
    position: absolute;
    top: 0;
    right: 0;
    color: #ddd;
  }
}

.cms-element-category-navigation {
  border-right: 1px solid #cccccc;
}

.cms-element-popular-repair-guide-category-slider {
  .cms-element-title {
    font-weight: 700;
  }

  .repair-guide-slider.has-nav {
    padding-left: 30px;
    padding-right: 30px;
  }

  .card-body {
    text-align: center;
    padding: 0;
  }

  .product-info {
    border-top: 1px solid #ccc;
    padding-top: 10px;
  }

  .product-name {
    &:hover {
      color: $link-hover-color;
    }
  }
}

.cms-element-most-common-symptoms {
  .cms-element-title {
    font-weight: 700;
  }

  &__container {
    grid-template-columns: repeat(auto-fill, minmax(270px, 1fr));
    display: grid;
  }

  &__item {
    height: 60px;
    margin-right: 20px;
  }

  &__symptom-link {
    width: 250px;
    padding: 10px 0 10px 40px;
    display: block;
    background-position: 0;
    background-repeat: no-repeat;
    background-size: 30px;
    color: $black;
    background-image: url("/bundles/ersatzteilshop/assets/img/tools.svg");  }
}

.cms-element-video-slider {
  .video-slider-container {
    .video-slider-link {
      color: #000000;
    }

    .video-slider-item {
      padding: 0px 3px;

      .image-slider-image {
        margin-bottom: 10px;
        max-width: 200px ;
      }
    }
  }

  .base-slider-controls-prev, .base-slider-controls-next {
    font-size: 21px;
    color: rgba(0, 0, 0, .85);
    opacity: 1;
  }
}

.type-number-modal-bottom-button {
  display: none;
  align-items: center;
  justify-content: center;
}

.cms-image-download-button-container {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;

  a {
    span {

    }

    img {
      padding: 10px;
      background-color: rgba(153, 153, 153, .6);
      color: white;
      border-radius: 5px;
      margin: 0 0 0 10px;
    }
  }
}

.cms-element-blog-listing {
    .product-box {
        border: none;

        .product-image-placeholder {
            max-width: inherit;
        }

        .product-info {
            a.product-name {
                font-weight: normal;
            }
        }
    }
}

.cms-element-image-text-row-column {
  .col-md-12.cms-element-image-text-row-column-item {
      margin-bottom: 30px;
  }

  .text-above {
    display: flex;
    flex-direction: column-reverse;
    align-items: center;
    justify-content: flex-end;
    .cms-element-image-text-row-column-image {
      margin-top: 30px;
    }
  }

  .text-below {
    display: flex;
    flex-direction: column;
    align-items: center;

    .cms-element-image-text-row-column-image {
      margin-bottom: 30px;
    }
  }
  .text-left {
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    .cms-element-image-text-row-column-image {
      margin-left: 30px;
    }
  }

  .text-right {
    display: flex;
    flex-direction: row;
    align-items: center;
    .cms-element-image-text-row-column-image {
      margin-right: 30px;
    }
  }
}

.related-videos__search {
  width: 350px;
}

@include media-breakpoint-down(md) {
  .related-videos__search {
    width: 100%;
  }

  .image-download-span-desktop{
    display:none;
  }

  .manufacturer-landingpage-image-container {
    img {
      min-width: 100%;
    }
  }

  .image-download-span-mobile{
    display:block;
    position: absolute;
    left: 70%;
    transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    color: green;
    font-size: 16px;
    padding: 5px 11px;
    border: none;
    cursor: pointer;
    border-radius: 5px;
  }

  .type-number-modal {
    display: none;
  }

  .type-number-modal-bottom-button {
    margin-top: 10px;
    display: flex;
    .btn {
      width: 100%;
    }
  }
  .search-widget-modal {
    .modal-dialog {
      width: 100%;
      height: 100%;
      margin: 0;
    }

    .modal-content {
      width: 100%;
      height: 100%;


      .type-number-content {
        padding: 0px 10px;

        .type-number-select {
          margin-bottom: 45px;
        }
        img {
          width: 300px;
          height: 225px;
          margin-left: 0;
        }
      }
    }
  }

  .cms-element-tab {
    .nav-tabs {
      border-bottom: none;
      background-color: #f8f7ee;
      display: flex;

      .nav-item{
        flex: 3;
        text-align: center;
        margin-bottom: 0;

        .nav-link {
          border: none;
          border-top: 2px solid transparent;
          border-radius: 0;

          &.active {
            border-top-color: #44b438;
          }

          span {
            padding: 20px 5px 5px 5px;
            text-align: center;
          }
        }
      }
    }
  }

  .cms-element-banner {
    .cms-element-banner__button {
      top: unset;
      bottom: 10px;
    }
    .cms-element-banner__header {
      width: 100%;
    }
  }

  .cms-element-search-widget {
    .cms-element-search-widget-container {
      .cms-element-search-widget-image {
        margin-left: 25px;
        margin-bottom: 25px;
        padding: 10px 35px 10px 10px;

        img {
          width: 100%;
          height: auto;
        }
      }
    }
  }
}
.sw-cms-el-toggles {
  width: 100%;
}

.cms-element-entity-mapping {
  .entity-mapping-items {
    display: flex;
    flex-wrap: wrap;
    margin-left: -10px;
    margin-right: -10px;
  }

  .entity-mapping-item {
    margin-bottom: 20px;
    padding-left: 10px;
    padding-right: 10px;
  }

  .entity-mapping-category-layout {
    .category-layout-container {
      background: #FFF;
      border: 2px solid #E0E0E0;
      border-radius: 12px;
      overflow: hidden;
      transition: border-color 0.3s ease;
      padding: 20px;

      &:hover {
        border-color: #202E3D;
      }
    }

    .category-layout-grid {
      display: grid;
      grid-template-columns: 171px 1fr;
      gap: 20px;
    }

    .category-image-section {
      background: #F8F9FA;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;

      .category-image-placeholder {
        width: 171px;
        height: 171px;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
          border-radius: 8px;
        }

        svg {
          border-radius: 8px;
        }
      }
    }

    .category-info-section {
      padding: 0;
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .category-header {
      .category-names-row {
        display: flex;
        align-items: baseline;
        gap: 10px;
        flex-wrap: wrap;
      }

      .category-name-main {
        margin: 0;
        font-size: 20px;
        font-weight: 900;
        color: #202E3D;
        line-height: 1.0;

        .category-link {
          text-decoration: none;
          color: inherit;
          transition: color 0.3s ease;
        }
      }

      .category-parent-inline {
        font-size: 16px;
        font-weight: 400;
        color: #6B7280;
        line-height: 1.0;
        text-transform: none;
        letter-spacing: normal;
      }
    }

    .category-manufacturers {

      .manufacturers-grid {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 10px;
        margin-bottom: 0;

        .manufacturer-icon {
          background: #F8F9FA;
          border: 1px solid #E5E7EB;
          border-radius: 6px;
          padding: 8px;
          text-align: center;
          cursor: pointer;
          height: 28px;
          display: flex;
          align-items: center;
          justify-content: center;
          text-decoration: none;

          &:hover {
            border: 2px solid #202E3D;
          }

          img {
            max-width: 100%;
            max-height: 20px;
            object-fit: contain;
          }

          .manufacturer-text {
            font-size: 12px;
            color: #374151;
            font-weight: 500;
          }
        }
      }
    }

    .category-actions {

      .action-buttons-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;

        .delivery-badge {
          display: flex;
          height: 42px;
          justify-content: center;
          align-items: center;
          gap: 10px;
          flex: 1 0 0;
          border-radius: 8px;
          font-weight: 900;
          font-size: 14px;
          background: white;
          border: 1px solid #F5F5F5;
          color: #202E3D;

          svg {
            width: 20px;
            height: 20px;
          }
        }

        .action-button {
          display: flex;
          height: 42px;
          justify-content: center;
          align-items: center;
          gap: 10px;
          flex: 1 0 0;
          border-radius: 8px;
          font-weight: 900;
          font-size: 16px;
          cursor: pointer;

          svg {
            width: 20px;
            height: 20px;
          }

          &.purchase-button {
            background: #202E3D;
            color: white;
            border: 1px solid #202E3D;

            .purchase-link {
              display: flex;
              align-items: center;
              gap: 10px;
              text-decoration: none;
              color: inherit;
              width: 100%;
              justify-content: center;
            }
          }
          &:hover {
            background-color: #74cb7b !important;
            border: 1px solid #74cb7b;
            .purchase-link {
              color: #202E3D;
            }

            svg {
              path {
                fill: #202E3D;
              }
            }
          }

        }
      }
    }

    .delivery-badge-mobile {
      display: none;
    }

    @media (max-width: 768px) {
      .category-layout-container {
        padding: 20px;
      }

      .category-layout-grid {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }

      .category-image-section {
        background: transparent;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        width: 100%;

        .category-image-placeholder {
          width: 100%;
          display: flex;
          gap: 20px;
          align-items: flex-start;

          img {
            width: 171px;
            height: 171px;
          }

          .category-header {
            width: 100%;
            
            .category-names-row {
              justify-content: center;
            }
          }
        }
      }

      .category-info-section {
        padding: 0;
        gap: 10px;
      }

      .category-header {
        .category-names-row {
          display: block;
        }

        .category-name-main {
          font-size: 20px;
          margin-bottom: 10px;
        }

        .category-parent-inline {
          font-size: 14px;
          color: #202E3D;
        }
      }

      .delivery-badge-mobile {
        display: flex;
        height: 32px;
        align-items: center;
        gap: 10px;
        margin-top: 20px;
        font-weight: 900;
        font-size: 14px;
        color: #202E3D;

        svg {
          width: 20px;
          height: 20px;
        }
      }

      .delivery-badge-desktop {
        display: none !important;
      }

      .category-manufacturers {
        .manufacturers-grid {
          grid-template-columns: repeat(3, 1fr);
          gap: 10px;

          .manufacturer-icon {
            height: 48px;
            padding: 8px;
            background: #FFFFFF;
            border: 1px solid #E5E7EB;
            border-radius: 8px;

            img {
              max-height: 24px;
            }
          }
        }
      }

      .category-actions {
        margin-top: 4px;

        .action-buttons-grid {
          grid-template-columns: 1fr;
          gap: 0;

          .action-button {
            height: 48px;

            &.purchase-button {
              width: 100%;
              
              .purchase-link {
                padding: 0;
                height: 100%;
              }
            }
          }
        }
      }
    }
  }

  .manufacturer-card {
    height: 82px;
    padding: 0 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1 0 0;
    border-radius: 8px;
    background: #FFF;
    border: 2px solid #E0E0E0;

    &:hover {
      border: 2px solid #202E3D;
    }

    .manufacturer-link {
      text-decoration: none;
      color: inherit;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .manufacturer-image img {
      max-height: 60px;
      max-width: 100%;
      object-fit: contain;
      display: block;
    }

    .manufacturer-name {
      color: #202E3D;
      text-align: center;
      font-size: 20px;
      font-style: normal;
      font-weight: 900;
      line-height: 150%;
      margin: 0;
    }
  }

  @media (max-width: 767.98px) {
    .category-name,
    .manufacturer-name {
      font-size: 16px;
    }
  }
}


