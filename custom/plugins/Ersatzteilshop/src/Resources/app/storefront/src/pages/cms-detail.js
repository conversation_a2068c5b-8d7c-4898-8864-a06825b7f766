/**
 * AcrisCookieConsent
 * CookieConfiguration
 * OverrideAcrisCookieConsent
 * CookiePlugin
 * OverrideCookiePlugin
 *
 * GcrBadgeWidget
 *
 * already attached in cr.js
 */

import "../shopware-common";
import "../shopware-slider";

// Shopware
import "lazysizes";
import "lazysizes/plugins/parent-fit/ls.parent-fit";
import "lazysizes/plugins/unveilhooks/ls.unveilhooks";
import 'Ersatzteilshop/js/custom';
import 'Ersatzteilshop/js/trustshop';

// Using
// FlinkCmsAnchor
import FlinkCmsAnchor from 'FlinkCmsAnchor/js/anchor';
import FlinkCmsAnchorDotNav from 'FlinkCmsAnchor/js/dotnav';

// AcrisCookieConsentCS
import AcrisCookieConsentPlugin from 'AcrisCookieConsentCS/plugin/acris-cookie-consent/acris-cookie-consent.plugin';
import CookieConfigurationPlugin from 'AcrisCookieConsentCS/plugin/cookie/cookie-configuration.plugin';

// XantenGoogleCustomerReviews
import GcrBadgeWidgetPlugin from "XantenGoogleCustomerReviews/plugin/gcr-badge-widget.plugin";

// WbmTagManagerEcomm
import WbmDataLayer from "WbmTagManagerEcomm/plugin/datalayer.plugin";
import ProductClickTracking from 'WbmTagManagerEcomm/plugin/productClickTracking.plugin';
import Promotions from 'WbmTagManagerEcomm/plugin/promotions.plugin';
import 'WbmTagManagerEcomm/listener/windowLoad.listener';
import 'WbmTagManagerEcomm/service/http-client.service';
import 'WbmTagManagerEcomm/subscriber/cookieConfigurationUpdate.subscriber';

// Ersatzteilshop
import ScrollUpExtendPlugin from "Ersatzteilshop/plugin/widget/scroll-up-extend.plugin";
import RepairGuideSearchWidgetPlugin from 'Ersatzteilshop/plugin/widget/repair-guide-search-widget.plugin';
import ErrorGroupSearchWidgetPlugin from "Ersatzteilshop/plugin/widget/error-group-search-widget.plugin";
import SearchWidgetPlugin from 'Ersatzteilshop/plugin/widget/search-widget.plugin';
import SearchWidgetMobilePlugin from 'Ersatzteilshop/plugin/widget/search-widget-mobile.plugin';
import CollapseDetailPanelPlugin from 'Ersatzteilshop/plugin/collapse/collapse-detail-panel';
//import ShopReviewWidgetPlugin from "Ersatzteilshop/plugin/shop-review/shop-review-widget.plugin";
import OffCanvasCartPlugin from 'Ersatzteilshop/plugin/offcanvas-cart/offcanvas-cart.plugin';

// PluszweiConversionBooster
// import PopupOffCanvasCartPlugin from "PluszweiConversionBooster/popup-offcanvas/popup-offcanvas-cart.plugin";
import UpsellProductCartPlugin from "PluszweiConversionBooster/upsell-product-cart/upsell-product-cart.plugin";

// Necessary for the webpack hot module reloading server
if (module.hot) {
    module.hot.accept();
}

// AcrisCookieConsentCS
PluginManager.register('AcrisCookieConsent', AcrisCookieConsentPlugin, '[data-acris-cookie-consent]');
PluginManager.register('CookieConfiguration', CookieConfigurationPlugin, '[data-acris-cookie-consent]');

// XantenGoogleCustomerReviews
PluginManager.register('GcrBadgeWidget', GcrBadgeWidgetPlugin, '[data-gcr-badge-widget]');
// WbmTagManagerEcomm
PluginManager.register('WbmDataLayer', WbmDataLayer);
PluginManager.register('ProductClickTracking', ProductClickTracking, '.product-box a', { parent: '.product-box' });
PluginManager.register('ProductClickTracking', ProductClickTracking, '.product-box button', { parent: '.product-box' });
PluginManager.register('Promotions', Promotions);

// Ersatzteilshop
PluginManager.override('ScrollUp', ScrollUpExtendPlugin, '[data-scroll-up]');
PluginManager.register('RepairGuideSearchWidget', RepairGuideSearchWidgetPlugin, '[data-repair-guide-search]');
PluginManager.register('ErrorGroupSearchWidgetPlugin', ErrorGroupSearchWidgetPlugin, '[data-error-group-search]');
PluginManager.register('SearchWidgetPlugin', SearchWidgetPlugin, '[data-search-widget]');
PluginManager.register('SearchWidgetMobilePlugin', SearchWidgetMobilePlugin, '[data-search-mobile]');
PluginManager.register('CollapseDetailPanelPlugin', CollapseDetailPanelPlugin);
//PluginManager.register('ShopReviewWidget', ShopReviewWidgetPlugin, '[data-shop-review-widget]');

// PluszweiConversionBooster
PluginManager.register('PluszweiUpsellProductCartPlugin', UpsellProductCartPlugin, '[data-upsell-product-cart-slider-plugin]');
PluginManager.override('OffCanvasCart', OffCanvasCartPlugin, '[data-offcanvas-cart]');

try {
    // In case of the plugin doesn't active
    PluginManager.override('FlinkCmsAnchor', FlinkAnchorPlugin, '[href^="#"]');

} catch (e) {}

// FlinkCmsAnchor
PluginManager.register('FlinkCmsAnchorDotNav', FlinkCmsAnchorDotNav, '[data-flink-cms-anchor-dotnav="true"]');
