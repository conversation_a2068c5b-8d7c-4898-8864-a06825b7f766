$radius-values: 2, 4, 8, 16;

// Border Radius Utilities
@mixin generate-border-radius-utilities {
  @each $radius-value in $radius-values {
    .k11-border-radius-#{$radius-value} {
      border-radius: #{$radius-value}px;
    }
  }
}

.k11-border {
  border-radius: 8px;
  border: 2px solid #D8D8D8;
}
.k11-border-hover {
  @extend .border;
  &:hover, &:active {
    border-color: #202e3d;
  }
}
.k11-border-active {
  border-radius: 8px;
  border: 2px solid #202e3d;
}

.k11-border-green {
  border-radius: 8px;
  border: 2px solid #74CB7B;
}

.k11-border-red {
  border-radius: 8px;
  border: 2px solid #FF4930;
}

.k11-border-orange {
  border-radius: 8px;
  border: 2px solid #D79000;
}

.k11-border-bottom-primary {
  border-bottom: 1px solid #D8D8D8;
}

@include generate-border-radius-utilities;