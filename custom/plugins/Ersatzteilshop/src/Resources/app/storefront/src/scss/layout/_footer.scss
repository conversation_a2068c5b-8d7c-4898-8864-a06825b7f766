footer {
  #footer_top {
    display: flex;
    padding: 13px 130px 6px 131px;
    justify-content: center;
    align-items: center;
    background: #ECECEC;
    text-align: center;
    cursor: pointer;
    box-shadow: 0 3px 6px 0 rgba(0, 0, 0, .3);

    a {
      color: #202E3D;

      &:hover {
        text-decoration: none;
      }
    }
  }
}

.footer-main {
  background-color: #FFFFFF;
  color: #202E3D;
  box-shadow: 0px -3px 6px 0px rgba(0, 0, 0, 0.25);
}

.footer-column {
    padding-top: 0;
    padding-bottom: 0;
}

.footer-icon {
  margin-right: 10px;
  margin-bottom: 2px;
}

.trust-pilot-text{
  font-size: 8px;
  font-weight: 600;
}

.footer-column-headline {
  color: #202E3D;
  font-size: 14px;
  font-style: normal;
  font-weight: 900;
  line-height: 200%;
  padding-top: 20px;
  align-items: center;
}

.footer-link {
    color: #202E3D;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 180%;

    &:hover {
        color: #202E3D;
        text-decoration: none;
        cursor: pointer;
    }
}

.footer-payment-sofort {
    background: url('/bundles/ersatzteilshop/assets/icons/sofort_ueberweisung_icon.png');
}
.footer-payment-visa {
    background: url('/bundles/ersatzteilshop/assets/icons/visa_icon.png');
}
.footer-payment-master-card {
    background: url('/bundles/ersatzteilshop/assets/icons/mastercard_icon.png');
}
.footer-payment-vorkasse {
    background: url('/bundles/ersatzteilshop/assets/icons/vorkasse_icon.png');
}
.footer-payment-amex {
    background: url('/bundles/ersatzteilshop/assets/icons/amex_express_icon.png');
}

.footer-payment-paypal {
    background: url('/bundles/ersatzteilshop/assets/icons/paypal_icon.png');
}

.footer-payment {
    width: 80px;
    height: 24px;
    display: block;
}

.footer-bottom {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #ECECEC;
    height: 41px;
    padding: 6px 0 7px 0;
    gap: 10%;
}

.footer__banner {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #202E3D;
  gap: 10%;

  .banner-text {
    color: #FFF;
    font-style: normal;
    text-align: center;
  }

  .banner-text--large {
    font-size: 24px;
    margin-bottom: 4px;
    margin-top: 10px;
    font-weight: 900;
    line-height: normal;
  }

  .banner-text--small {
    font-size: 14px;
    font-weight: 400;
    line-height: 100%;
  }
}

.social_youtube{
    margin-right: 5px;
    width: 35px;
    height: 25px;
}
.social_pinterest{
    margin-right: 5px;
    width: 30px;
    height: 30px;
}
.social_instagram{
    margin-right: 5px;
    width: 33px;
    height: 33px;
}
.social_facebook{
    margin-right: 5px;
    width: 30px;
    height: 30px;
}

.footer-link-item {
    img {
        width: 100%;
    }
}

@include media-breakpoint-up(lg) {
    .footer-service-menu-list {
        display: block;
    }
}
@include media-breakpoint-up(md) {
  .footer-columns {
    border: none;
  }

  .column-badges {
    display: none;
  }
}
@include media-breakpoint-down(md) {
  .footer-service-menu-link {
    margin: 0 15px;
  }
}
@include media-breakpoint-down(sm) {
    .footer-main {
      background-color: #FFFFFF;
      color: #202E3D;
      box-shadow: none;
    }
    .footer-column {
      border: none;
      padding-left: 33px;
    }
    .footer-service-menu-list {
        padding: 20px 0;
    }
    .footer-service-menu-item {
      display: block;

      &:after {
        border-left: none;
      }

      .footer-service-menu-link {
        padding: 8px 0;
        margin: 0 10px;
      }
    }
    .up-arrow {
      display: block;
      margin: 0 auto 5px;
    }
    .footer-link-item {
      padding: 0;
    }
    .footer-bottom {
      box-shadow: 0px -3px 6px 0px rgba(0, 0, 0, 0.25);
    }
    .toggle-icon {
      background: none;
      border: none;
      cursor: pointer;

      &:focus {
        outline: none;
      }
    }
    .toggle-icon svg {
      transition: transform 0.15s ease-in-out;
    }

    .toggle-icon.--active svg {
      transform: rotate(90deg);
    }
}
.whatsapp-popup {
    display: none;
    position: fixed;
    width: 340px;
    justify-content: center;
    gap: 20px;
    padding: 40px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    border: 1px solid #ccc;
    z-index: 1000;
}
.whatsapp-popup-qrcode {
    display: flex;
    justify-content: center;
    align-items: center;
}
.whatsapp-popup-text-link {
    cursor: pointer;
}
.whatsapp-popup-button {
    display: flex;
    width: 250px;
    height: 42px;
    min-height: 42px;
    padding: 9px 0px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 8px;
    border: none;
    background: #E0E0E0;
    margin: 0 auto
}
.whatsapp-popup-button:hover {
    background-color: #F5F5F5;
}
.whatsapp-popup-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 500;
}
