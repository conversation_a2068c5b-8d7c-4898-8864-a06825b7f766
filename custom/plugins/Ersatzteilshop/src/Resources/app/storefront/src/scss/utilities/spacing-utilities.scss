$increments: 0, 2, 5, 10, 15, 16, 17, 18, 19, 20, 25, 30, 35, 40, 45, 50, 55, 60;
$breakpoints: (
        sm: 576px,
        md: 768px,
        lg: 992px,
        xl: 1200px
);

$percentages: 10, 15, 25, 50, 75, 80, 85, 100;
$sizes: (
        auto: auto,
        100: 100%
);

@mixin generate-margin-padding-utilities {
  $properties: (
          ml: margin-left,
          mt: margin-top,
          mr: margin-right,
          mb: margin-bottom,
          m: margin,
          my: (margin-top, margin-bottom),
          mx: (margin-left, margin-right),
          pl: padding-left,
          pt: padding-top,
          pr: padding-right,
          pb: padding-bottom,
          p: padding,
          py: (padding-top, padding-bottom),
          px: (padding-left, padding-right),
          w: width,
          h: height
  );

  @each $prefix, $property in $properties {
    @each $increment in $increments {
      .k11-#{$prefix}-#{$increment} {
        @if type-of($property) == list {
          @each $prop in $property {
            #{$prop}: #{$increment}px;
          }
        } @else {
          #{$property}: #{$increment}px;
        }
      }
    }

    @if $property == width or $property == height {
      @each $percentage in $percentages {
        .k11-#{$prefix}-#{$percentage} {
          #{$property}: #{$percentage + '%'};
        }
      }

      @each $size, $value in $sizes {
        .k11-#{$prefix}-#{$size} {
          #{$property}: #{$value};
        }
      }
    }
  }

  @each $breakpoint, $width in $breakpoints {
    @media (min-width: $width) {
      @each $prefix, $property in $properties {
        @each $increment in $increments {
          .k11-#{$breakpoint}-#{$prefix}-#{$increment} {
            @if type-of($property) == list {
              @each $prop in $property {
                #{$prop}: #{$increment}px;
              }
            } @else {
              #{$property}: #{$increment}px;
            }
          }
        }

        @if $property == width or $property == height {
          @each $percentage in $percentages {
            .k11-#{$breakpoint}-#{$prefix}-#{$percentage} {
              #{$property}: #{$percentage + '%'};
            }
          }

          @each $size, $value in $sizes {
            .k11-#{$breakpoint}-#{$prefix}-#{$size} {
              #{$property}: #{$value};
            }
          }
        }
      }
    }
  }
}

@include generate-margin-padding-utilities;