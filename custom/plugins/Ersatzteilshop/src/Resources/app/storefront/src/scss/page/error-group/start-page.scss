.page-sidebar-category {
    li {
        a.page-sidebar-category--item {
            padding: 5px 30px 5px 30px;
            display: block;
            background-position: 0;
            background-repeat: no-repeat;
            background-size: 20px;

            &.color-link {
                color: #37ad27;
            }
        }
    }
}

.subcategories-promo-block {
    .subcategories-promo-block--item {
        border: 1px solid #ccc;
        padding: 0;
        width: 23%;

        img {
            max-width: 127px;
        }

        p {
            display: block;
            color: #fff;
            background-color: #222f3e;
            padding: 15px;
            margin: 0;
        }
    }
}

.manufactures-promo-block {
    color: black;

    .manufactures-promo-block--item {
        padding: 0;
        width: 18%;
    }

    img {
        margin: 15px 0;
        max-width: 140px;
        max-height: 70px;
    }

    p {
        border-top: 1px solid #ccc;
        padding-top: 10px;
        margin: 0;
        color: $sw-text-color;
    }
}



.icon_errorcode {
    background-image: url('data:image/svg+xml;utf8,<svg width="33" height="31" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><path d="M32.756 27.96L18.362 1.093A2.073 2.073 0 0 0 16.534 0h-.002c-.765 0-1.466.42-1.827 1.091L.245 27.958a2.063 2.063 0 0 0 .049 2.038A2.081 2.081 0 0 0 2.074 31h28.85a2.071 2.071 0 0 0 1.832-3.04zm-30.682.973L16.532 2.067l14.395 26.866H2.074z" fill="%23212E3D" fill-rule="nonzero"/><path d="M15 12.2v6.6c0 1.217.896 2.2 2 2.2 1.102 0 2-.983 2-2.2v-6.6c0-1.214-.898-2.2-2-2.2-1.102 0-2 .983-2 2.2z" fill="%2339B442"/><circle fill="%2339B442" cx="17" cy="24" r="2"/></g></svg>')
}
