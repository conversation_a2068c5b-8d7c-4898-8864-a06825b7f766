.container-main {
  padding-top: 0;
  margin-bottom: 50px;
}

.t_shirt_desktop_div {
  display:block;
}
.t_shirt_mobile_div {
  display:none
}

.page-sidebar {
  -ms-flex-order: 1;
  order: 1;
  min-width: 225px;
  padding-top: 40px;

  .page-sidebar-section {
    border-right: 1px solid #ccc;
    margin-bottom: 2rem;
    padding-bottom: 30px;
    padding-top: 5px;

    .page-sidebar-section-title {
      font-size: 1.25rem;
      font-weight: 700;
      margin-bottom: 0.8rem;
    }
  }

  .page-sidebar-category-parent {
    position: relative;

    &::before {
      position: absolute;
      left: 0;
      font-family: "Font Awesome 5 Free";
      font-size: 21px;
      content: fa-content($fa-var-chevron-left);
      color: $primary;
      pointer-events: none;
    }
    a {
      font-size: 16px;
      font-weight: 400;
      line-height: 30px;
      color: $black;
      display:block;
    }
    span {
      padding-left: 25px;
    }
  }
  .page-sidebar-category-active {
    font-size: 16px;
    font-weight: 400;
    line-height: 21px;
    color: $black;
  }

  .page-sidebar-category {
    list-style: none;
    padding: 0;
    margin: 0;
    display: list-item;

    li {
      border-bottom: 1px solid #ccc;
      padding: 15px 25px 15px 0px;
      position: relative;

      a {
        display:block;
        width:110%;
        position: relative;
        font-size: 14px;
        font-weight: 400;
        line-height: 21px;
        color: $black;
      }

      img {
        position: absolute;
        height: 20px;
        margin-left: 3px;
        max-width: 28px;
      }
      span {
        width: 200px;
        padding-left: 40px;
        position:relative;
      }

      &::after {
        position: absolute;
        right: 10px;
        font-family: "Font Awesome 5 Free";
        font-size: 21px;
        content: fa-content($fa-var-chevron-right);
        color: $primary;
        top: 50%;
        transform: translate(-50%, -50%);
        pointer-events: none;
      }
    }

    li:first-of-type {
      border-top: 1px solid #ccc;
    }
  }
  ul.nav {
    list-style: none;
    padding: 0;
    margin: 0;
    display: list-item;
  }

  a {
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
    color: $black;
  }
  .page-sidebar-selected {
    margin-bottom: 0;
    .page-sidebar-category {
      li {
        border-bottom: none;
        padding: 10px 0px;
        &::after {
          content: "";
        }

        a {
          background-image: url("#{$asset-path}/img/checkbox_1.png");
          background-size: 14px 14px;
          background-repeat: no-repeat;
          background-position: 100%;
          display: block;
          width: 95%;
          margin-right: 10px;
        }

        img {
          position: absolute;
          margin-left: 3px;
          max-width: 30px;
          margin-top: 5px;
          height: 10px;
        }

        &:hover {
          a {
            background-image: url("#{$asset-path}/img/checkbox_0.png");
          }
        }
      }
      li:first-of-type {
        border-top: none;
      }
    }
  }

  .trusted-shop-section {
    .page-sidebar-section-title {
      font-size: 16px;
    }
    .trusted-shop-content {
      display: flex;
      align-items: center;
      img {
        width: 45px;
        height: 45px;
      }
      ul {
        list-style: none;
        margin-bottom: 0;
        padding-inline-start: 15px;

        li {
          position: relative;
          padding-left: 18px;

          &::before {
            position: absolute;
            left: 0;
            top: 2px;
            font-family: "Font Awesome 5 Free";
            font-size: 12px;
            content: fa-content($fa-var-check);
            color: #64baec;
          }
        }
      }
    }
  }
  .allego-section {
    text-align: center;
    padding-right: 40px;
    a {
      font-size: 16px;
    }
  }
}

.page-main-content {
  -ms-flex-order: 2;
  order: 2;
  -ms-flex-positive: 1;
  flex-grow: 1;
  margin-bottom: 3rem;
  max-width: 100%;

  .search-widget {
    margin-top: 60px;
  }
}

.cat-boxes {
  list-style: none;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding: 10px 0 10px 10px;
  max-width: 850px;

  li {
    width: 161px;
    min-height: 165px;
    text-align: center;
    padding: 20px 14px;

    img {
      padding-bottom: 15px;
      margin-bottom: 5px;
      height: 100px;
      max-width: 100px;
    }

    p {
      border-top: 1px solid #ccc;
      padding-top: 8px;
    }

    a {
      color: $black;

      :hover {
        color: #014c8c;
      }
    }
  }
}


@include media-breakpoint-up(md) {
    .all-brands {
        column-count: 4;
    }
}

@include media-breakpoint-down(sm) {
    .all-brands {
        column-count: 2;
    }
}

.all-brands {
  list-style: none;
  padding: 0;

  a {
    color: $black;

    :hover {
      color: #014c8c;
    }
  }
}

.category-description {
  margin-top: 20px;
  font-style: italic;
}

.expandable-description {
  position: relative;

  .description-content {
    overflow: hidden;
    transition: max-height 0.3s ease-in-out;
    max-height: 3em;
    line-height: 1.5em;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 1.5em;
      background: linear-gradient(transparent, white);
      pointer-events: none;
      transition: opacity 0.3s ease-in-out;
    }
  }

  .expand-toggle {
    display: none;
  }

  .expand-button {
    margin-top: 10px;

    .expand-text {
      display: inline;
      font-style: normal;
    }

    .collapse-text {
      display: none;
    }
  }

  .expand-toggle:checked ~ .description-content {
    max-height: none;

    &::after {
      opacity: 0;
    }
  }

  .expand-toggle:checked ~ .expand-button {
    .expand-text {
      display: none;
    }

    .collapse-text {
      display: inline;
    }
  }

  &.short-content {
    .expand-button {
      display: none;
    }

    .description-content::after {
      display: none;
    }
  }
}

.category-section-title {
  margin-bottom: 20px;
  margin-top: 60px;
  font-size: 18px;
}

.category-seo {
  margin-bottom: 20px;

  h2 {
    margin-top: 2rem;
    font-size: 18px;
  }
}


.product-listing-wrapper {
  padding-top: 2.5rem;
}

.suitable-repair-guides {
  margin: 40px 0px;

  .repair-guide-list {
    display: flex;
    flex-wrap: wrap;

    .suitable-repair-guide-item {
      display: flex;
      align-items: center;
      flex: 0 50%;
      height: 60px;

      a {
        padding: 10px 0 10px 40px;
        display: block;
        background-position: 0;
        background-repeat: no-repeat;
        background-size: 30px;
        background-image: url(/bundles/ersatzteilshop/assets/img/no_icon_cat.svg);
        color: #000;
      }
    }
  }

  .btn-repair-guide {
    float: right;
    margin-right: 100px;
    padding-left: 30px;
    padding-right: 30px;
  }
}

@include media-breakpoint-down(md) {
  .page-sidebar {
    min-width: 200px;
  }
}

@include media-breakpoint-down(sm) {

  .t_shirt_desktop_div {
    display:none;
  }
  .t_shirt_mobile_div {
    display:block
  }
  .page-sidebar {
    display: none;
    padding-top: 10px;
    .page-sidebar-section {
      border-right: none;
    }
  }

  .page-main-content {
    padding: 10px;
  }

  .category-section-title {
    font-size: $h2-font-size;
    margin-bottom: 1.5rem;
  }
  .suitable-repair-guides {
    .btn-repair-guide {
      float: none;
      width: 100%;
      margin-top: 15px;
    }
  }
}

.cat-box-index-image {
  border-bottom: 1px solid #ccc;
  padding-bottom: 15px;
  margin-bottom: 5px;
  width: 120px;
  height: 60px
}

.faq_landing {
  margin: 3rem 0;
  text-align: center;

  div {
      display: inline-block;
      width: 200px;
      margin-bottom: 1.5rem;
      vertical-align: top;
  }
  a {
    color: #000;
  }
  img {
    height: 100px;
    display: block;
    margin: 0 auto 10px;
  }
  ul {
    padding: 0;
    list-style: none;
  }
  li {
    a {
      color: #000;
    }
  }


}

.svg-icon {
  display: inline-block;
}

.is--blog-page {
  font-size: 16px;
  h1,
  .h1 {
    font-size: 30px;
    margin-bottom: 2rem;
  }
  h2,
  .h2 {
    font-size: 24px;
  }
  h3,
  .h3 {
    font-size: 18px;
  }
  h4,
  .h4 {
    font-size: 16px;
  }
}

.newsletter-banner {
  max-width: 900px;
  margin: 2rem auto;
  .confirm-message {
    padding: 1rem 0;
    text-align: left;
    font-size: inherit;
    font-weight: normal;
  }
  h3, h4 {
    color: #fff;
  }
  h4 {
    font-size: 16px;
    font-weight: 400;
  }
  .confirm-alert {
    padding: 1em 0;
    .alert {
      margin-top: 1.2rem;
    }
  }
  #newsletter-form {
    margin: 1rem 0;
    input {
      border-radius: 0;
    }
    button {
      border-radius: 0;
    }
    @include media-breakpoint-up(sm) {
      input[name='firstName'] {
        margin: 0 1rem;
        max-width: 300px;
      }
    }
    .newsletter-message {
      margin: 0.5rem 0;
      color: #44b438;
    }
  }
  .newsletter-note {
    font-size: 0.8rem;
  }
}
@include media-breakpoint-down(sm) {
  .newsletter-banner {
    text-align: center;
    .confirm-message, .confirm-alert {
      padding: 0;
      text-align: center;
      font-size: inherit;
      font-weight: normal;
      .alert {
        text-align: left;
      }
    }
    h3 {
      font-size: 1rem;
      line-height: 1.5rem;
    }
    h4 {
      font-size: 1rem;
      font-weight: 400;
      line-height: 1.5rem;
    }
    #newsletter-form {
      input {
        width: 100%;
        margin: 0.5rem 0;
      }
      .input-group-append {
        width: 100%;
      }
      button {
        margin: 0.5rem 0;
        width: 100%;
      }
    }
  }
}
