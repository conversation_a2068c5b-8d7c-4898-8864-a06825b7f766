$font-sizes: 12, 14, 16, 18, 20, 24;
$line-heights: 1, 1.2, 1.4, 1.6, 1.8, 2;
$font-weights: 100, 200, 300, 400, 500, 600, 700, 800, 900;
$breakpoints: ( 'md': 768px, );

@mixin generate-font-utilities {

  @each $size in $font-sizes {
    .k11-font-size-#{$size} {
      font-size: #{$size}px;
    }
  }


  @each $line-height in $line-heights {
    .k11-line-height-#{$line-height} {
      line-height: #{$line-height};
    }
  }

  @each $weight in $font-weights {
    .k11-font-weight-#{$weight} {
      font-weight: #{$weight};
    }
  }

  .k11-text-left {
    text-align: left;
  }
  .k11-text-center {
    text-align: center;
  }
  .k11-text-right {
    text-align: right;
  }

  @each $breakpoint-name, $breakpoint-size in $breakpoints {
    @media (min-width: $breakpoint-size) {
      .k11-text-#{$breakpoint-name}-left {
        text-align: left;
      }
      .k11-text-#{$breakpoint-name}-center {
        text-align: center;
      }
      .k11-text-#{$breakpoint-name}-right {
        text-align: right;
      }
    }
  }
}

@include generate-font-utilities;