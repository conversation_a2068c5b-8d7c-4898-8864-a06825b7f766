import "lazysizes";
import "lazysizes/plugins/parent-fit/ls.parent-fit";
import "lazysizes/plugins/unveilhooks/ls.unveilhooks";

import './js/custom';
//import './js/trustshop';

import FormValidationExtendPlugin from "./plugin/form/form-validation-extend.plugin";
import FormSubmitLoaderExtendPlugin from "./plugin/form/form-submit-loader-extend.plugin";
import SearchWidgetFixPlugin from "./plugin/header/search-widget-fix.plugin";
import ListingLimitPlugin from './plugin/listing/listing-limit.plugin';
import ShippingAddressOptionPlugin from './plugin/checkout/shipping-address-option.plugin';
import ShippingNotAvailableNoticePlugin from './plugin/checkout/shipping-not-available-notice.plugin';
import SearchWidgetPlugin from './plugin/widget/search-widget.plugin';
import CollapseDetailPanelPlugin from './plugin/collapse/collapse-detail-panel';
import VolumePriceSelectPlugin from './plugin/product-detail/volume-price-select.plugin';
import AppliancePlugin from './plugin/product-detail/appliance.plugin';
import FollowAnchorPlugin from './plugin/anchor/follow-anchor.plugin';
import RepairGuideSearchWidgetPlugin from './plugin/widget/repair-guide-search-widget.plugin';
import ApplianceProductListPlugin from './plugin/appliance/product-list.plugin';
import ErrorGroupSearchWidgetPlugin from './plugin/widget/error-group-search-widget.plugin';
import VideoSliderPlugin from './plugin/widget/video-slider.plugin';
import ApplianceCategoryPlugin from "./plugin/appliance/categories.plugin";
import SearchWidgetMobilePlugin from './plugin/widget/search-widget-mobile.plugin';
import ScrollToReviewPlugin from './plugin/product-detail/scroll-to-review.plugin';
import ErrorPageScrollPlugin from './plugin/error/error-page-scroll.plugin';
import SearchPageWidgetPlugin from './plugin/widget/search-page-widget.plugin';
import RecentSearchPlugin from './plugin/search/recent-search.plugin';
import VideoLibraryOverviewPlugin from './plugin/video-library/video-library-overview.plugin';
import VideoLibraryYoutubePlayerPlugin from './plugin/video-library/video-library-youtube-player.plugin';
import VideoLibraryDetailsPlugin from './plugin/video-library/video-library-details.plugin';
import BlogFilterPlugin from './plugin/blog/blog-filter-plugin';
import ImageSearchPlugin from './plugin/product-detail/image-search.plugin';

import LazyLoadGalleryImagesPlugin from './plugin/product-detail/lazy-load-gallery-images.plugin'
import AddressValidatePlugin from './plugin/address/address-validate.plugin';
import StockSubscriberPlugin from './plugin/product-detail/stock-subscriber.plugin';
import UpdateCharacterDisplayPlugin from './plugin/customer-repair-guide/update-character-display.plugin'
import UploadPreviewImagePlugin from './plugin/customer-repair-guide/upload-preview-image.plugin'
import GenerateBulletpointListPlugin from './plugin/customer-repair-guide/generate-bulletpoint-list.plugin'
import CreateStepPlugin from './plugin/customer-repair-guide/create-step.plugin'
import ScrollIntoViewPlugin from './plugin/customer-repair-guide/scroll-into-view.plugin'
import ProductPriceAjaxPlugin from './plugin/product-price/product-price-ajax.plugin'
import ProductDeliveryAjaxPlugin from './plugin/product-delivery/product-delivery-ajax.plugin'
import LocationReviewFormPlugin from "./plugin/shop-review/location-review-form.plugin";
import ErsatzteilshopFilterPropertySelectPlugin from "./plugin/listing/ersatzteilshop-filter-property-select.plugin";
import ErsatzteilshopListingPlugin from "./plugin/listing/ersatzteilshop-listing.plugin";
import ListingSearchPlugin from "./plugin/listing/listing-search.plugin";
import CategoryFilterPlugin from "./plugin/listing/category-filter.plugin";
import ListingInfinitivePaginationPlugin from "./plugin/listing/listing-infinitive-pagination.plugin";
import ListingFilteringPlugin from "./plugin/listing/listing-filter.plugin";
import ListingSortingPlugin from "./plugin/listing/listing-sorting.plugin";
import NoResultPagePlugin from './plugin/search/no-result-page.plugin';
import ApplianceSearchWidgetPlugin from './plugin/widget/appliance-search-widget.plugin';
import LinkMaskingPlugin from './plugin/utilities/link-masking-plugin.js';
import DummyProductPlugin from "./plugin/product-detail/dummy-product.plugin";
import AustriaNoticePlugin from './plugin/address/austria-notice.plugin';
import VideoLibraryWidgetPlugin from './plugin/widget/video-library-widget.plugin';
import ApplianceTabFilterPlugin from "./plugin/appliance/appliance-tab-filter.plugin";


//import ShopReviewWidgetPlugin from "./plugin/shop-review/shop-review-widget.plugin";
import NewsletterSubscriptionCheckboxPlugin from "./plugin/newsletter/subscription-checkbox.plugin";
import ScrollUpExtendPlugin from "./plugin/widget/scroll-up-extend.plugin";
import ProductQuantityPlugin from "./plugin/product-detail/product-quantity.plugin";
import FormAjaxSubmitExtendPlugin from './plugin/product-detail/form-ajax-submit-extend.plugin';
import TopBrandsWidgetPlugin from "./plugin/widget/topbrands-widget.plugin";
import CrossSellingProductSwitcherPlugin from './plugin/product-detail/cross-selling-product-switcher.plugin';
import FlinkAnchorPlugin from "./plugin/anchor/flink-anchor.plugin";
import LocalOffCanvasTabsPlugin from "./plugin/offcanvas-tabs/local-offcanvas-tabs.plugin";
import OffCanvasCartPlugin from './plugin/offcanvas-cart/offcanvas-cart.plugin';
import ListingProductCountPlugin from "./plugin/listing/listing-product-count.plugin";
import UploadReviewImagePlugin from './plugin/product-detail/upload-review-image.plugin'
import RepairReviewImagePlugin from './plugin/product-detail/repair-review-image.plugin'
import AddToCartExtendPlugin from "./plugin/add-to-cart/add-to-cart-extend.plugin";
import NavigationPlugin from "./plugin/navigation/navigation.plugin";
import FooterTogglePlugin from "./plugin/footer/footer-toggle.plugin";
import FooterWhatsAppPlugin from "./plugin/footer/footer-whatsapp.plugin";
import ZendeskHelpcenterPlugin from "./plugin/zendesk-helpcenter/zendesk-helpcenter.plugin";
import ScrollToGpsrPlugin from "./plugin/product-detail/scroll-to-gpsr.plugin";
import AddressAccountTypePlugin from "./plugin/checkout/address-account-type.plugin";
import PrioPlugin from "./plugin/checkout/prio.plugin";
import TippingPlugin from "./plugin/checkout/tipping.plugin";
import ProgressBarPlugin from "./plugin/checkout/progress-bar.plugin";
import CheckoutConfirmPaymentToggle from "./plugin/checkout/checkout-confirm-payment-toggle.plugin";
import VoucherEnhancementPlugin from "./plugin/checkout/voucher-enhancement.plugin";
import ShippingAddressEditorPlugin from "./plugin/checkout/shipping-address-editor.plugin";


PluginManager.register('Tipping', TippingPlugin, '[data-tipping]');
PluginManager.register('CrossSellingProductSwitcher', CrossSellingProductSwitcherPlugin, '[data-cross-selling-product-switcher]');
PluginManager.override('FormAjaxSubmit', FormAjaxSubmitExtendPlugin, '[data-form-ajax-submit]');
PluginManager.register('ProductQuantity', ProductQuantityPlugin, '[data-product-quantity]');
PluginManager.override('ScrollUp', ScrollUpExtendPlugin, '[data-scroll-up]');
PluginManager.register('ListingLimitPlugin', ListingLimitPlugin, '[data-listing-limit]');
PluginManager.register('ShippingAddressOptionPlugin', ShippingAddressOptionPlugin, '[data-shipping-address-option]');
PluginManager.register('ShippingNotAvailableNoticePlugin', ShippingNotAvailableNoticePlugin, '[data-shipping-not-available-notice]');
PluginManager.register('SearchWidgetPlugin', SearchWidgetPlugin, '[data-search-widget]');
PluginManager.register('CollapseDetailPanelPlugin', CollapseDetailPanelPlugin);
PluginManager.register('VolumePriceSelectPlugin', VolumePriceSelectPlugin, '[data-volume-price-selection="true"]');
PluginManager.register('Appliance', AppliancePlugin, '[data-appliance]');
PluginManager.register('Appliance', AppliancePlugin, '.product-detail-reviews-rating');
PluginManager.register('FollowAnchor', FollowAnchorPlugin, '[data-follow-anchor]');
PluginManager.register('RepairGuideSearchWidget', RepairGuideSearchWidgetPlugin, '[data-repair-guide-search]');
PluginManager.register('ApplianceProductList', ApplianceProductListPlugin, '[data-appliance-product-list]');
PluginManager.register('ErrorGroupSearchWidgetPlugin', ErrorGroupSearchWidgetPlugin, '[data-error-group-search]');
PluginManager.register('VideoSliderPlugin', VideoSliderPlugin, '[data-video-slider]');
PluginManager.register('SearchWidgetMobilePlugin', SearchWidgetMobilePlugin, '[data-search-mobile]');
PluginManager.register('ScrollToReviewPlugin', ScrollToReviewPlugin, '[data-scroll-to-review]');
PluginManager.register('ErrorPageScrollPlugin', ErrorPageScrollPlugin, '[data-error-page-scroll]');
PluginManager.register('ApplianceCategory', ApplianceCategoryPlugin, '[data-appliance-category]')
PluginManager.register('UpdateCharacterDisplayPlugin', UpdateCharacterDisplayPlugin, '[data-customer-repair-instruction]');
PluginManager.register('UploadPreviewImagePlugin', UploadPreviewImagePlugin, '[data-customer-repair-instruction-upload]');
PluginManager.register('GenerateBulletpointListPlugin', GenerateBulletpointListPlugin, '[data-customer-repair-instruction-tools]');
PluginManager.register('CreateStepPlugin', CreateStepPlugin, '[data-instruction-step]');
PluginManager.register('ScrollIntoViewPlugin', ScrollIntoViewPlugin, '[data-instruction-scroll]');
PluginManager.register('AddressValidatePlugin', AddressValidatePlugin, '[data-address-validate]');
PluginManager.override('FormValidation', FormValidationExtendPlugin, '[data-form-validation]');
PluginManager.override('FormSubmitLoader', FormSubmitLoaderExtendPlugin, '[data-form-submit-loader]');
PluginManager.register('LazyLoadGalleryImagesPlugin', LazyLoadGalleryImagesPlugin, '[data-lazy-load-gallery-images]');
PluginManager.register('StockSubscriberPlugin', StockSubscriberPlugin, '#form-product-stock-subscriber');
PluginManager.register('ProductPriceAjax', ProductPriceAjaxPlugin, '[data-product-price-ajax]');
PluginManager.register('ProductDeliveryAjax', ProductDeliveryAjaxPlugin, '[data-product-delivery-ajax]');
PluginManager.register('LocationReviewForm', LocationReviewFormPlugin, '[data-location-review-form]');
PluginManager.override('Listing', ErsatzteilshopListingPlugin, '[data-listing]');
PluginManager.override('FilterPropertySelect', ErsatzteilshopFilterPropertySelectPlugin, '[data-filter-property-select]');
PluginManager.register('CategoryFilter', CategoryFilterPlugin, '[data-category-filter]');
PluginManager.register('ListingSearch', ListingSearchPlugin, '[data-listing-search]');
PluginManager.register('ListingInfinitivePagination', ListingInfinitivePaginationPlugin, '[data-listing-infinitive-pagination]');
PluginManager.register('ListingFilteringPlugin', ListingFilteringPlugin, '[data-listing-filtering]');
PluginManager.register('ListingSortingPlugin', ListingSortingPlugin, '[data-listing-sortings]');
//PluginManager.register('ShopReviewWidget', ShopReviewWidgetPlugin, '[data-shop-review-widget]');
PluginManager.register('NewsletterSubscriptionCheckbox', NewsletterSubscriptionCheckboxPlugin, '[data-newsletter-subscription-checkbox]');
PluginManager.register('TopBrandsWidgetPlugin', TopBrandsWidgetPlugin, '[data-topbrands-widget]');
PluginManager.register('SearchPageWidgetPlugin', SearchPageWidgetPlugin, '[data-search-page-widget]');
PluginManager.register('LocalOffCanvasTabs', LocalOffCanvasTabsPlugin, '[data-local-offcanvas-tabs]');
PluginManager.register('ListingProductCount', ListingProductCountPlugin, '[data-listing-product-count]');
PluginManager.register('UploadReviewImagePlugin', UploadReviewImagePlugin, '[data-customer-product-upload]');
PluginManager.register('RepairReviewImagePlugin', RepairReviewImagePlugin, '[data-customer-repair-review-upload]');
PluginManager.register('RecentSearchPlugin', RecentSearchPlugin, '[data-recent-search]');
PluginManager.register('VideoLibraryOverviewPlugin', VideoLibraryOverviewPlugin, '[data-attribute-video-library]');
PluginManager.register('VideoLibraryYoutubePlayerPlugin', VideoLibraryYoutubePlayerPlugin, '[data-has-chapters]');
PluginManager.register('VideoLibraryDetailsPlugin', VideoLibraryDetailsPlugin, '[data-attribute-video-library-detail]');
PluginManager.register('ZendeskHelpcenterPlugin', ZendeskHelpcenterPlugin, '[data-zendesk-helpcenter]');
PluginManager.register('BlogFilterPlugin', BlogFilterPlugin, '[data-attribute-blog]');
PluginManager.register('AddToCartGA4', AddToCartExtendPlugin, '[data-add-to-cart]');
PluginManager.register('NavigationPlugin', NavigationPlugin, '[data-header-navigation]');
PluginManager.register('FooterTogglePlugin', FooterTogglePlugin, '[data-footer]');
PluginManager.register('FooterWhatsAppPlugin', FooterWhatsAppPlugin, '[data-footer-whatsapp]');
PluginManager.register('NoResultPagePlugin', NoResultPagePlugin, '[data-no-result]');
PluginManager.register('ApplianceSearchWidgetPlugin', ApplianceSearchWidgetPlugin, '[data-appliance-search-widget]');
PluginManager.register('LinkMaskingPlugin', LinkMaskingPlugin, '[data-link-masking]');
PluginManager.register('DummyProductPlugin', DummyProductPlugin, '[data-dummy-product]');
PluginManager.register('AustriaNotice', AustriaNoticePlugin, '[data-austria-notice]');
PluginManager.register('ImageSearchPlugin', ImageSearchPlugin, '[data-image-search]');
PluginManager.register('ScrollToGprsPlugin', ScrollToGpsrPlugin, '[data-scroll-to-gpsr]');
PluginManager.register('VideoLibraryWidgetPlugin', VideoLibraryWidgetPlugin, '[data-video-library-widget]');
PluginManager.register('AddressAccountTypePlugin', AddressAccountTypePlugin, '[data-account-type-buttons]');
PluginManager.override('OffCanvasCart', OffCanvasCartPlugin, '[data-offcanvas-cart]');
PluginManager.register('Prio', PrioPlugin, '[data-prio]');
PluginManager.register('ProgressBarPlugin', ProgressBarPlugin, '[data-progress-bar]');
PluginManager.register('ApplianceTabFilterPlugin', ApplianceTabFilterPlugin, '[data-appliance-tab-filter]');
PluginManager.register('CheckoutConfirmPaymentToggle', CheckoutConfirmPaymentToggle, '[data-confirm-payment-toggle]');
PluginManager.register('VoucherEnhancement', VoucherEnhancementPlugin, '[data-voucher-enhancement="true"]');
PluginManager.register('ShippingAddressEditor', ShippingAddressEditorPlugin, '#btn-edit-shipping-address-modal');

// Override the original SearchWidgetPlugin
PluginManager.override('SearchWidget', SearchWidgetFixPlugin, '[data-search-form]');

try {
    // In case of the plugin doesn't active
    PluginManager.override('FlinkCmsAnchor', FlinkAnchorPlugin, '[href^="#"]');
} catch (e) {}
