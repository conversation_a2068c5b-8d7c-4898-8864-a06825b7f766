import "../shopware-common";
import "../shopware-slider";

import 'bootstrap/js/dist/tooltip';

import TooltipUtil from 'src/utility/tooltip/tooltip.util';
/**
 * WbmTagManagerEcomm
 */
import WbmDataLayer from "WbmTagManagerEcomm/plugin/datalayer.plugin";
import ProductClickTracking from 'WbmTagManagerEcomm/plugin/productClickTracking.plugin';
import Promotions from 'WbmTagManagerEcomm/plugin/promotions.plugin';

import 'WbmTagManagerEcomm/listener/windowLoad.listener';
import 'WbmTagManagerEcomm/service/http-client.service';
import 'WbmTagManagerEcomm/subscriber/cookieConfigurationUpdate.subscriber';
/**
 * AcrisCookieConsentCS
 */
import AcrisCookieConsentPlugin from 'AcrisCookieConsentCS/plugin/acris-cookie-consent/acris-cookie-consent.plugin';
import CookieConfigurationPlugin from 'AcrisCookieConsentCS/plugin/cookie/cookie-configuration.plugin';
/**
 * XantenGoogleCustomerReviews
 */
import GcrBadgeWidgetPlugin from "XantenGoogleCustomerReviews/plugin/gcr-badge-widget.plugin";

// PluszweiBackInStockReminder
import BackInStockReminderPlugin from "PluszweiBackInStockReminder/plugin/product-detail/back-in-stock-reminder.plugin";

/**
 * Ersatzteilshop
 */
import 'Ersatzteilshop/js/custom';
import 'Ersatzteilshop/js/trustshop';

import CollapseDetailPanelPlugin from 'Ersatzteilshop/plugin/collapse/collapse-detail-panel';
import LocationReviewFormPlugin from "Ersatzteilshop/plugin/shop-review/location-review-form.plugin";
//import ShopReviewWidgetPlugin from "Ersatzteilshop/plugin/shop-review/shop-review-widget.plugin";
import NewsletterSubscriptionCheckboxPlugin from "Ersatzteilshop/plugin/newsletter/subscription-checkbox.plugin";
import ScrollUpExtendPlugin from "Ersatzteilshop/plugin/widget/scroll-up-extend.plugin";

import UploadReviewImagePlugin from 'Ersatzteilshop/plugin/product-detail/upload-review-image.plugin'
import RepairReviewImagePlugin from 'Ersatzteilshop/plugin/product-detail/repair-review-image.plugin'
import ProductQuantityPlugin from "Ersatzteilshop/plugin/product-detail/product-quantity.plugin";
import FormAjaxSubmitExtendPlugin from 'Ersatzteilshop/plugin/product-detail/form-ajax-submit-extend.plugin';
import CrossSellingProductSwitcherPlugin
    from 'Ersatzteilshop/plugin/product-detail/cross-selling-product-switcher.plugin';
import LazyLoadGalleryImagesPlugin from 'Ersatzteilshop/plugin/product-detail/lazy-load-gallery-images.plugin'
import StockSubscriberPlugin from 'Ersatzteilshop/plugin/product-detail/stock-subscriber.plugin';
import ScrollToReviewPlugin from 'Ersatzteilshop/plugin/product-detail/scroll-to-review.plugin';
import VolumePriceSelectPlugin from 'Ersatzteilshop/plugin/product-detail/volume-price-select.plugin';
import AppliancePlugin from 'Ersatzteilshop/plugin/product-detail/appliance.plugin';

import FormValidationExtendPlugin from "Ersatzteilshop/plugin/form/form-validation-extend.plugin";
import FormSubmitLoaderExtendPlugin from "Ersatzteilshop/plugin/form/form-submit-loader-extend.plugin";

import RatingSystemPlugin from 'src/plugin/rating-system/rating-system.plugin';
import FlinkAnchorPlugin from "Ersatzteilshop/plugin/anchor/flink-anchor.plugin";

import RepairReviewSubmit from "RepairReview/plugin/review-form-submit/review-form-submit.plugin";
import RepairReviewList from "RepairReview/plugin/review-list/repair-review-list.plugin";
import SearchWidgetPlugin from "Ersatzteilshop/plugin/widget/search-widget.plugin";

import FixFormAjaxSubmitPlugin from "K11GuestReviews/plugin/form-ajax-submit/form-ajax-submit.plugin";

import SwagPayPalExpressCheckoutButton from "SwagPayPal/express-checkout-button/swag-paypal.express-checkout";
import SwagPayPalInstallmentBanner from "SwagPayPal/installment/swag-paypal.installment-banner";
// import PopupOffCanvasCartPlugin from "PluszweiConversionBooster/popup-offcanvas/popup-offcanvas-cart.plugin";
import OffCanvasCartPlugin from 'Ersatzteilshop/plugin/offcanvas-cart/offcanvas-cart.plugin';
import UpsellProductCartPlugin from "PluszweiConversionBooster/upsell-product-cart/upsell-product-cart.plugin";
import FooterWhatsAppPlugin from "Ersatzteilshop/plugin/footer/footer-whatsapp.plugin";
import ScrollToGpsrPlugin from "Ersatzteilshop/plugin/product-detail/scroll-to-gpsr.plugin";

new TooltipUtil();

PluginManager.register('WbmDataLayer', WbmDataLayer);
PluginManager.register('ProductClickTracking', ProductClickTracking, '.product-box a', { parent: '.product-box' });
PluginManager.register('ProductClickTracking', ProductClickTracking, '.product-box button', { parent: '.product-box' });
PluginManager.register('Promotions', Promotions);

// Necessary for the webpack hot module reloading server
if (module.hot) {
    module.hot.accept();
}

PluginManager.register('AcrisCookieConsent', AcrisCookieConsentPlugin, '[data-acris-cookie-consent]');PluginManager.register('CookieConfiguration', CookieConfigurationPlugin, '[data-acris-cookie-consent]');
PluginManager.register('GcrBadgeWidget', GcrBadgeWidgetPlugin, '[data-gcr-badge-widget]');
PluginManager.override('ScrollUp', ScrollUpExtendPlugin, '[data-scroll-up]');
PluginManager.register('CollapseDetailPanelPlugin', CollapseDetailPanelPlugin);
PluginManager.register('LocationReviewForm', LocationReviewFormPlugin, '[data-location-review-form]');
//PluginManager.register('ShopReviewWidget', ShopReviewWidgetPlugin, '[data-shop-review-widget]');
PluginManager.register('NewsletterSubscriptionCheckbox', NewsletterSubscriptionCheckboxPlugin, '[data-newsletter-subscription-checkbox]');
PluginManager.register('LazyLoadGalleryImagesPlugin', LazyLoadGalleryImagesPlugin, '[data-lazy-load-gallery-images]');
PluginManager.register('UploadReviewImagePlugin', UploadReviewImagePlugin, '[data-customer-product-upload]');
PluginManager.register('RepairReviewImagePlugin', RepairReviewImagePlugin, '[data-customer-repair-review-upload]');
PluginManager.register('ProductQuantity', ProductQuantityPlugin, '[data-product-quantity]');
PluginManager.override('FormAjaxSubmit', FormAjaxSubmitExtendPlugin, '[data-form-ajax-submit]');
PluginManager.override('FormAjaxSubmit', FixFormAjaxSubmitPlugin, '[data-form-ajax-submit]');
PluginManager.register('CrossSellingProductSwitcher', CrossSellingProductSwitcherPlugin, '[data-cross-selling-product-switcher]');
PluginManager.register('StockSubscriberPlugin', StockSubscriberPlugin, '#form-product-stock-subscriber');
PluginManager.register('ScrollToReviewPlugin', ScrollToReviewPlugin, '[data-scroll-to-review]');
PluginManager.register('VolumePriceSelectPlugin', VolumePriceSelectPlugin, '[data-volume-price-selection="true"]');
// PluginManager.register('Appliance', AppliancePlugin, '[data-appliance]');
PluginManager.register('Appliance', AppliancePlugin, '.product-detail-reviews-rating');
PluginManager.override('FormValidation', FormValidationExtendPlugin, '[data-form-validation]');
PluginManager.override('FormSubmitLoader', FormSubmitLoaderExtendPlugin, '[data-form-submit-loader]');
PluginManager.register('RatingSystem', RatingSystemPlugin, '[data-rating-system]');
PluginManager.register('SwagPayPalExpressButton', SwagPayPalExpressCheckoutButton, '[data-swag-paypal-express-button]',);
PluginManager.register('SwagPayPalInstallmentBanner', SwagPayPalInstallmentBanner, '[data-swag-paypal-installment-banner]',);

PluginManager.register('RepairReviewSubmit', RepairReviewSubmit, '[data-repair-review-form]');
PluginManager.register('RepairReviewList', RepairReviewList, '[data-repair-review]');
PluginManager.register('SearchWidgetPlugin', SearchWidgetPlugin, '[data-search-widget]');
PluginManager.register('FooterWhatsAppPlugin', FooterWhatsAppPlugin, '[data-footer-whatsapp]');
PluginManager.register('ScrollToGprsPlugin', ScrollToGpsrPlugin, '[data-scroll-to-gpsr]');

// PluszweiBackInStockReminder
PluginManager.register('BackInStockReminder', BackInStockReminderPlugin, '[data-pluszwei-back-in-stock-reminder-submit]');

// PluszweiConversionBooster
PluginManager.register('PluszweiUpsellProductCartPlugin', UpsellProductCartPlugin, '[data-upsell-product-cart-slider-plugin]');
PluginManager.override('OffCanvasCart', OffCanvasCartPlugin, '[data-offcanvas-cart]');

try {
    // In case of the plugin doesn't active
    PluginManager.override('FlinkCmsAnchor', FlinkAnchorPlugin, '[href^="#"]');
} catch (e) {
}
