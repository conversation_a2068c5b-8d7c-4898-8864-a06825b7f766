import Plugin from 'src/plugin-system/plugin.class';

export default class AllBrandsPlugin extends Plugin {
    init() {
        this.registerEventListeners();
        this.applyFilter('a');
    }

    registerEventListeners() {
        document.querySelectorAll('.ln-filter').forEach(filter => {
            filter.addEventListener('click', (event) => {
                event.preventDefault();
                const letter = event.currentTarget.dataset.letter;
                if (!event.currentTarget.classList.contains('ln-disabled')) {
                    this.applyFilter(letter);
                }
            });
        });
    }

    applyFilter(letter) {
        document.querySelectorAll('.all-brands li').forEach(item => {
            if (item.classList.contains(`ln-${letter}`)) {
                item.classList.remove('d-none');
            } else {
                item.classList.add('d-none');
            }
        });
        const allBrands = document.querySelector('.all-brands');
        if (allBrands.classList.contains('d-none')) {
            allBrands.classList.remove('d-none');
        }
        const currentSelected = document.querySelector('a.ln-filter.ln-selected');
        if (currentSelected) {
            currentSelected.classList.remove('ln-selected');
        }
        const newSelected = document.querySelector(`a[data-letter="${letter}"]`);
        if (newSelected) {
            newSelected.classList.add('ln-selected');
        }
    }
}