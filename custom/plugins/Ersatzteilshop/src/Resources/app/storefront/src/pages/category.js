/**
 * AcrisCookieConsent
 * CookieConfiguration
 * OverrideAcrisCookieConsent
 * CookiePlugin
 * OverrideCookiePlugin
 *
 * GcrBadgeWidget
 *
 * already attached in cr.js
 */

import "../shopware-common";
import "../shopware-slider";

// Shopware
import ListingPaginationPlugin from 'src/plugin/listing/listing-pagination.plugin';
import ListingSortingPlugin from 'src/plugin/listing/listing-sorting.plugin';
import ListingPlugin from 'src/plugin/listing/listing.plugin';

// AcrisCookieConsentCS
import AcrisCookieConsentPlugin from 'AcrisCookieConsentCS/plugin/acris-cookie-consent/acris-cookie-consent.plugin';
import CookieConfigurationPlugin from 'AcrisCookieConsentCS/plugin/cookie/cookie-configuration.plugin';

// XantenGoogleCustomerReviews
import GcrBadgeWidgetPlugin from "XantenGoogleCustomerReviews/plugin/gcr-badge-widget.plugin";

// WbmTagManagerEcomm
import WbmDataLayer from "WbmTagManagerEcomm/plugin/datalayer.plugin";
import ProductClickTracking from 'WbmTagManagerEcomm/plugin/productClickTracking.plugin';
import Promotions from 'WbmTagManagerEcomm/plugin/promotions.plugin';
import 'WbmTagManagerEcomm/listener/windowLoad.listener';
import 'WbmTagManagerEcomm/service/http-client.service';
import 'WbmTagManagerEcomm/subscriber/cookieConfigurationUpdate.subscriber';

// Ersatzteilshop
import 'Ersatzteilshop/js/custom';
import 'Ersatzteilshop/js/trustshop';
import LocationReviewFormPlugin from "Ersatzteilshop/plugin/shop-review/location-review-form.plugin";
import AllBrandsPlugin from "Ersatzteilshop/plugin/category/all-brands.plugin";
//import ShopReviewWidgetPlugin from "Ersatzteilshop/plugin/shop-review/shop-review-widget.plugin";
import NewsletterSubscriptionCheckboxPlugin from "Ersatzteilshop/plugin/newsletter/subscription-checkbox.plugin";
import ScrollUpExtendPlugin from "Ersatzteilshop/plugin/widget/scroll-up-extend.plugin";
import ErrorGroupSearchWidgetPlugin from "Ersatzteilshop/plugin/widget/error-group-search-widget.plugin";
import SearchWidgetPlugin from 'Ersatzteilshop/plugin/widget/search-widget.plugin';
import FlinkAnchorPlugin from "Ersatzteilshop/plugin/anchor/flink-anchor.plugin";
import CollapseDetailPanelPlugin from 'Ersatzteilshop/plugin/collapse/collapse-detail-panel';
import SearchWidgetMobilePlugin from 'Ersatzteilshop/plugin/widget/search-widget-mobile.plugin';
import TopBrandsWidgetPlugin from "Ersatzteilshop/plugin/widget/topbrands-widget.plugin";
import VideoSliderPlugin from "Ersatzteilshop/plugin/widget/video-slider.plugin";
import ErsatzteilshopListingPlugin from "Ersatzteilshop/plugin/listing/ersatzteilshop-listing.plugin";
import ListingLimitPlugin from "Ersatzteilshop/plugin/listing/listing-limit.plugin";
import FollowAnchorPlugin from "Ersatzteilshop/plugin/anchor/follow-anchor.plugin";
import FooterWhatsAppPlugin from "Ersatzteilshop/plugin/footer/footer-whatsapp.plugin";

import AswoListingPaginationPlugin from "Ersatzteilshop/plugin/listing/aswo-listing-pagination.plugin";
import OffCanvasCartPlugin from 'Ersatzteilshop/plugin/offcanvas-cart/offcanvas-cart.plugin';

// FlinkCmsAnchor
import FlinkCmsAnchor from 'FlinkCmsAnchor/js/anchor';
import FlinkCmsAnchorDotNav from 'FlinkCmsAnchor/js/dotnav';

// PluszweiConversionBooster
// import PopupOffCanvasCartPlugin from "PluszweiConversionBooster/popup-offcanvas/popup-offcanvas-cart.plugin";
import UpsellProductCartPlugin from "PluszweiConversionBooster/upsell-product-cart/upsell-product-cart.plugin";


// Shopware
PluginManager.register('FollowAnchor', FollowAnchorPlugin, '[data-follow-anchor]');
PluginManager.register('Listing', ListingPlugin, '[data-listing]');
PluginManager.register('ListingPagination', ListingPaginationPlugin, '[data-listing-pagination]');
PluginManager.register('ListingSorting', ListingSortingPlugin, '[data-listing-sorting]');

// AcrisCookieConsentCS
PluginManager.register('AcrisCookieConsent', AcrisCookieConsentPlugin, '[data-acris-cookie-consent]');
PluginManager.register('CookieConfiguration', CookieConfigurationPlugin, '[data-acris-cookie-consent]');

// XantenGoogleCustomerReviews
PluginManager.register('GcrBadgeWidget', GcrBadgeWidgetPlugin, '[data-gcr-badge-widget]');

// WbmTagManagerEcomm
PluginManager.register('WbmDataLayer', WbmDataLayer);
PluginManager.register('ProductClickTracking', ProductClickTracking, '.product-box a', { parent: '.product-box' });
PluginManager.register('ProductClickTracking', ProductClickTracking, '.product-box button', { parent: '.product-box' });
PluginManager.register('Promotions', Promotions);
// Necessary for the webpack hot module reloading server
if (module.hot) {
    module.hot.accept();
}

// Ersatzteilshop
PluginManager.override('ScrollUp', ScrollUpExtendPlugin, '[data-scroll-up]');
PluginManager.register('CollapseDetailPanelPlugin', CollapseDetailPanelPlugin);
PluginManager.register('LocationReviewForm', LocationReviewFormPlugin, '[data-location-review-form]');
//PluginManager.register('ShopReviewWidget', ShopReviewWidgetPlugin, '[data-shop-review-widget]');
PluginManager.register('NewsletterSubscriptionCheckbox', NewsletterSubscriptionCheckboxPlugin, '[data-newsletter-subscription-checkbox]');
PluginManager.register('ErrorGroupSearchWidgetPlugin', ErrorGroupSearchWidgetPlugin, '[data-error-group-search]');
PluginManager.register('SearchWidgetPlugin', SearchWidgetPlugin, '[data-search-widget]');
PluginManager.register('SearchWidgetMobilePlugin', SearchWidgetMobilePlugin, '[data-search-mobile]');
PluginManager.register('TopBrandsWidgetPlugin', TopBrandsWidgetPlugin, '[data-topbrands-widget]');
PluginManager.register('VideoSliderPlugin', VideoSliderPlugin, '[data-video-slider]');
PluginManager.override('Listing', ErsatzteilshopListingPlugin, '[data-listing]');
PluginManager.register('AswoListingPagination', AswoListingPaginationPlugin, '[data-aswo-listing-pagination]');
PluginManager.register('ListingLimitPlugin', ListingLimitPlugin, '[data-listing-limit]');
PluginManager.register('AllBrandsPlugin', AllBrandsPlugin, '[data-all-brands]');
PluginManager.register('FooterWhatsAppPlugin', FooterWhatsAppPlugin, '[data-footer-whatsapp]');

// PluszweiConversionBooster
PluginManager.register('PluszweiUpsellProductCartPlugin', UpsellProductCartPlugin, '[data-upsell-product-cart-slider-plugin]');
PluginManager.override('OffCanvasCart', OffCanvasCartPlugin, '[data-offcanvas-cart]');

try {
    // In case of the plugin doesn't active
    PluginManager.override('FlinkCmsAnchor', FlinkAnchorPlugin, '[href^="#"]');

} catch (e) {}

// FlinkCmsAnchor
PluginManager.register('FlinkCmsAnchorDotNav', FlinkCmsAnchorDotNav, '[data-flink-cms-anchor-dotnav="true"]');
