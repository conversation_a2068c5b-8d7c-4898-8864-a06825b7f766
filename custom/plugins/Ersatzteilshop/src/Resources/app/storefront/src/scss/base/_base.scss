/*
Base
==================================================
Contains global base styles for all pages
*/

html {
  font-size: $font-size-base;
}

body {
  -webkit-font-smoothing: inherit;
  text-rendering: optimizeLegibility;
}

h1,
.h1 {
  font-size: $h1-font-size;
  margin: 20px 0;
  line-height: 100%;
}

h2,
.h2 {
  font-size: $h2-font-size;
}
h3,
.h3 {
  font-size: $h3-font-size;
}
h4,
.h4 {
  font-size: $h4-font-size;
}
h5,
.h5 {
  font-size: $h5-font-size;
}
h6,
.h6 {
  font-size: $h6-font-size;
}

a {
  color: #37ad27;
}

a:hover {
  color: $link-hover-color;
}

.legend {
  border-bottom: 1px solid #ccc;
  padding-bottom: 5px;
  line-height: 27px;
  font-weight: 700;
}



.icon-review {
  color: #FDBB33;
}
