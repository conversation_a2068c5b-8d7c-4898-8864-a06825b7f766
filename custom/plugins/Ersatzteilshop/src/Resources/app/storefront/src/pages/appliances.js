import "../shopware-common";
import "../shopware-slider";

/**
 * AcrisCookieConsentCS
 */
import AcrisCookieConsentPlugin from 'AcrisCookieConsentCS/plugin/acris-cookie-consent/acris-cookie-consent.plugin';
import CookieConfigurationPlugin from 'AcrisCookieConsentCS/plugin/cookie/cookie-configuration.plugin';

window.PluginManager.register('AcrisCookieConsent', AcrisCookieConsentPlugin, '[data-acris-cookie-consent]');
window.PluginManager.register('CookieConfiguration', CookieConfigurationPlugin, '[data-acris-cookie-consent]');

/**
 * XantenGoogleCustomerReviews
 */
import GcrBadgeWidgetPlugin from "XantenGoogleCustomerReviews/plugin/gcr-badge-widget.plugin";

PluginManager.register('GcrBadgeWidget', GcrBadgeWidgetPlugin, '[data-gcr-badge-widget]');

/**
 * Ersatzteilshop
 */
import 'Ersatzteilshop/js/custom';
import 'Ersatzteilshop/js/trustshop';

import CollapseDetailPanelPlugin from 'Ersatzteilshop/plugin/collapse/collapse-detail-panel';
import LocationReviewFormPlugin from "Ersatzteilshop/plugin/shop-review/location-review-form.plugin";
//import ShopReviewWidgetPlugin from "Ersatzteilshop/plugin/shop-review/shop-review-widget.plugin";
import NewsletterSubscriptionCheckboxPlugin from "Ersatzteilshop/plugin/newsletter/subscription-checkbox.plugin";
import ScrollUpExtendPlugin from "Ersatzteilshop/plugin/widget/scroll-up-extend.plugin";
import OffCanvasCartPlugin from 'Ersatzteilshop/plugin/offcanvas-cart/offcanvas-cart.plugin';

// PluszweiConversionBooster
// import PopupOffCanvasCartPlugin from "PluszweiConversionBooster/popup-offcanvas/popup-offcanvas-cart.plugin";
import UpsellProductCartPlugin from "PluszweiConversionBooster/upsell-product-cart/upsell-product-cart.plugin";
import ApplianceTabFilterPlugin from "../plugin/appliance/appliance-tab-filter.plugin";

PluginManager.override('ScrollUp', ScrollUpExtendPlugin, '[data-scroll-up]');
PluginManager.register('CollapseDetailPanelPlugin', CollapseDetailPanelPlugin);
PluginManager.register('LocationReviewForm', LocationReviewFormPlugin, '[data-location-review-form]');
//PluginManager.register('ShopReviewWidget', ShopReviewWidgetPlugin, '[data-shop-review-widget]');
PluginManager.register('NewsletterSubscriptionCheckbox', NewsletterSubscriptionCheckboxPlugin, '[data-newsletter-subscription-checkbox]');
PluginManager.register('ApplianceTabFilterPlugin', ApplianceTabFilterPlugin, '[data-appliance-tab-filter]');

// PluszweiConversionBooster
PluginManager.register('PluszweiUpsellProductCartPlugin', UpsellProductCartPlugin, '[data-upsell-product-cart-slider-plugin]');
// PluginManager.override('OffCanvasCart', PopupOffCanvasCartPlugin, '[data-offcanvas-cart]');
PluginManager.override('OffCanvasCart', OffCanvasCartPlugin, '[data-offcanvas-cart]');
