table#appliance-result {

  tr {
    cursor: pointer;
  }

  tr:hover td {
    background-color: #fff7d7;
  }

  td, th {
    padding: 20px 20px 20px 0;
    @include media-breakpoint-down(md) {
      padding: 10px 0;
    }
    width: 5%;
  }

  th {
    background-color: #fff;
    color: #222f3e;
    border-top: none;
  }

  td {
    border-bottom: 1px solid #ccc;
    @include media-breakpoint-down(md) {
      max-width: 125px;
    }
  }

  .mb {
    display: none !important;
  }

  .pc {
    display: table !important;
  }

  a.appliance-link {
    text-decoration: underline;
    color: #44b436;
  }

  .hide-pc {
    display: none !important;
  }

  .hide-mb {
    display: table-cell !important;
  }
  .table-td-inlined-break {
    color:black;
    display: inline-block;
    overflow-wrap: break-word;
    @include media-breakpoint-down(md) {
      display: inline-block !important;
      max-width: 110px;
    }
  }
  @include media-breakpoint-down(md) {
    th {
      &.action-btn {
        width: 150px;
      }
    }
    .hide-mb {
      display: none !important;
    }
    .hide-pc {
      display: table-cell !important;
    }
  }
}

.appliance-search-info {
  background: #f3d49a;
  padding: 20px 20px 20px 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 15px;
  &--icon {
    img {
      width: 70px;
      @include media-breakpoint-down(md) {
        width: 40px;
      }
    }
  }
  &--text {
    margin-left: 5px;
  }
}

.error-search-info {
  a.fill-div {
    display: block;
    height: 100%;
    width: 100%;
    text-decoration: none;
  }
  margin-top: 2rem;
  background: #FFD446;
  border-radius: 8px;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 40px;
  &--icon {
    justify-content: center;
    display: flex;
    img {
      width: 70px;
      @include media-breakpoint-down(md) {
        width: 40px;
      }
    }
  }
  &--text {
    span {
      font-weight:bold;
      text-decoration: underline;
      color: #37ad27;

    }
    margin-left: 5px;
    color: black;
    @include media-breakpoint-down(md) {
      text-align: center
    }
  }
}

.product-appliances .dataTable-top {
  padding-left: 0;
  padding-right: 0;
  .dataTable-input {
    width: 400px;
    @include media-breakpoint-down(md) {
      width: auto;
    }
  }
  .dataTable-search {
    @include media-breakpoint-down(md) {
      width: 100%;
    }
  }
}
