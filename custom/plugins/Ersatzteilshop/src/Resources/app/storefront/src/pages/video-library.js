import "../shopware-common";
import AcrisCookieConsentPlugin from 'AcrisCookieConsentCS/plugin/acris-cookie-consent/acris-cookie-consent.plugin';
import CookieConfigurationPlugin from 'AcrisCookieConsentCS/plugin/cookie/cookie-configuration.plugin';
import GcrBadgeWidgetPlugin from "XantenGoogleCustomerReviews/plugin/gcr-badge-widget.plugin";
import WbmDataLayer from "WbmTagManagerEcomm/plugin/datalayer.plugin";
import 'WbmTagManagerEcomm/listener/windowLoad.listener';
import 'WbmTagManagerEcomm/service/http-client.service';
import 'WbmTagManagerEcomm/subscriber/cookieConfigurationUpdate.subscriber';
import 'Ersatzteilshop/js/custom';
import 'Ersatzteilshop/js/trustshop';
import VideoLibraryOverviewPlugin from "../plugin/video-library/video-library-overview.plugin";
import VideoLibraryYoutubePlayerPlugin from "../plugin/video-library/video-library-youtube-player.plugin";
import ScrollUpExtendPlugin from "Ersatzteilshop/plugin/widget/scroll-up-extend.plugin";
import SearchWidgetPlugin from 'Ersatzteilshop/plugin/widget/search-widget.plugin';
import CollapseDetailPanelPlugin from 'Ersatzteilshop/plugin/collapse/collapse-detail-panel';
import SearchWidgetMobilePlugin from 'Ersatzteilshop/plugin/widget/search-widget-mobile.plugin';
import VideoSliderPlugin from "Ersatzteilshop/plugin/widget/video-slider.plugin";
// import PopupOffCanvasCartPlugin from "PluszweiConversionBooster/popup-offcanvas/popup-offcanvas-cart.plugin";
import OffCanvasCartPlugin from 'Ersatzteilshop/plugin/offcanvas-cart/offcanvas-cart.plugin';
import UpsellProductCartPlugin from "PluszweiConversionBooster/upsell-product-cart/upsell-product-cart.plugin";

PluginManager.register('AcrisCookieConsent', AcrisCookieConsentPlugin, '[data-acris-cookie-consent]');
PluginManager.register('CookieConfiguration', CookieConfigurationPlugin, '[data-acris-cookie-consent]');
PluginManager.register('GcrBadgeWidget', GcrBadgeWidgetPlugin, '[data-gcr-badge-widget]');
PluginManager.register('WbmDataLayer', WbmDataLayer);
PluginManager.override('ScrollUp', ScrollUpExtendPlugin, '[data-scroll-up]');
PluginManager.register('CollapseDetailPanelPlugin', CollapseDetailPanelPlugin);
PluginManager.register('SearchWidgetPlugin', SearchWidgetPlugin, '[data-search-widget]');
PluginManager.register('SearchWidgetMobilePlugin', SearchWidgetMobilePlugin, '[data-search-mobile]');
PluginManager.register('VideoSliderPlugin', VideoSliderPlugin, '[data-video-slider]');
PluginManager.register('VideoLibraryOverviewPlugin', VideoLibraryOverviewPlugin, '[data-attribute-video-library]');
PluginManager.register('VideoLibraryYoutubePlayerPlugin', VideoLibraryYoutubePlayerPlugin, '[data-has-chapters]');
PluginManager.register('PluszweiUpsellProductCartPlugin', UpsellProductCartPlugin, '[data-upsell-product-cart-slider-plugin]');
PluginManager.override('OffCanvasCart', OffCanvasCartPlugin, '[data-offcanvas-cart]');

if (module.hot) {
    module.hot.accept();
}
