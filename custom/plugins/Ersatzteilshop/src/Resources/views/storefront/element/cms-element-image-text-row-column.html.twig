{% block element_image_text_row_column %}
    {% set elementConfig = element.fieldConfig.elements %}
    {% set colClass = 'col-md' %}
    {% if elementConfig.displayMode.value == 'vertical'  %}
        {% set colClass = 'col-md-12' %}
    {% endif %}
    {% set colClass = colClass ~ ' text-' ~ elementConfig.imageAlign.value %}
    {% set colClass = colClass ~ ' text-' ~ elementConfig.textPosition.value %}

    <div class="cms-element-image-text-row-column">
        <div class="container">
            <div class="row">
                {% for image in element.data.imageItems %}
                    <div class="{{ colClass }} cms-element-image-text-row-column-item">
                        {% set attributes = {
                            'class': 'img-fluid cms-element-image-text-row-column-image',
                            'alt': (image.media.translated.alt ?: ''),
                            'title': (image.media.translated.title ?: '')
                        } %}
                        {% sw_thumbnails 'cms-image-text-thumbnails' with {
                            media: image.media
                        } %}
                        <div class="cms-element-image-text-row-column-text">
                            {{ image.text|raw }}
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>
{% endblock %}
