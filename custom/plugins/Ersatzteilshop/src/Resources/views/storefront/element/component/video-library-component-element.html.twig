{% block element_video %}
    <div style="position: relative; display: inline-block;" data-youtube-id="{{ youtubeId }}">
{#            <div itemscope itemtype="https://schema.org/VideoObject">#}
{#                <meta itemprop="thumbnailUrl" content="https://img.youtube.com/vi_webp/{{ youtubeId }}/{{ overview ? 'hqdefault' : 'maxresdefault' }}.webp"/>#}
{#                <meta itemprop="name" content="{{ video.translated.title }}"/>#}
{#                <meta itemprop="uploadDate" content="{{ video.createdAt ? video.createdAt|date : '' }}"/>#}
{#                <meta itemprop="embedUrl" content="https://www.youtube-nocookie.com/embed/{{ youtubeId }}"/>#}
{#            </div>#}
            <div class="video-container" style="position: relative; width: {{ width }}px; height: {{ height }}px; background-color: #000; cursor: pointer;">
                <img class="youtube_thumbnail_overview" loading="{{ loading }}" src="https://img.youtube.com/vi_webp/{{ youtubeId }}/{{ overview ? 'hqdefault' : 'maxresdefault' }}.webp" alt="youtube_thumnail">
                <img loading="{{ loading }}" class='playbutton' src="{{ asset('bundles/ersatzteilshop/assets/img/play_black.svg') }}" alt="playbutton_logo"/>
            </div>
            <p class="video-paragraph k11-black-link">{{ video.translated.title }}</p>
            <a data-controller="prefetch" href="{{ seoUrl('frontend.video.library.page', {'videoLibraryId': video.id}) }}" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; z-index: 10; background: transparent; cursor: pointer;"></a>
    </div>
{% endblock %}
