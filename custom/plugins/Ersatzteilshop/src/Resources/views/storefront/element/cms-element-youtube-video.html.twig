{% sw_extends '@Storefront/storefront/element/cms-element-youtube-video.html.twig' %}

{% block element_youtube_video_inner %}
    {% set iframeClass = 'cms-element-youtube-video__video' %}

    {% set youtubeId = videoUrl|split('?')|first|split('/')|last %}

    {% set videoUrl = videoUrl ~ '&autoplay=1&modestbranding=1&iv_load_policy=3&theme=light&playsinline=1' %}

    {% if config.needsConfirmation.value == false %}
        <iframe
            class="{{ iframeClass }}"
            srcdoc="<style>*{padding:0;margin:0;overflow:hidden}html,body{background:#000;height:100%}img{position:absolute;width:100%;top:0;bottom:0;margin:auto}</style><a href={{ videoUrl }}><img src=https://img.youtube.com/vi/{{ youtubeId }}/maxresdefault.jpg><img id='playbutton' src='{{ asset('bundles/ersatzteilshop/assets/img/play.png') }}' style='width: 66px; position: absolute; left: 41.5%;'></a>"
            frameborder="0"
            allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
            allowfullscreen
            scrolling="no"
            loading="lazy"
            style="background-color: #000"
        ></iframe>
    {% else %}
        {% set pluginConfiguration = {
            videoUrl: videoUrl,
            iframeClasses: [ iframeClass ],
            btnClasses: [ 'btn', 'btn-outline-secondary' ],
            backdropClasses: [ 'cms-element-youtube-video__backdrop' ],
            confirmButtonText: 'component.cms.vimeo.acceptButtonLabel'|trans,
            overlayText: 'component.cms.vimeo.privacyNotice'|trans({
                '%url%': path('frontend.cms.page',{ id: config('core.basicInformation.privacyPage') }),
                '%platform%': 'YouTube'
            })|raw
        } %}

        {% block element_youtube_video_placeholder %}
            <div class="cms-element-youtube-video__placeholder"
                 data-cms-gdpr-video-element="true"
                 data-cms-gdpr-video-element-options="{{ pluginConfiguration|json_encode }}">

                {% if elementData.media %}
                    {% sw_thumbnails 'cms-element-youtube-video__placeholder' with {
                        media: elementData.media
                    } %}
                {% endif %}
            </div>
        {% endblock %}
    {% endif %}
{% endblock %}
