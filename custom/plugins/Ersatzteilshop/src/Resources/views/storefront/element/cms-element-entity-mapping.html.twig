{% block cms_element_entity_mapping %}
    {% set config = element.config %}
    {% set itemsPerRow = config.itemsPerRow.value | default(3) %}
    
    {% if element.data is defined %}
        {% set entityType   = element.data.entityType  | default(null) %}
        {% set searchResult = element.data.entities    | default(null) %}
    {% endif %}
    
    <div class="cms-element-entity-mapping is-grid k11-w-100">
        
        {% if searchResult is defined and searchResult.entities|length > 0 %}
            {% if entityType == 'product' %}
                <div>
                    {% for item in searchResult.elements %}
                        <div class="product-item-container k11-mb-20">
                            {% sw_include "@Ersatzteilshop/storefront/component/product/card/box-full.html.twig" with {
                                product: item,
                                layout:  'full'
                            } %}
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                {% if entityType == 'category' %}
                    <div class="entity-mapping-category-layout">
                        {% for item in searchResult.elements %}
                            <div class="category-layout-container">
                                <div class="category-layout-grid">
                                    <div class="category-image-section">
                                        <div class="category-image-placeholder">
                                            {% set categoryImage = item.extensions.categoryImage|default(null) %}
                                            {% if categoryImage %}
                                                <img src="{{ asset('bundles/ersatzteilshop/assets/icons/categories/' ~ categoryImage) }}" 
                                                     alt="{{ item.translated.name }}" 
                                                     width="171" 
                                                     height="171" />
                                            {% else %}
                                                <svg width="171" height="171" viewBox="0 0 171 171" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <rect width="171" height="171" fill="#E8E8E8" stroke="#D0D0D0" stroke-width="1"/>
                                                </svg>
                                            {% endif %}
                                            <div class="category-header d-block d-md-none">
                                                <div class="category-names-row">
                                                    <h3 class="category-name-main">
                                                        <a href="{{ seoUrl('frontend.navigation.page', {'navigationId': item.id}) }}" class="category-link">
                                                            {{ item.translated.name }}
                                                        </a>
                                                    </h3>
                                                    {% if item.level > 2 and item.parent %}
                                                        <div class="category-parent-inline">
                                                            {{ item.parent.name }}
                                                        </div>
                                                    {% endif %}
                                                </div>
                                                
                                                {# Mobile delivery badge - shown only on mobile #}
                                                <div class="delivery-badge delivery-badge-mobile">
                                                    <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <mask id="mask0_7100_3089_mobile" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="25">
                                                            <rect y="0.25" width="24" height="24" fill="#D9D9D9"/>
                                                        </mask>
                                                        <g mask="url(#mask0_7100_3089_mobile)">
                                                            <path d="M18 4.25V8.25H21L24 12.25V17.25H22C22 18.0833 21.7083 18.7917 21.125 19.375C20.5417 19.9583 19.8333 20.25 19 20.25C18.1667 20.25 17.4583 19.9583 16.875 19.375C16.2917 18.7917 16 18.0833 16 17.25H13C13 18.0833 12.7083 18.7917 12.125 19.375C11.5417 19.9583 10.8333 20.25 10 20.25C9.16667 20.25 8.45833 19.9583 7.875 19.375C7.29167 18.7917 7 18.0833 7 17.25H5V14.25H3V12.25H5V10.25H2V8.25H5V6.25H0V4.25H18ZM10 16.25C9.71667 16.25 9.47878 16.3454 9.28711 16.5371C9.09544 16.7288 9 16.9667 9 17.25C9 17.5333 9.09544 17.7712 9.28711 17.9629C9.47878 18.1546 9.71667 18.25 10 18.25C10.2833 18.25 10.5212 18.1546 10.7129 17.9629C10.9046 17.7712 11 17.5333 11 17.25C11 16.9667 10.9046 16.7288 10.7129 16.5371C10.5212 16.3454 10.2833 16.25 10 16.25ZM19 16.25C18.7167 16.25 18.4788 16.3454 18.2871 16.5371C18.0954 16.7288 18 16.9667 18 17.25C18 17.5333 18.0954 17.7712 18.2871 17.9629C18.4788 18.1546 18.7167 18.25 19 18.25C19.2833 18.25 19.5212 18.1546 19.7129 17.9629C19.9046 17.7712 20 17.5333 20 17.25C20 16.9667 19.9046 16.7288 19.7129 16.5371C19.5212 16.3454 19.2833 16.25 19 16.25ZM18 13.25H22.25L20 10.25H18V13.25Z" fill="#74CB7B"/>
                                                        </g>
                                                    </svg>
                                                    
                                                    <span>schnelle Lieferung</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="category-info-section">
                                        <div class="category-header d-none d-md-block">
                                            <div class="category-names-row">
                                                <h3 class="category-name-main">
                                                    <a href="{{ seoUrl('frontend.navigation.page', {'navigationId': item.id}) }}" class="category-link">
                                                        {{ item.translated.name }}
                                                    </a>
                                                </h3>
                                                {% if item.level > 2 and item.parent %}
                                                    <div class="category-parent-inline">
                                                        {{ item.parent.name }}
                                                    </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                        
                                        <div class="category-manufacturers">
                                            <div class="manufacturers-grid">
                                                {% set availableManufacturers = item.extensions.availableManufacturers|default([]) %}
                                                {% for manufacturer in availableManufacturers %}
                                                    <a href="{{ seoUrl('frontend.category.manufacturer.page', {'navigationId': item.id, 'manufacturerId': manufacturer.id}) }}" class="manufacturer-icon" title="{{ manufacturer.name }}">
                                                        <img src="{{ asset('bundles/ersatzteilshop/assets/icons/manufacturers/' ~ manufacturer.icon) }}" 
                                                             alt="{{ manufacturer.name }}" />
                                                    </a>
                                                {% endfor %}
                                                <a href="{{ seoUrl('frontend.navigation.page', {'navigationId': item.id}) }}" class="manufacturer-icon" title="Alle">
                                                    <img src="{{ asset('bundles/ersatzteilshop/assets/icons/manufacturers/alle.png') }}"
                                                         alt="{{ manufacturer.name }}" />
                                                </a>
                                            </div>
                                        </div>
                                        
                                        <div class="category-actions">
                                            <div class="action-buttons-grid">
                                                <div class="delivery-badge delivery-badge-desktop">
                                                    <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <mask id="mask0_7100_3089" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="25">
                                                            <rect y="0.25" width="24" height="24" fill="#D9D9D9"/>
                                                        </mask>
                                                        <g mask="url(#mask0_7100_3089)">
                                                            <path d="M18 4.25V8.25H21L24 12.25V17.25H22C22 18.0833 21.7083 18.7917 21.125 19.375C20.5417 19.9583 19.8333 20.25 19 20.25C18.1667 20.25 17.4583 19.9583 16.875 19.375C16.2917 18.7917 16 18.0833 16 17.25H13C13 18.0833 12.7083 18.7917 12.125 19.375C11.5417 19.9583 10.8333 20.25 10 20.25C9.16667 20.25 8.45833 19.9583 7.875 19.375C7.29167 18.7917 7 18.0833 7 17.25H5V14.25H3V12.25H5V10.25H2V8.25H5V6.25H0V4.25H18ZM10 16.25C9.71667 16.25 9.47878 16.3454 9.28711 16.5371C9.09544 16.7288 9 16.9667 9 17.25C9 17.5333 9.09544 17.7712 9.28711 17.9629C9.47878 18.1546 9.71667 18.25 10 18.25C10.2833 18.25 10.5212 18.1546 10.7129 17.9629C10.9046 17.7712 11 17.5333 11 17.25C11 16.9667 10.9046 16.7288 10.7129 16.5371C10.5212 16.3454 10.2833 16.25 10 16.25ZM19 16.25C18.7167 16.25 18.4788 16.3454 18.2871 16.5371C18.0954 16.7288 18 16.9667 18 17.25C18 17.5333 18.0954 17.7712 18.2871 17.9629C18.4788 18.1546 18.7167 18.25 19 18.25C19.2833 18.25 19.5212 18.1546 19.7129 17.9629C19.9046 17.7712 20 17.5333 20 17.25C20 16.9667 19.9046 16.7288 19.7129 16.5371C19.5212 16.3454 19.2833 16.25 19 16.25ZM18 13.25H22.25L20 10.25H18V13.25Z" fill="#74CB7B"/>
                                                        </g>
                                                    </svg>
                                                    
                                                    <span>schnelle Lieferung</span>
                                                </div>
                                                <div class="action-button purchase-button">
                                                    <a href="{{ seoUrl('frontend.navigation.page', {'navigationId': item.id}) }}" class="purchase-link">
                                                        <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <mask id="mask0_1651_11325" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="24">
                                                                <rect x="0.947266" width="24" height="24" fill="#D9D9D9"/>
                                                            </mask>
                                                            <g mask="url(#mask0_1651_11325)">
                                                                <path d="M7.94727 22C7.39727 22 6.92643 21.8042 6.53477 21.4125C6.1431 21.0208 5.94727 20.55 5.94727 20C5.94727 19.45 6.1431 18.9792 6.53477 18.5875C6.92643 18.1958 7.39727 18 7.94727 18C8.49727 18 8.9681 18.1958 9.35977 18.5875C9.75143 18.9792 9.94727 19.45 9.94727 20C9.94727 20.55 9.75143 21.0208 9.35977 21.4125C8.9681 21.8042 8.49727 22 7.94727 22ZM17.9473 22C17.3973 22 16.9264 21.8042 16.5348 21.4125C16.1431 21.0208 15.9473 20.55 15.9473 20C15.9473 19.45 16.1431 18.9792 16.5348 18.5875C16.9264 18.1958 17.3973 18 17.9473 18C18.4973 18 18.9681 18.1958 19.3598 18.5875C19.7514 18.9792 19.9473 19.45 19.9473 20C19.9473 20.55 19.7514 21.0208 19.3598 21.4125C18.9681 21.8042 18.4973 22 17.9473 22ZM6.14727 4H20.8973C21.2806 4 21.5723 4.17083 21.7723 4.5125C21.9723 4.85417 21.9806 5.2 21.7973 5.55L18.2473 11.95C18.0639 12.2833 17.8181 12.5417 17.5098 12.725C17.2014 12.9083 16.8639 13 16.4973 13H9.04727L7.94727 15H18.9473C19.2306 15 19.4681 15.0958 19.6598 15.2875C19.8514 15.4792 19.9473 15.7167 19.9473 16C19.9473 16.2833 19.8514 16.5208 19.6598 16.7125C19.4681 16.9042 19.2306 17 18.9473 17H7.94727C7.19727 17 6.6306 16.6708 6.24727 16.0125C5.86393 15.3542 5.84727 14.7 6.19727 14.05L7.54727 11.6L3.94727 4H2.94727C2.66393 4 2.42643 3.90417 2.23477 3.7125C2.0431 3.52083 1.94727 3.28333 1.94727 3C1.94727 2.71667 2.0431 2.47917 2.23477 2.2875C2.42643 2.09583 2.66393 2 2.94727 2H4.57227C4.7556 2 4.9306 2.05 5.09727 2.15C5.26393 2.25 5.38893 2.39167 5.47227 2.575L6.14727 4Z" fill="white"/>
                                                            </g>
                                                        </svg>
                                                        <span>JETZT KAUFEN</span>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="entity-mapping-items row">
                        {% for item in searchResult.elements %}
                            {% set columnClass = 'col-md-' ~ (12 / itemsPerRow)|round %}
                            <div class="entity-mapping-item {{ columnClass }}">
                                {% if entityType == 'manufacturer' %}
                                    <div class="manufacturer-card">
                                        <a href="{{ seoUrl('frontend.manufacturer.page', {'manufacturerId': item.id}) }}" class="manufacturer-link">
                                            <div class="manufacturer-name-wrapper">
                                                {% if item.media %}
                                                    <div class="manufacturer-image">
                                                        {% sw_thumbnails 'manufacturer-image-thumbnails' with {
                                                            media: item.media,
                                                            sizes: { 'default':'300px' }
                                                        } %}
                                                    </div>
                                                {% else %}
                                                    <div class="manufacturer-name">
                                                        {{ item.translated.name }}
                                                    </div>
                                                {% endif %}
                                            </div>
                                        </a>
                                    </div>
                                {% endif %}
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
            {% endif %}
        {% endif %}
    </div>
{% endblock %}