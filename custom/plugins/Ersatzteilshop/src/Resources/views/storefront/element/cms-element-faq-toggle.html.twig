{% block element_faq_toggle %}
    <div class="sw-cms-el-toggles">
        <div class="cms-author-container">
            {% if element.data %}
                {% set faqToggle = element.data %}
                
                <div class="panel hide">
                    <h2>
                        <a href="#"
                           class="panel-head"
                           style="font-size: {{ faqToggle.headingFontSize }}px;">
                            {{ faqToggle.heading }}
                            <svg class="toggle-icon" width="12" height="17" viewBox="0 0 12 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M2.26562 2.01172L8.67575 8.42184L2.26563 14.832" stroke="#202E3D" stroke-width="3.84607"/>
                            </svg>
                        </a>
                    </h2>
                    
                    <div class="panel-body product-detail-property">
                        <div class="product-detail-properties">
                            <div class="row product-detail-properties-container">
                                <div class="col-12">
                                    {% for item in faqToggle.faqItems %}
                                        <h3>{{ item.question }}</h3>
                                        <div>{{ item.answer|raw }}</div>
                                        {% if not loop.last %}<div><br></div>{% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <script type="application/ld+json">
                    {{ faqToggle.schemaData|json_encode|raw }}
                </script>
            {% else %}
                <div class="panel hide">
                    <h2>
                        <a href="#"
                           class="panel-head"
                           style="font-size: {{ element.config.headingFontSize.value }}px;">
                            {{ element.config.heading.value }}
                            <svg class="toggle-icon" width="12" height="17" viewBox="0 0 12 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M2.26562 2.01172L8.67575 8.42184L2.26563 14.832" stroke="#202E3D" stroke-width="3.84607"/>
                            </svg>
                        </a>
                    </h2>
                    <div class="panel-body product-detail-property">
                        <div class="product-detail-properties">
                            <div class="row product-detail-properties-container">
                                <div class="col-12">
                                    {% if element.config.faqItems.value %}
                                        {% for item in element.config.faqItems.value %}
                                            <h3>{{ item.question }}</h3>
                                            <div>{{ item.answer|raw }}</div>
                                            {% if not loop.last %}<div><br></div>{% endif %}
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                {% if element.config.faqItems.value %}
                    <script type="application/ld+json">
                    {
                        "@context": "https://schema.org",
                        "@type": "FAQPage",
                        "name": "FAQ",
                        "mainEntity": [
                            {% for item in element.config.faqItems.value %}
                            {
                                "@type": "Question",
                                "name": "{{ item.question|replace({'"': '\\"'})|raw }}",
                                "acceptedAnswer": {
                                    "@type": "Answer",
                                    "text": "{{ item.answer|striptags|replace({'"': '\\"'})|raw }}"
                                }
                            }{% if not loop.last %},{% endif %}
                            {% endfor %}
                        ]
                    }
                    </script>
                {% endif %}
            {% endif %}
        </div>
    </div>
{% endblock %}
