{% block element_video_library_component %}
    {% set deviceType = app.request.headers.get('User-Agent')|lower %}
    {% if 'mobile' in deviceType %}
        {% set height = '210' %}
        {% set width = '380' %}
    {% else %}
        {% set height = '165' %}
        {% set width = '300' %}
    {% endif %}
    
    {% set compareCmsPageId = page.cmsPage.id is null ? page.videoLibrary.cmsPageId : page.cmsPage.id %}
    {% set sortedQuestions = element.data.videos|sort((a, b) => (b.cmsPageId ? 1 : 0) - (a.cmsPageId ? 1 : 0)) %}
    {% set questionsWithCmsAndUrl = sortedQuestions|filter(video => video.cmsPageId and video.cmsPageId != compareCmsPageId) %}
    {% set questionsNoCmsButUrl = sortedQuestions|filter(video => not video.cmsPageId and video.url) %}
    {% set questionsNoCmsNoUrl  = sortedQuestions|filter(video => not video.cmsPageId and not video.url) %}
    {% if sortedQuestions|length > 5 %}
        <div class="k11-mb-20" data-video-search-container>
            <div class="related-videos__search">
                <form class="k11-border-active d-flex align-items-center k11-w-85 k11-md-w-100">
                    <input type="search"
                           class="input-group search-input border-0 k11-border-radius-8 k11-pl-15 form-control"
                           placeholder="Beitrag suchen..."
                           data-video-search-input>
                    <div class="svg-container">
                        <button class="search-input--btn border-0 p-0 outline-none bg-none"
                                type="button"
                                data-video-search-btn
                                disabled>
                            <svg class="k11-p-2" width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="34" height="34" rx="5" fill="#202E3D"/>
                                <path d="M5 27.1333L13.4 18.7333C12.8667 18.0667 12.4444 17.3 12.1333 16.4333C11.8222 15.5667 11.6667 14.6444 11.6667 13.6667C11.6667 11.2444 12.5056 9.19444 14.1833 7.51667C15.8611 5.83889 17.9111 5 20.3333 5C22.7556 5 24.8056 5.83889 26.4833 7.51667C28.1611 9.19445 29 11.2444 29 13.6667C29 16.0889 28.1611 18.1389 26.4833 19.8167C24.8056 21.4944 22.7556 22.3333 20.3333 22.3333C19.3556 22.3333 18.4333 22.1778 17.5667 21.8667C16.7 21.5556 15.9333 21.1333 15.2667 20.6L6.86667 29L5 27.1333ZM14.3333 13.6667C14.3333 15.3333 14.9167 16.75 16.0833 17.9167C17.25 19.0833 18.6667 19.6667 20.3333 19.6667C22 19.6667 23.4167 19.0833 24.5833 17.9167C25.75 16.75 26.3333 15.3333 26.3333 13.6667C26.3333 12 25.75 10.5833 24.5833 9.41667C23.4167 8.25 22 7.66667 20.3333 7.66667C18.6667 7.66667 17.25 8.25 16.0833 9.41667C14.9167 10.5833 14.3333 12 14.3333 13.6667Z" fill="white"/>
                            </svg>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    {% endif %}
    
    
    {# --- questionsWithCmsAndUrl --- #}
    {% if questionsWithCmsAndUrl|length > 0 %}
        <div class="k11-my-40" data-video-group="questionsWithCmsAndUrl">
            <p class="k11-font-size-18 k11-font-weight-500 k11-mb-10" data-video-group-heading>Relevanteste Beiträge</p>
            <div class="d-grid k11-gap-20 k11-md-gap-25 k11-grid-template-1 k11-md-grid-template-3"
                 data-video-group-items>
                {% for videoId, video in questionsWithCmsAndUrl %}
                    {% set manufacturerList = [] %}
                    {% if video.translated.manufacturers is defined and video.translated.manufacturers %}
                        {% for manufacturer in video.translated.manufacturers %}
                            {% set manufacturerList = manufacturerList|merge([manufacturer]) %}
                        {% endfor %}
                    {% endif %}
                    {% set applianceList = [] %}
                    {% if video.translated.appliances is defined and video.translated.appliances %}
                        {% for appliance in video.translated.appliances %}
                            {% set applianceList = applianceList|merge([appliance]) %}
                        {% endfor %}
                    {% endif %}
                    <div class="video-item searchable {% if loop.index > 6 %}d-none{% endif %}"
                         data-video-item
                         data-attribute-video="{{ applianceList|join(' ') }}"
                         data-attribute-type="{{ video.videoType }}"
                         data-attribute-manufacturer="{{ manufacturerList|join(' ') }}">
                        <div class="question-input-container">
                            <a class="k11-black-link k11-font-weight-500"
                               href="{{ seoUrl('frontend.video.library.page', {'videoLibraryId': video.id}) }}">
                                <u>{{ video.questionInput ?: video.title }}</u>
                            </a>
                        </div>
                    </div>
                {% endfor %}
                
                {% if questionsWithCmsAndUrl|length > 6 %}
                    <div class="related-videos__actions" data-video-load-more>
                        <button class="load-more-videos" data-load-more>Mehr anzeigen</button>
                    </div>
                {% endif %}
            </div>
        </div>
    {% endif %}
    
    {# --- questionsNoCmsButUrl --- #}
    {% if questionsNoCmsButUrl|length > 0 %}
        <div class="k11-my-40" data-video-group="questionsNoCmsButUrl">
            <p class="k11-font-size-18 k11-font-weight-500 k11-mb-10" data-video-group-heading>Unsere Video-Beiträge</p>
            <div class="d-grid k11-gap-20 k11-md-gap-25 k11-grid-template-1 k11-md-grid-template-3"
                 data-video-group-items>
                {% for videoId, video in questionsNoCmsButUrl %}
                    {% if element.config.showWQuestions.value %}
                        <div class="video-item searchable {% if loop.index > 6 %}d-none{% endif %}"
                             data-video-item
                             data-attribute-video="{% if video.translated.appliances is defined and video.translated.appliances %}{{ video.translated.appliances|join(' ') }}{% else %}{{ video.appliance|default('') }}{% endif %}"
                             data-attribute-type="{{ video.videoType }}"
                             data-attribute-manufacturer="{% set manufacturerList = [] %}{% if video.translated.manufacturers is defined and video.translated.manufacturers %}{% for manufacturer in video.translated.manufacturers %}{% set manufacturerList = manufacturerList|merge([manufacturer]) %}{% endfor %}{% endif %}{{ manufacturerList|join(' ') }}">
                            <div class="question-input-container">
                                <a class="k11-black-link k11-font-weight-500"
                                   href="{{ seoUrl('frontend.video.library.page', {'videoLibraryId': video.id}) }}">
                                    <u>{{ video.questionInput ?: video.title }}</u>
                                </a>
                            </div>
                        </div>
                    {% else %}
                        <div class="video-item searchable {% if loop.index > 6 %}d-none{% endif %}"
                             data-video-item
                             data-attribute-video="{% if video.translated.appliances is defined and video.translated.appliances %}{{ video.translated.appliances|join(' ') }}{% else %}{{ video.appliance|default('') }}{% endif %}"
                             data-attribute-type="{{ video.videoType }}"
                             data-attribute-manufacturer="{% set manufacturerList = [] %}{% if video.translated.manufacturers is defined and video.translated.manufacturers %}{% for manufacturer in video.translated.manufacturers %}{% set manufacturerList = manufacturerList|merge([manufacturer]) %}{% endfor %}{% endif %}{{ manufacturerList|join(' ') }}">
                            {% include '@Ersatzteilshop/storefront/element/component/video-library-component-element.html.twig' with {
                                width: width,
                                height: height,
                                youtubeId: video.youtubeId,
                                video: video,
                                overview: true,
                                loading: loading
                            } only %}
                        </div>
                    {% endif %}
                {% endfor %}
                
                {% if questionsNoCmsButUrl|length > 6 %}
                    <div class="related-videos__actions" data-video-load-more>
                        <button class="load-more-videos" data-load-more>Mehr anzeigen</button>
                    </div>
                {% endif %}
            </div>
        </div>
    {% endif %}
    
    {# --- questionsNoCmsNoUrl --- #}
    {% if questionsNoCmsNoUrl|length > 0 %}
        <div class="k11-my-40" data-video-group="questionsNoCmsNoUrl">
            <p class="k11-font-size-18 k11-font-weight-500 k11-mb-10" data-video-group-heading>Weitere Beiträge</p>
            <div class="d-grid k11-gap-20 k11-md-gap-25 k11-grid-template-1 k11-md-grid-template-3"
                 data-video-group-items>
                {% for videoId, video in questionsNoCmsNoUrl %}
                    {% set manufacturerList = [] %}
                    {% if video.translated.manufacturers is defined and video.translated.manufacturers %}
                        {% for manufacturer in video.translated.manufacturers %}
                            {% set manufacturerList = manufacturerList|merge([manufacturer]) %}
                        {% endfor %}
                    {% endif %}
                    {% set applianceList = [] %}
                    {% if video.translated.appliances is defined and video.translated.appliances %}
                        {% for appliance in video.translated.appliances %}
                            {% set applianceList = applianceList|merge([appliance]) %}
                        {% endfor %}
                    {% endif %}
                    <div class="video-item searchable {% if loop.index > 6 %}d-none{% endif %}"
                         data-video-item
                         data-attribute-video="{{ applianceList|join(' ') }}"
                         data-attribute-type="{{ video.videoType }}"
                         data-attribute-manufacturer="{{ manufacturerList|join(' ') }}">
                        <div class="question-input-container">
                            <a class="k11-black-link k11-font-weight-500"
                               href="{{ seoUrl('frontend.video.library.page', {'videoLibraryId': video.id}) }}">
                                <u>{{ video.questionInput ?: video.title }}</u>
                            </a>
                        </div>
                    </div>
                {% endfor %}
            </div>
            
            {% if questionsNoCmsNoUrl|length > 6 %}
                <div class="related-videos__actions" data-video-load-more>
                    <button class="load-more-videos" data-load-more>Mehr anzeigen</button>
                </div>
            {% endif %}
        </div>
    {% endif %}
{% endblock %}
