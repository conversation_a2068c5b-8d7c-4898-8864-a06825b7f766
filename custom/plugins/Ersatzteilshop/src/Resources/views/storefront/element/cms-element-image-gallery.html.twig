{% sw_extends '@Storefront/storefront/element/cms-element-image-gallery.html.twig' %}

{% block element_image_gallery_inner_item %}
    <div class="gallery-slider-item-container">
        <div class="gallery-slider-item is-{{ displayMode }} js-magnifier-container"{% if minHeight and  (displayMode == "cover" or displayMode == "contain" ) %} style="min-height: {{ minHeight }}"{% endif %}>
            {% set cdnUrl = app.request.server.get('CDN_URL')~'/public/' %}
            {% set lgSrc = image.url|replace({(cdnUrl): cloud_flare_image_url~ 'lg/'}) %}
            {% set attributes = {
                'class': 'img-fluid gallery-slider-image magnifier-image js-magnifier-image h-100',
                'alt': (image.translated.alt ?: fallbackImageTitle ?: image.fileName),
                'title': (image.translated.title ?: fallbackImageTitle ?: image.fileName),
                'data-full-image': lgSrc,
                display: (isProduct and mediaItem.id != page.product.coverId) ? 'none' : 'block'
            } %}
            {% if displayMode == 'cover' or displayMode == 'contain' %}
                {% set attributes = attributes|merge({ 'data-object-fit': displayMode }) %}
            {% endif %}

            {% set sizes = {'default': '300px'} %}
            {% if isProduct %}
                {% set attributes = attributes|merge({ 'itemprop': 'image' }) %}
                {% set sizes = {} %}
            {% endif %}
            {% sw_thumbnails 'gallery-slider-image-thumbnails' with {
                load: false,
                media: image,
                sizes: sizes
            } %}
        </div>
    </div>
{% endblock %}

{% block element_image_gallery_inner_thumbnails_item_inner %}
    {% set thumbnail = image.getThumbnails() ? image.getThumbnails()|filter(t => (t.getWidth() <= 250 and t.getWidth() >= 100))|last : false %}
    <div class="gallery-slider-thumbnails-item-inner">
        {% if controllerName is same as ('Product') %}
            {% cf_single_thumbnail 'gallery-slider-image-thumbnails' with {
                load: false,
                media: image,
                attributes: {
                    'class': 'gallery-slider-thumbnails-image',
                    'alt': (image.translated.alt ?: fallbackImageTitle ?: image.fileName),
                    'title': (image.translated.title ?: fallbackImageTitle ?: image.fileName),
                    'src': image.url,
                    'width': '56',
                }
            } %}
        {% else %}
            <img class="gallery-slider-thumbnails-image"
                 src="{{ thumbnail ? thumbnail.url : image.url }}"
                 loading="lazy"
                 alt="{{ (image.translated.alt ?: fallbackImageTitle ?: image.fileName) }}"
                 title="{{ (image.translated.title ?: fallbackImageTitle ?: image.fileName) }}"
                 {% if isProduct %}itemprop="image"{% endif %}
            />
        {% endif %}
    </div>
{% endblock %}

{% block element_image_gallery_inner_zoom_modal_slider_item_image %}
    {% sw_thumbnails 'gallery-slider-image-thumbnails' with {
        media: image,
        attributes: {
            'class': 'gallery-slider-image js-image-zoom-element js-load-img',
            'alt': (image.translated.alt ?: fallbackImageTitle ?: image.fileName),
            'title': (image.translated.title ?: fallbackImageTitle ?: image.fileName),
            src: image.url,
            display: (isProduct and media.id != page.product.coverId) ? 'none' : 'block'
        },
        load: false,
        loadOriginalImage: true,
        autoColumnSizes: false,
        zoomContainer: true
    } %}
{% endblock %}

{% block element_image_gallery_inner_zoom_modal_thumbnails_item_inner %}
    {% set thumbnail = image.getThumbnails() ? image.getThumbnails()|filter(t => (t.getWidth() <= 250 and t.getWidth() >= 100))|last : false %}
    <div class="gallery-slider-thumbnails-item-inner">
        {% if controllerName is same as ('Product') %}
            {% cf_single_thumbnail 'gallery-slider-image-thumbnails' with {
                media: image,
                attributes: {
                    'class': 'gallery-slider-thumbnails-image',
                    'alt': (image.translated.alt ?: fallbackImageTitle ?: image.fileName),
                    'title': (image.translated.title ?: fallbackImageTitle ?: image.fileName),
                    'src': image.url,
                }
            } %}
        {% else %}
            <img class="gallery-slider-thumbnails-image"
                 src="{{ thumbnail ? thumbnail.url : image.url }}"
                 loading="lazy"
                 alt="{{ (image.translated.alt ?: fallbackImageTitle ?: image.fileName) }}"
                 title="{{ (image.translated.title ?: fallbackImageTitle ?: image.fileName) }}"
                 {% if isProduct %}itemprop="image"{% endif %}
            />
        {% endif %}
    </div>
{% endblock %}

{% block element_image_gallery_inner_single %}
    <div class="base-slider-hover"></div>
    <div class="gallery-slider-single-image is-{{ displayMode }} js-magnifier-container"{% if minHeight %} style="min-height: {{ minHeight }}"{% endif %}>
        {% if imageCount > 0 %}
            {% if controllerName is same as ('Product') %}

                {% set cdnUrl = app.request.server.get('CDN_URL')~'/public/' %}
                {% set lgSrc = mediaItems|first.url|replace({(cdnUrl): cloud_flare_image_url~ 'lg/'}) %}
                {% set attributes = {
                    'class': 'img-fluid gallery-slider-image magnifier-image js-magnifier-image h-100',
                    'alt': (mediaItems|first.translated.title ?: mediaItems|first.fileName),
                    'title': (mediaItems|first.translated.title ?: mediaItems|first.fileName),
                    'data-full-image': lgSrc,
                    display: (mediaItem.id === page.product.coverId  ? 'block' : 'none')
                } %}
            {% else %}
                {% set attributes = {
                    'class': 'img-fluid gallery-slider-image magnifier-image js-magnifier-image h-100',
                    'alt': (mediaItems|first.translated.title ?: mediaItems|first.fileName),
                    'title': (mediaItems|first.translated.title ?: mediaItems|first.fileName),
                    'data-full-image': mediaItems|first.url,
                    display: (mediaItem.id === page.product.coverId  ? 'block' : 'none')
                } %}
            {% endif %}

            {% if displayMode == 'cover' or displayMode == 'contain' %}
                {% set attributes = attributes|merge({ 'data-object-fit': displayMode }) %}
            {% endif %}

            {% if isProduct %}
                {% set attributes = attributes|merge({ 'itemprop': 'image' }) %}
            {% endif %}

            {% sw_thumbnails 'gallery-slider-image-thumbnails' with {
                media: mediaItems|first,
                load: false,
            } %}

        {% else %}
            {% if isProduct %}
                <img itemprop="image" src="{{ asset('bundles/ersatzteilshop/assets/img/no_product_image.png') }}" data-object-fit="contain" alt="{{ page.product.translated.name }}" />
            {% else %}
                {% sw_icon 'placeholder' style {
                    'size': 'fluid'
                } %}
            {% endif %}
        {% endif %}
    </div>
{% endblock %}

{% block element_image_gallery_inner_col %}
    {% if isProduct and imageCount <= 0 %}
        {% set navigationArrows = null %}
        {% set navigationDots = null %}
    {% endif %}

    {{ parent() }}
{% endblock %}

{% block element_image_gallery_inner_container %}
    <div class="base-slider-hover"></div>
    {{ parent() }}
{% endblock %}

{% block element_image_gallery_inner_control_prev_icon %}
    <svg width="19" height="28" viewBox="0 0 19 28" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M16.6094 2.98438L5.39166 14.2021L16.6094 25.4198" stroke="#202E3D" stroke-opacity="0.25" stroke-width="6.73063"/>
    </svg>
{% endblock %}

{% block element_image_gallery_inner_control_next_icon %}
    <svg width="19" height="28" viewBox="0 0 19 28" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M2.39062 25.4219L13.6083 14.2042L2.39062 2.98645" stroke="#202E3D" stroke-opacity="0.25" stroke-width="6.73063"/>
    </svg>
{% endblock %}

{% block element_image_gallery_inner_zoom_modal_action_buttons %}
  <div class="zoom-modal-actions btn-group d-none d-md-block"
       role="group"
       aria-label="zoom actions">

      {% block element_image_gallery_inner_zoom_modal_action_zoom_out %}
          <button class="btn btn-light image-zoom-btn js-image-zoom-out">
              {% block element_image_gallery_inner_zoom_modal_action_zoom_out_icon %}
                  {% sw_icon 'minus-circle' %}
              {% endblock %}
          </button>
      {% endblock %}

      {% block element_image_gallery_inner_zoom_modal_action_zoom_reset %}
          <button class="btn btn-light image-zoom-btn js-image-zoom-reset">
              {% block element_image_gallery_inner_zoom_modal_action_zoom_reset_icon %}
                  {% sw_icon 'screen-minimize' %}
              {% endblock %}
          </button>
      {% endblock %}

      {% block element_image_gallery_inner_zoom_modal_action_zoom_in %}
          <button class="btn btn-light image-zoom-btn js-image-zoom-in">
              {% block element_image_gallery_inner_zoom_modal_action_zoom_in_icon %}
                  {% sw_icon 'plus-circle' %}
              {% endblock %}
          </button>
      {% endblock %}
  </div>
{% endblock %}
