{% block element_popular_repair_guide_category_slider %}
    {% if not sliderConfig %}
        {% set sliderConfig = element.fieldConfig.elements %}
    {% endif %}

    {% if element.data.repairGuideCategories.elements|length > 0 %}
        <div class="cms-element-product-slider cms-element-{{ element.type }}">
            {% block element_popular_repair_guide_category_slider_alignment %}
                {% set productSliderOptions = {
                    productboxMinWidth: '150px',
                    slider: {
                        gutter: 30,
                        autoplayButtonOutput: false,
                        nav: false,
                        mouseDrag: false,
                        controls: true,
                        autoplay: false,
                    }
                } %}

                {% block element_popular_repair_guide_category_slider_title %}
                    {% if sliderConfig.title.value %}
                        <h2 class="cms-element-title">{{ sliderConfig.title.value }}</h2>
                    {% endif %}
                {% endblock %}

                {% block element_popular_repair_guide_category_slider_slider %}
                    <div class="base-slider product-slider has-nav repair-guide-slider"
                         data-product-slider="true"
                         data-product-slider-options="{{ productSliderOptions|json_encode }}">

                        <div class="loader"></div>

                        {% block element_popular_repair_guide_category_slider_inner %}
                            {% block element_popular_repair_guide_category_slider_element %}
                                <div class="product-slider-container"
                                     data-product-slider-container="true">
                                    {% for category in element.data.repairGuideCategories.elements %}
                                        {% block element_popular_repair_guide_category_slider_inner_item %}
                                            <div class="slider-item product-slider-item" style="min-width: 200px">
                                                {% block component_popular_repair_guide_category %}
                                                    {% if category %}
                                                        {% set name = category.translated.name %}
                                                        {% set id = category.id %}
                                                        {% set cover = category.media %}

                                                        {% set link = seoUrl('frontend.navigation.page', { navigationId: category.id }) %}
                                                        {% if category.type == 'link' and category.externalLink %}
                                                            {% set link = category.externalLink %}
                                                        {% endif %}

                                                        <div class="card product-box box-standard">
                                                            {% block component_popular_repair_guide_category_content %}
                                                                <div class="card-body">
                                                                    {% block component_popular_repair_guide_category_image %}
                                                                        <div class="product-image-wrapper">
                                                                            <a href="{{ link }}"
                                                                               title="{{ name }}"
                                                                               class="product-image-link is-standard">
                                                                                {% if cover.url %}
                                                                                    {% set attributes = {
                                                                                        'class': 'product-image is-standard',
                                                                                        'alt': (cover.translated.alt ?: name),
                                                                                        'title': (cover.translated.title ?: name)
                                                                                    } %}

                                                                                    {% sw_thumbnails 'product-image-thumbnails' with {
                                                                                        load: false,
                                                                                        media: cover,
                                                                                        sizes: {
                                                                                            'default': '150px'
                                                                                        }
                                                                                    } %}
                                                                                {% else %}
                                                                                    <div class="product-image-placeholder">
                                                                                        {% sw_icon 'placeholder' style {
                                                                                            'size': 'fluid'
                                                                                        } %}
                                                                                    </div>
                                                                                {% endif %}
                                                                            </a>
                                                                        </div>
                                                                    {% endblock %}

                                                                    {% block component_popular_repair_guide_category_info %}
                                                                        <div class="product-info">
                                                                            {% block component_popular_repair_guide_category_name %}
                                                                                <a href="{{ link }}"
                                                                                   class="product-name"
                                                                                   title="{{ name }}">
                                                                                    {{ name }}
                                                                                </a>
                                                                            {% endblock %}
                                                                        </div>
                                                                    {% endblock %}
                                                                </div>
                                                            {% endblock %}
                                                        </div>
                                                    {% endif %}
                                                {% endblock %}
                                            </div>
                                        {% endblock %}
                                    {% endfor %}
                                </div>
                            {% endblock %}

                            {% block element_popular_repair_guide_category_slider_controls %}
                                <div class="product-slider-controls-container">
                                    <div class="base-slider-controls"
                                         data-product-slider-controls="true">
                                        {% block element_popular_repair_guide_category_slider_controls_items %}
                                            <button class="base-slider-controls-prev product-slider-controls-prev">
                                                {% block element_popular_repair_guide_category_slider_controls_items_prev_icon %}
                                                    <span class="fa fa-chevron-left"></span>
                                                {% endblock %}
                                            </button>
                                            <button class="base-slider-controls-next product-slider-controls-next">
                                                {% block element_popular_repair_guide_category_slider_controls_items_next_icon %}
                                                    <span class="fa fa-chevron-right"></span>
                                                {% endblock %}
                                            </button>
                                        {% endblock %}
                                    </div>
                                </div>
                            {% endblock %}
                        {% endblock %}
                    </div>
                {% endblock %}
            {% endblock %}
        </div>
    {% endif %}
{% endblock %}
