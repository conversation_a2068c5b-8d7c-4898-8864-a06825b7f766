{% sw_extends '@Storefront/storefront/page/checkout/confirm/confirm-address.html.twig' %}

{# Neues Layout: Rechnungs- und Lieferadresse nebeneinander #}
{% block page_checkout_confirm_address_billing_title %}
    <div class="confirm-address-billing-section">
        <div class="address-header">
            <div class="card-title">
                {{ "checkout.billingAddressHeader"|trans|sw_sanitize }}
            </div>

            <div class="address-edit-button">
                {% set addressEditorOptions = {
                    changeBilling: true,
                    addressId: billingAddress.id,
                    csrfToken: sw_csrf("frontend.account.addressbook", {"mode": "token"})
                } %}
                    <a href="{{ path('frontend.account.address.edit.page', {'addressId': billingAddress.id}) }}"
                       title="{{ "account.overviewChangeBilling"|trans|striptags }}"
                       class="editCheckout_button"
                       data-address-editor="true"
                       data-address-editor-options='{{ addressEditorOptions|json_encode }}'>
{#                        {{ "account.change"|trans|sw_sanitize }}#}
{#                        <picture>#}
{#                            <img style="position: relative; left: 5px" width="20" width="15" border="0"  src="{{ asset('/bundles/ersatzteilshop/assets/img/icon_edit.svg') }}" />#}
{#                        </picture>#}
                        <img alt="edit-note" src="{{ asset('bundles/ersatzteilshop/assets/icons/svgs/edit-note.svg') }}">
                    </a>
            </div>
        </div>
{% endblock %}

{% block page_checkout_confirm_address_billing_data %}
        <div class="confirm-address-billing">
            {% sw_include '@Storefront/storefront/component/address/address.html.twig' with {
                'address': billingAddress
            } %}
        </div>
    </div>
{% endblock %}

{% block page_checkout_confirm_address_billing_actions %}
{% endblock %}


{% block page_checkout_confirm_address_shipping_title %}
    <div class="confirm-address-shipping-section">
        <div class="address-header">
            <div class="card-title">
                {{ "checkout.shippingAddressHeader"|trans|sw_sanitize }}
            </div>

            <div class="address-edit-button">

                    {% set addressEditorOptions = {
                        changeShipping: true,
                        addressId: shippingAddress.id,
                        csrfToken: sw_csrf("frontend.account.addressbook", {"mode": "token"})
                    } %}

                    {% block  page_checkout_confirm_address_shipping_actions_link %}
                        <a href="{{ path('frontend.account.address.edit.page', {'addressId': shippingAddress.id}) }}"
                           title="{{ "account.overviewChangeShipping"|trans|striptags }}"
                           id="btn-edit-shipping-address-modal"
                           class="editCheckout_button"
                           data-address-editor="true"
                           data-address-editor-options='{{ addressEditorOptions|json_encode }}'
                           data-billing-address-id="{{ billingAddress.id }}"
                           data-shipping-address-id="{{ shippingAddress.id }}">
{#                            {{ "account.change"|trans|sw_sanitize }}#}
{#                            <picture>#}
{#                                <img style="position: relative; left: 5px" width="20" width="15" border="0"  src="{{ asset('/bundles/ersatzteilshop/assets/img/icon_edit.svg') }}" />#}
{#                            </picture>#}
                            <img alt="edit-note" src="{{ asset('bundles/ersatzteilshop/assets/icons/svgs/edit-note.svg') }}">
                        </a>
                    {% endblock %}

            </div>
        </div>

{% endblock %}

{% block page_checkout_confirm_address_shipping_data %}
        <div class="confirm-address-shipping">
            {% if billingAddress.id is same as(shippingAddress.id) or billingAddress.customFields.addressType == 'MeineinkaufAddress' %}
                {% block page_checkout_confirm_address_shipping_data_equal %}
                    <p>
                        {{ "checkout.addressEqualText"|trans|sw_sanitize }}
                    </p>
                {% endblock %}
            {% else %}
                {% sw_include '@Storefront/storefront/component/address/address.html.twig' with {
                'address': shippingAddress
                } %}
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block page_checkout_confirm_address_shipping_actions %}

{% endblock %}
