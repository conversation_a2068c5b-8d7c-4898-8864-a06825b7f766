{% sw_extends '@Storefront/storefront/page/_page.html.twig' %}

{% block base_head %}
    {% sw_include '@Storefront/storefront/page/product-detail/special-dummy-product/meta.html.twig' %}

    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "Product",
            "name": "{{ page.product.name }}",
        "image": "{{ page.product.cover.media.url }}",
        "releaseDate": "{{ page.product.releaseDate|date('Y-m-d') }}",
        "productID": "{{ page.product.id }}",
        "sku": "{{ page.product.productNumber }}",
        "brand": {
            "@type": "Brand",
            "name": "{{ page.product.manufacturer.name }}"
        },
        "offers": {
            "@type": "Offer",
            "url": "{{ seoUrl('frontend.detail.page', {'productId': page.product.id}) }}",
            "priceCurrency": "{{ context.currency.isoCode }}",
            "price": "{{ page.product.calculatedCheapestPrice.unitPrice }}",
            "availability": "https://schema.org/InStock"
        }
    }
    </script>
{% endblock %}

{% block base_main_container %}
    <div class="container-main k11-mt-60 k11-mb-0">
        {% block page_main_content %}
            {% block page_content %}
                {% set searchTerm = page.extensions.applianceCategory.searchTerm %}
                <div class="container-main-content" data-dummy-product="true" data-dummy-searchterm="{{ searchTerm }}">
                    <div id="dummy-product-content">
                        {% block page_content_heading %}
                            <h2 class="k11-font-size-20 k11-line-height-1 k11-font-weight-900 k11-text-left" itemprop="name">
                                {{ page.product.name}}
                            </h2>
                        {% endblock %}

                        {% block page_content_mobile_image %}
                            <div class="d-flex d-md-none align-items-center position-relative overflow-hidden" style="min-width: 350px;">
                                {% set imgSrc = noImage %}
                                {% if page.product.cover.media.url %}
                                    {% set imgSrc = page.product.cover.media.url %}
                                {% endif %}
                                <img class="counter-element-image w-100" src="{{ imgSrc }}" alt="{{ page.product.name }}" style="opacity: 1;"/>
                                <div class="counter-element-text w-100"></div>
                            </div>
                        {% endblock %}

                        {% block page_content_product_box %}
                            <div class="d-grid k11-grid-template-1 k11-md-grid-template-3 k11-gap-20 k11-my-40">
                                {% block page_content_product_box_image %}
                                    <div class="d-none d-md-flex align-items-center position-relative overflow-hidden">
                                        {% set imgSrc = noImage %}
                                        {% if page.product.cover.media.url %}
                                            {% set imgSrc = page.product.cover.media.url %}
                                        {% endif %}
                                        <img class="counter-element-image w-100" src="{{ imgSrc }}" alt="{{ page.product.name }}" style="opacity: 1;" />
                                        <div class="counter-element-text"></div>
                                    </div>
                                {% endblock %}

                                {% block page_content_main_search_widget_mobile %}
                                    <div class="d-block d-md-none">
                                        <div id="initial-call-to-action-mobile" class="k11-border-green k11-p-20 k11-mb-20">
                                            <div class="d-grid k11-grid-template-1 k11-md-grid-template-2 k11-gap-10">
                                                <div class="d-block d-md-flex align-items-center justify-content-center">
                                                    <h2 class="k11-font-size-20 k11-line-height-1 k11-font-weight-900 k11-text-center k11-text-md-left cta-question k11-mb-0">
                                                        {{ 'ersatzteilshop.productDetail.applianceSearchWidget.headingDummyPage'|trans|sw_sanitize }}
                                                    </h2>
                                                </div>
                                                <div class="d-block d-md-flex justify-content-center" data-action="hide_cta">
                                                    <div class="delivery-information__badge delivery-information__badge--short k11-pointer w-100">
                                                        <svg class="k11-m-5" width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <mask id="mask0_5363_48886" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="24">
                                                                <rect x="0.736328" width="24" height="24" fill="#D9D9D9"/>
                                                            </mask>
                                                            <g mask="url(#mask0_5363_48886)">
                                                                <path d="M9.68633 13.55L15.3363 7.875L13.9113 6.45L9.68633 10.7L7.56133 8.6L6.13633 10L9.68633 13.55ZM10.7363 18C8.50299 18 6.61133 17.225 5.06133 15.675C3.51133 14.125 2.73633 12.2333 2.73633 10C2.73633 7.76667 3.51133 5.875 5.06133 4.325C6.61133 2.775 8.50299 2 10.7363 2C12.9697 2 14.8613 2.775 16.4113 4.325C17.9613 5.875 18.7363 7.76667 18.7363 10C18.7363 10.9333 18.5905 11.8125 18.2988 12.6375C18.0072 13.4625 17.5947 14.2167 17.0613 14.9L22.7363 20.6L21.3363 22L15.6363 16.325C14.953 16.8583 14.1988 17.2708 13.3738 17.5625C12.5488 17.8542 11.6697 18 10.7363 18Z" fill="#202E3D"/>
                                                            </g>
                                                        </svg>
                                                        <span class="k11-mr-5 k11-font-size-14 k11-font-weight-900">JETZT PRÜFEN</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="model-number-search-mobile" class="k11-border-green k11-p-20 k11-mb-20 d-none">
                                            <h2 class="k11-font-size-16 k11-line-height-1 k11-font-weight-900 k11-text-center k11-text-md-left cta-question">
                                                {{ 'ersatzteilshop.productDetail.applianceSearchWidget.headingDummyPage'|trans|sw_sanitize }}
                                            </h2>
                                            <div class="d-grid k11-grid-template-1 k11-gap-10">
                                                <div class="position-relative">
                                                    <form class="k11-border-green d-flex align-items-center" action="{{ path('frontend.search.page') }}" method="get">
                                                        <div id="modelNumberCollapsible" class="input-group search-input border-0 k11-border-radius-8 k11-pl-15 form-control k11-line-height-1">
                                                            <input id="search-input" class="input-group search-input border-0 outline-none bg-none w-100 k11-font-size-16" placeholder="Typennummer / Modellnummer eingeben" type="search" name="search" autocomplete="off">
                                                        </div>
                                                        <div class="svg-container">
                                                            <button class="search-input--btn border-0 p-0 outline-none bg-none" type="submit">
                                                                <span class="input-initial-state">
                                                                     <svg class="k11-p-2" width="34" height="35" viewBox="0 0 34 35" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                        <rect x="0.890625" y="0.28125" width="32.3184" height="34" rx="4" fill="#74CB7B"/>
                                                                        <mask id="mask0_3388_106468" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="5" y="5" width="25" height="25">
                                                                            <rect x="5.04688" y="5.28125" width="24" height="24" fill="#D9D9D9"/>
                                                                        </mask>
                                                                        <g mask="url(#mask0_3388_106468)">
                                                                            <path d="M26.6469 28.2812L23.5719 25.2313C23.2719 25.4146 22.951 25.5521 22.6094 25.6438C22.2677 25.7354 21.9135 25.7812 21.5469 25.7812C20.4469 25.7812 19.5052 25.3896 18.7219 24.6063C17.9385 23.8229 17.5469 22.8813 17.5469 21.7812C17.5469 20.6812 17.9385 19.7396 18.7219 18.9563C19.5052 18.1729 20.4469 17.7812 21.5469 17.7812C22.6469 17.7812 23.5885 18.1729 24.3719 18.9563C25.1552 19.7396 25.5469 20.6812 25.5469 21.7812C25.5469 22.1646 25.4969 22.5271 25.3969 22.8687C25.2969 23.2104 25.1552 23.5313 24.9719 23.8313L28.0469 26.8813L26.6469 28.2812ZM10.5469 25.7812C9.44688 25.7812 8.50521 25.3896 7.72188 24.6063C6.93854 23.8229 6.54688 22.8813 6.54688 21.7812C6.54688 20.6812 6.93854 19.7396 7.72188 18.9563C8.50521 18.1729 9.44688 17.7812 10.5469 17.7812C11.6469 17.7812 12.5885 18.1729 13.3719 18.9563C14.1552 19.7396 14.5469 20.6812 14.5469 21.7812C14.5469 22.8813 14.1552 23.8229 13.3719 24.6063C12.5885 25.3896 11.6469 25.7812 10.5469 25.7812ZM21.5469 23.7812C22.0969 23.7812 22.5677 23.5854 22.9594 23.1938C23.351 22.8021 23.5469 22.3313 23.5469 21.7812C23.5469 21.2312 23.351 20.7604 22.9594 20.3688C22.5677 19.9771 22.0969 19.7812 21.5469 19.7812C20.9969 19.7812 20.526 19.9771 20.1344 20.3688C19.7427 20.7604 19.5469 21.2312 19.5469 21.7812C19.5469 22.3313 19.7427 22.8021 20.1344 23.1938C20.526 23.5854 20.9969 23.7812 21.5469 23.7812ZM10.5469 14.7812C9.44688 14.7812 8.50521 14.3896 7.72188 13.6063C6.93854 12.8229 6.54688 11.8812 6.54688 10.7812C6.54688 9.68125 6.93854 8.73958 7.72188 7.95625C8.50521 7.17292 9.44688 6.78125 10.5469 6.78125C11.6469 6.78125 12.5885 7.17292 13.3719 7.95625C14.1552 8.73958 14.5469 9.68125 14.5469 10.7812C14.5469 11.8812 14.1552 12.8229 13.3719 13.6063C12.5885 14.3896 11.6469 14.7812 10.5469 14.7812ZM21.5469 14.7812C20.4469 14.7812 19.5052 14.3896 18.7219 13.6063C17.9385 12.8229 17.5469 11.8812 17.5469 10.7812C17.5469 9.68125 17.9385 8.73958 18.7219 7.95625C19.5052 7.17292 20.4469 6.78125 21.5469 6.78125C22.6469 6.78125 23.5885 7.17292 24.3719 7.95625C25.1552 8.73958 25.5469 9.68125 25.5469 10.7812C25.5469 11.8812 25.1552 12.8229 24.3719 13.6063C23.5885 14.3896 22.6469 14.7812 21.5469 14.7812Z" fill="#202E3D"/>
                                                                        </g>
                                                                    </svg>
                                                                </span>
                                                                <span class="input-loading-state d-none">
                                                                    <svg class="k11-p-2 k11-mr-5" width="35" height="35" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                        <path d="M12 22C10.6333 22 9.34167 21.7375 8.125 21.2125C6.90833 20.6875 5.84583 19.9708 4.9375 19.0625C4.02917 18.1542 3.3125 17.0917 2.7875 15.875C2.2625 14.6583 2 13.3667 2 12C2 10.6167 2.2625 9.32083 2.7875 8.1125C3.3125 6.90417 4.02917 5.84583 4.9375 4.9375C5.84583 4.02917 6.90833 3.3125 8.125 2.7875C9.34167 2.2625 10.6333 2 12 2C12.2833 2 12.5208 2.09583 12.7125 2.2875C12.9042 2.47917 13 2.71667 13 3C13 3.28333 12.9042 3.52083 12.7125 3.7125C12.5208 3.90417 12.2833 4 12 4C9.78333 4 7.89583 4.77917 6.3375 6.3375C4.77917 7.89583 4 9.78333 4 12C4 14.2167 4.77917 16.1042 6.3375 17.6625C7.89583 19.2208 9.78333 20 12 20C14.2167 20 16.1042 19.2208 17.6625 17.6625C19.2208 16.1042 20 14.2167 20 12C20 11.7167 20.0958 11.4792 20.2875 11.2875C20.4792 11.0958 20.7167 11 21 11C21.2833 11 21.5208 11.0958 21.7125 11.2875C21.9042 11.4792 22 11.7167 22 12C22 13.3667 21.7375 14.6583 21.2125 15.875C20.6875 17.0917 19.9708 18.1542 19.0625 19.0625C18.1542 19.9708 17.0958 20.6875 15.8875 21.2125C14.6792 21.7375 13.3833 22 12 22Z" fill="#202E3D"/>
                                                                    </svg>
                                                                </span>
                                                                <span class="input-no-result-state d-none">
                                                                    <svg class="k11-p-2" width="34" height="35" viewBox="0 0 34 35" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                        <rect y="0.730469" width="34" height="34" rx="4" fill="#74CB7B"/>
                                                                        <mask id="mask0_3662_74930" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="5" y="5" width="24" height="25">
                                                                            <rect x="5" y="5.73047" width="24" height="24" fill="#D9D9D9"/>
                                                                        </mask>
                                                                        <g mask="url(#mask0_3662_74930)">
                                                                            <path d="M24.6 26.7305L18.3 20.4305C17.8 20.8305 17.225 21.1471 16.575 21.3805C15.925 21.6138 15.2333 21.7305 14.5 21.7305C12.6833 21.7305 11.1458 21.1013 9.8875 19.843C8.62917 18.5846 8 17.0471 8 15.2305C8 13.4138 8.62917 11.8763 9.8875 10.618C11.1458 9.35964 12.6833 8.73047 14.5 8.73047C16.3167 8.73047 17.8542 9.35964 19.1125 10.618C20.3708 11.8763 21 13.4138 21 15.2305C21 15.9638 20.8833 16.6555 20.65 17.3055C20.4167 17.9555 20.1 18.5305 19.7 19.0305L26 25.3305L24.6 26.7305ZM14.5 19.7305C15.75 19.7305 16.8125 19.293 17.6875 18.418C18.5625 17.543 19 16.4805 19 15.2305C19 13.9805 18.5625 12.918 17.6875 12.043C16.8125 11.168 15.75 10.7305 14.5 10.7305C13.25 10.7305 12.1875 11.168 11.3125 12.043C10.4375 12.918 10 13.9805 10 15.2305C10 16.4805 10.4375 17.543 11.3125 18.418C12.1875 19.293 13.25 19.7305 14.5 19.7305Z" fill="#202E3D"/>
                                                                        </g>
                                                                    </svg>
                                                                </span>
                                                                <span class="input-success-state d-none">
                                                                <svg class="k11-p-2 k11-mr-5" width="35" height="35" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"></svg>
                                                        </span>
                                                            </button>
                                                        </div>
                                                    </form>
                                                    <div class="collapse position-relative w-100" id="modelNumberCollapse" style="top: -6px">
                                                        <div class="collapsible-content" style="padding: 10px;position: relative;border: 1px solid #74cb7b;border-top: 0;border-bottom-right-radius: 8px;border-bottom-left-radius: 8px;">
                                                            <div class="align-items-center">
                                                                <div class="k11-p-10 k11-bg-white" style="max-height: 200px; overflow-y: auto;">
                                                                    <ul class="list-unstyled mb-0 search-results-container">
                                                                        <!-- Suchergebnisse -->
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {% endblock %}

                                {% block page_content_product_box_information %}
                                    <div class="k11-border k11-p-20 d-flex flex-column">
                                        {% block page_content_product_box_information_manufacturer %}
                                            {% if page.product.manufacturer %}
                                                <div class="product-detail-manufacturer k11-mb-20 opacity-50">
                                                    {% block page_product_detail_manufacturer_inner %}
                                                        {% block page_product_detail_manufacturer_link %}
                                                            {% if page.product.manufacturer.media %}
                                                                {% block page_product_detail_manufacturer_logo %}
                                                                    <img src="{{ page.product.manufacturer.media|sw_encode_media_url }}"
                                                                         class="product-detail-manufacturer-logo"
                                                                         height="28px"
                                                                         alt="{{ page.product.manufacturer.translated.name }}"/>
                                                                {% endblock %}
                                                            {% else %}
                                                                {% block page_product_detail_manufacturer_text %}
                                                                    {{ page.product.manufacturer.translated.name }}
                                                                {% endblock %}
                                                            {% endif %}
                                                        {% endblock %}
                                                    {% endblock %}
                                                </div>
                                            {% endif %}
                                        {% endblock %}

                                        {% block page_content_product_box_information_ot_badge %}
                                            <div class="k11-mb-20">
                                                <img width="266px" height="29px" class="d-block" loading="eager" alt="product-ot-badge" src="{{ asset('bundles/ersatzteilshop/assets/icons/svgs/dummy-product-ot-badge.svg') }}">
                                            </div>
                                        {% endblock %}

                                        {% block page_content_product_box_information_alternative %}
                                            <div class="k11-mb-15">
                                                <p class="k11-mb-0 k11-line-height-1 k11-color-light-grey user-select-none">
                                                    <u>{{ 'ersatzteilshop.productDetail.alternativeBuy'|trans|sw_sanitize }}</u>
                                                </p>
                                            </div>
                                        {% endblock %}

                                        {% block page_content_product_box_information_reviews %}
                                            <div class="k11-mb-20">
                                                <img width="154px" height="29px" class="d-block" loading="eager" alt="product-review-stars" src="{{ asset('bundles/ersatzteilshop/assets/icons/svgs/dummy-product-review-stars.svg') }}">
                                            </div>
                                        {% endblock %}

                                        {% block page_content_product_box_information_payment_options %}
                                            <div class="k11-pt-20 k11-md-pt-0 mt-auto">
                                                <p class="k11-mb-10 k11-line-height-1 k11-color-light-grey user-select-none">{{ 'ersatzteilshop.productDetail.paymentMethodsLabel'|trans|sw_sanitize }}</p>
                                                <img width="266px" height="42px" class="d-block" loading="eager" alt="product-payment-options" src="{{ asset('bundles/ersatzteilshop/assets/icons/svgs/dummy-product-payment-options.svg') }}">
                                            </div>
                                        {% endblock %}
                                    </div>
                                {% endblock %}

                                {% block page_content_product_box_secondary_information %}
                                    <div class="k11-border k11-p-20 d-flex flex-column">
                                        {% block page_content_product_box_secondary_information_price %}
                                            {% sw_include '@Storefront/storefront/page/product-detail/buy-widget-price.html.twig' with { bigPrice : true } %}
                                        {% endblock %}

                                        {% block page_content_product_box_secondary_information_shipping %}
                                            <p class="dummy-product-tax k11-mb-20" data-toggle="modal"
                                               data-url="{{ path('frontend.widget.countryShippingCost') }}">
                                                {{ 'general.grossTaxInformationFreeShip'|trans|striptags }}
                                            </p>
                                        {% endblock %}

                                        {% block page_content_product_box_secondary_information_delivery_time %}
                                            <div class="k11-mb-10">
                                                <img width="130px" height="29px" class="d-block" loading="eager" alt="product-delivery-time" src="{{ asset('bundles/ersatzteilshop/assets/icons/svgs/dummy-product-delivery-time.svg') }}">
                                            </div>
                                        {% endblock %}

                                        {% block page_content_product_box_secondary_information_delivery_cost %}
                                            <div class="k11-mb-20">
                                                <img width="163px" height="28px" class="d-block" loading="eager" alt="product-delivery-cost" src="{{ asset('bundles/ersatzteilshop/assets/icons/svgs/dummy-product-delivery-cost.svg') }}">
                                            </div>
                                        {% endblock %}

                                        {% block page_content_product_box_secondary_information_buy_form %}
                                            {% if page.product.active %}
                                                <div>
                                                    {% sw_include '@Storefront/storefront/page/product-detail/v3/dummy-buy-widget-form.html.twig' %}
                                                </div>
                                            {% endif %}
                                        {% endblock %}
                                    </div>
                                {% endblock %}
                            </div>
                        {% endblock %}

                        {% block page_content_main_search_widget_desktop %}
                            <div class="d-none d-md-block">
                                <div id="initial-call-to-action-desktop" class="k11-border-green k11-p-20 k11-mb-20">
                                    <div class="d-grid k11-grid-template-1 k11-md-grid-template-2 k11-gap-10">
                                        <div class="d-block d-md-flex align-items-center justify-content-center">
                                            <h2 class="k11-font-size-20 k11-line-height-1 k11-font-weight-900 k11-text-center k11-text-md-left cta-question k11-mb-0">
                                                {{ 'ersatzteilshop.productDetail.applianceSearchWidget.headingDummyPage'|trans|sw_sanitize }}
                                            </h2>
                                        </div>
                                        <div class="d-block d-md-flex justify-content-center" data-action="hide_cta">
                                            <div class="delivery-information__badge delivery-information__badge--short k11-pointer w-100">
                                                <svg class="k11-m-5" width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <mask id="mask0_5363_48886" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="24">
                                                        <rect x="0.736328" width="24" height="24" fill="#D9D9D9"/>
                                                    </mask>
                                                    <g mask="url(#mask0_5363_48886)">
                                                        <path d="M9.68633 13.55L15.3363 7.875L13.9113 6.45L9.68633 10.7L7.56133 8.6L6.13633 10L9.68633 13.55ZM10.7363 18C8.50299 18 6.61133 17.225 5.06133 15.675C3.51133 14.125 2.73633 12.2333 2.73633 10C2.73633 7.76667 3.51133 5.875 5.06133 4.325C6.61133 2.775 8.50299 2 10.7363 2C12.9697 2 14.8613 2.775 16.4113 4.325C17.9613 5.875 18.7363 7.76667 18.7363 10C18.7363 10.9333 18.5905 11.8125 18.2988 12.6375C18.0072 13.4625 17.5947 14.2167 17.0613 14.9L22.7363 20.6L21.3363 22L15.6363 16.325C14.953 16.8583 14.1988 17.2708 13.3738 17.5625C12.5488 17.8542 11.6697 18 10.7363 18Z" fill="#202E3D"/>
                                                    </g>
                                                </svg>
                                                <span class="k11-mr-5 k11-font-size-14 k11-font-weight-900">JETZT PRÜFEN</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="model-number-search-desktop" class="k11-border-green k11-p-20 k11-mb-20 d-none">
                                    <h2 class="k11-font-size-16 k11-line-height-1 k11-font-weight-900 k11-text-center k11-text-md-left cta-question">
                                        {{ 'ersatzteilshop.productDetail.applianceSearchWidget.headingDummyPage'|trans|sw_sanitize }}
                                    </h2>
                                    <div class="d-grid k11-grid-template-1 k11-gap-10">
                                        <div class="position-relative">
                                            <form class="k11-border-green d-flex align-items-center" action="{{ path('frontend.search.page') }}" method="get">
                                                <div id="modelNumberCollapsible" class="input-group search-input border-0 k11-border-radius-8 k11-pl-15 form-control k11-line-height-1">
                                                    <input id="search-input" class="input-group search-input border-0 outline-none bg-none w-100 k11-font-size-16" placeholder="Typennummer / Modellnummer eingeben" type="search" name="search" autocomplete="off">
                                                </div>
                                                <div class="svg-container">
                                                    <button class="search-input--btn border-0 p-0 outline-none bg-none" type="submit">
                                                        <span class="input-initial-state">
                                                             <svg class="k11-p-2" width="34" height="35" viewBox="0 0 34 35" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                <rect x="0.890625" y="0.28125" width="32.3184" height="34" rx="4" fill="#74CB7B"/>
                                                                <mask id="mask0_3388_106468" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="5" y="5" width="25" height="25">
                                                                    <rect x="5.04688" y="5.28125" width="24" height="24" fill="#D9D9D9"/>
                                                                </mask>
                                                                <g mask="url(#mask0_3388_106468)">
                                                                    <path d="M26.6469 28.2812L23.5719 25.2313C23.2719 25.4146 22.951 25.5521 22.6094 25.6438C22.2677 25.7354 21.9135 25.7812 21.5469 25.7812C20.4469 25.7812 19.5052 25.3896 18.7219 24.6063C17.9385 23.8229 17.5469 22.8813 17.5469 21.7812C17.5469 20.6812 17.9385 19.7396 18.7219 18.9563C19.5052 18.1729 20.4469 17.7812 21.5469 17.7812C22.6469 17.7812 23.5885 18.1729 24.3719 18.9563C25.1552 19.7396 25.5469 20.6812 25.5469 21.7812C25.5469 22.1646 25.4969 22.5271 25.3969 22.8687C25.2969 23.2104 25.1552 23.5313 24.9719 23.8313L28.0469 26.8813L26.6469 28.2812ZM10.5469 25.7812C9.44688 25.7812 8.50521 25.3896 7.72188 24.6063C6.93854 23.8229 6.54688 22.8813 6.54688 21.7812C6.54688 20.6812 6.93854 19.7396 7.72188 18.9563C8.50521 18.1729 9.44688 17.7812 10.5469 17.7812C11.6469 17.7812 12.5885 18.1729 13.3719 18.9563C14.1552 19.7396 14.5469 20.6812 14.5469 21.7812C14.5469 22.8813 14.1552 23.8229 13.3719 24.6063C12.5885 25.3896 11.6469 25.7812 10.5469 25.7812ZM21.5469 23.7812C22.0969 23.7812 22.5677 23.5854 22.9594 23.1938C23.351 22.8021 23.5469 22.3313 23.5469 21.7812C23.5469 21.2312 23.351 20.7604 22.9594 20.3688C22.5677 19.9771 22.0969 19.7812 21.5469 19.7812C20.9969 19.7812 20.526 19.9771 20.1344 20.3688C19.7427 20.7604 19.5469 21.2312 19.5469 21.7812C19.5469 22.3313 19.7427 22.8021 20.1344 23.1938C20.526 23.5854 20.9969 23.7812 21.5469 23.7812ZM10.5469 14.7812C9.44688 14.7812 8.50521 14.3896 7.72188 13.6063C6.93854 12.8229 6.54688 11.8812 6.54688 10.7812C6.54688 9.68125 6.93854 8.73958 7.72188 7.95625C8.50521 7.17292 9.44688 6.78125 10.5469 6.78125C11.6469 6.78125 12.5885 7.17292 13.3719 7.95625C14.1552 8.73958 14.5469 9.68125 14.5469 10.7812C14.5469 11.8812 14.1552 12.8229 13.3719 13.6063C12.5885 14.3896 11.6469 14.7812 10.5469 14.7812ZM21.5469 14.7812C20.4469 14.7812 19.5052 14.3896 18.7219 13.6063C17.9385 12.8229 17.5469 11.8812 17.5469 10.7812C17.5469 9.68125 17.9385 8.73958 18.7219 7.95625C19.5052 7.17292 20.4469 6.78125 21.5469 6.78125C22.6469 6.78125 23.5885 7.17292 24.3719 7.95625C25.1552 8.73958 25.5469 9.68125 25.5469 10.7812C25.5469 11.8812 25.1552 12.8229 24.3719 13.6063C23.5885 14.3896 22.6469 14.7812 21.5469 14.7812Z" fill="#202E3D"/>
                                                                </g>
                                                            </svg>
                                                        </span>
                                                        <span class="input-loading-state d-none">
                                                          <svg class="k11-p-2 k11-mr-5" width="35" height="35" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M12 22C10.6333 22 9.34167 21.7375 8.125 21.2125C6.90833 20.6875 5.84583 19.9708 4.9375 19.0625C4.02917 18.1542 3.3125 17.0917 2.7875 15.875C2.2625 14.6583 2 13.3667 2 12C2 10.6167 2.2625 9.32083 2.7875 8.1125C3.3125 6.90417 4.02917 5.84583 4.9375 4.9375C5.84583 4.02917 6.90833 3.3125 8.125 2.7875C9.34167 2.2625 10.6333 2 12 2C12.2833 2 12.5208 2.09583 12.7125 2.2875C12.9042 2.47917 13 2.71667 13 3C13 3.28333 12.9042 3.52083 12.7125 3.7125C12.5208 3.90417 12.2833 4 12 4C9.78333 4 7.89583 4.77917 6.3375 6.3375C4.77917 7.89583 4 9.78333 4 12C4 14.2167 4.77917 16.1042 6.3375 17.6625C7.89583 19.2208 9.78333 20 12 20C14.2167 20 16.1042 19.2208 17.6625 17.6625C19.2208 16.1042 20 14.2167 20 12C20 11.7167 20.0958 11.4792 20.2875 11.2875C20.4792 11.0958 20.7167 11 21 11C21.2833 11 21.5208 11.0958 21.7125 11.2875C21.9042 11.4792 22 11.7167 22 12C22 13.3667 21.7375 14.6583 21.2125 15.875C20.6875 17.0917 19.9708 18.1542 19.0625 19.0625C18.1542 19.9708 17.0958 20.6875 15.8875 21.2125C14.6792 21.7375 13.3833 22 12 22Z" fill="#202E3D"/>
                                                        </svg>
                                                    </span>
                                                        <span class="input-no-result-state d-none">
                                                            <svg class="k11-p-2" width="34" height="35" viewBox="0 0 34 35" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                <rect y="0.730469" width="34" height="34" rx="4" fill="#74CB7B"/>
                                                                <mask id="mask0_3662_74930" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="5" y="5" width="24" height="25">
                                                                    <rect x="5" y="5.73047" width="24" height="24" fill="#D9D9D9"/>
                                                                </mask>
                                                                <g mask="url(#mask0_3662_74930)">
                                                                    <path d="M24.6 26.7305L18.3 20.4305C17.8 20.8305 17.225 21.1471 16.575 21.3805C15.925 21.6138 15.2333 21.7305 14.5 21.7305C12.6833 21.7305 11.1458 21.1013 9.8875 19.843C8.62917 18.5846 8 17.0471 8 15.2305C8 13.4138 8.62917 11.8763 9.8875 10.618C11.1458 9.35964 12.6833 8.73047 14.5 8.73047C16.3167 8.73047 17.8542 9.35964 19.1125 10.618C20.3708 11.8763 21 13.4138 21 15.2305C21 15.9638 20.8833 16.6555 20.65 17.3055C20.4167 17.9555 20.1 18.5305 19.7 19.0305L26 25.3305L24.6 26.7305ZM14.5 19.7305C15.75 19.7305 16.8125 19.293 17.6875 18.418C18.5625 17.543 19 16.4805 19 15.2305C19 13.9805 18.5625 12.918 17.6875 12.043C16.8125 11.168 15.75 10.7305 14.5 10.7305C13.25 10.7305 12.1875 11.168 11.3125 12.043C10.4375 12.918 10 13.9805 10 15.2305C10 16.4805 10.4375 17.543 11.3125 18.418C12.1875 19.293 13.25 19.7305 14.5 19.7305Z" fill="#202E3D"/>
                                                                </g>
                                                            </svg>
                                                        </span>
                                                        <span class="input-success-state d-none">
                                                            <svg class="k11-p-2 k11-mr-5" width="35" height="35" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"></svg>
                                                        </span>
                                                    </button>
                                                </div>
                                            </form>
                                            <div class="collapse position-relative w-100" id="modelNumberCollapse" style="top: -6px">
                                                <div class="collapsible-content" style="padding: 10px;position: relative;border: 1px solid #74cb7b;border-top: 0;border-bottom-right-radius: 8px;border-bottom-left-radius: 8px;">
                                                    <div class="align-items-center">
                                                        <div class="k11-p-10 k11-bg-white" style="max-height: 200px; overflow-y: auto;">
                                                            <ul class="list-unstyled mb-0 search-results-container">
                                                                <!-- Suchergebnisse -->
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endblock %}

                        {% block page_content_description %}
                            <div class="description-text">
                                <p class="k11-font-size-16 k11-font-weight-900 k11-mb-20">
                                    {{ 'ersatzteilshop.productDetail.productDescriptionHeading'|trans|sw_sanitize }}
                                </p>
                                {{ page.product.description|raw }}
                                <div class="pl-0 k11-mt-20">
                                    <div class="product-properties">
                                        {% set all_properties = [] %}

                                        {# Add sorted properties #}
                                        {% for group in page.product.sortedProperties %}
                                            {% set all_properties = all_properties|merge([{
                                                'name': group.translated.name,
                                                'value': group.options|map(option => option.translated.name)|join(', '),
                                                'is_aswo': false
                                            }]) %}
                                        {% endfor %}

                                        {# Add features #}
                                        {% set features = [] %}
                                        {% if aswo_property %}
                                            {% set features = product_properties(page.product.customFields.product_properties) %}
                                        {% else %}
                                            {% set features = product_properties(page.product.translated.customFields.product_properties) %}
                                        {% endif %}

                                        {% for feature in features %}
                                            {% set all_properties = all_properties|merge([{
                                                'name': feature.name,
                                                'value': feature.value,
                                                'is_aswo': aswo_property
                                            }]) %}
                                        {% endfor %}

                                        {# Render all properties #}
                                        {% for property in all_properties %}
                                            <div class="d-grid k11-grid-template-2 k11-gap-20{% if not loop.last %} k11-border-bottom-primary k11-pb-20 k11-mb-20{% endif %}{% if property.is_aswo %} is-aswo-property{% endif %}">
                                                <div class="property-label k11-font-weight-900">{{ property.name|e }}:</div>
                                                <div class="property-value">{{ property.value|e }}</div>
                                            </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        {% endblock %}
                    </div>
                </div>
            {% endblock %}
        {% endblock %}
    </div>
{% endblock %}

