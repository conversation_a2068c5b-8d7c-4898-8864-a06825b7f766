{% block element_video %}
    <div style="position: relative; {% if overview %} display: inline-block; {% endif %}" data-youtube-id="{{ youtubeId }}">
        {% if not noMeta and video and video.translated.name and video.translated.description %}
            <div itemscope itemtype="https://schema.org/VideoObject">
                <meta itemprop="thumbnailUrl" content="https://img.youtube.com/vi_webp/{{ youtubeId }}/{{ overview ? 'hqdefault' : 'maxresdefault' }}.webp"/>
                <meta itemprop="name" content="{{ video.translated.name }}"/>
                <meta itemprop="description" content="{{ video.translated.description }}"/>
                <meta itemprop="uploadDate" content="{{ video.createdAt ? video.createdAt|date : '' }}"/>
                <meta itemprop="embedUrl" content="https://www.youtube-nocookie.com/embed/{{ youtubeId }}"/>
            </div>
        {% endif %}
        {% if overview %}
            <div class="video-container" style="position: relative; width: {{ width }}px; height: {{ height }}px; background-color: #000; cursor: pointer;">
                <img class="youtube_thumbnail_overview" loading="{{ loading }}" src="https://img.youtube.com/vi_webp/{{ youtubeId }}/{{ overview ? 'hqdefault' : 'maxresdefault' }}.webp" alt="youtube_thumnail">
                <img loading="{{ loading }}" class='playbutton' src="{{ asset('bundles/ersatzteilshop/assets/img/play_black.svg') }}" alt="playbutton_logo"/>
            </div>
        {% else %}
            {% if youtubeId %}
                <iframe
                        {% if width is not empty %}
                            width="{{ width }}"
                        {% endif %}
                        {% if height is not empty %}
                            height="{{ height }}"
                        {% endif %}
                        id="myYoutubeVideo"
                        src="https://www.youtube-nocookie.com/embed/{{ youtubeId }}?enablejsapi=1"
                        frameborder="0"
                        allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
                        allowfullscreen
                        scrolling="no"
                        style="background-color: #000; display:block; width: 100vw;"
                ></iframe>
            {% endif %}
        {% endif %}

        {% if overview %}
            <p class="video-paragraph">{{ video.translated.title }}</p>
            <a data-controller="prefetch" href="{{ seoUrl('frontend.video.library.page', {'videoLibraryId': video.id}) }}" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; z-index: 10; background: transparent; cursor: pointer;"></a>
        {% endif %}
    </div>
{% endblock %}
