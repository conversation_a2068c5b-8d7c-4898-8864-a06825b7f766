{% sw_extends '@Storefront/storefront/page/content/index.html.twig' %}

{% block base_head %}
    {{ parent() }}

    {% set youtubeMetadata = null %}
    {% if video.translations is defined and video.translations is not empty %}
        {% set youtubeMetadata = video.translations.first.youtubeMetadata %}
    {% endif %}
    {% set videoDescription = video.description ?? video.seoShortDescription ?? '' %}
    {% set videoTitle = video.title %}
    {% set videoThumbnailUrl = "https://img.youtube.com/vi/" ~ video.youtubeId ~ "/0.jpg" %}
    {% set videoUploadDate = video.createdAt|date('c') %}
    {% set videoContentUrl = "https://youtu.be/" ~ video.youtubeId %}
    {% set videoEmbedUrl = "https://www.youtube.com/embed/" ~ video.youtubeId %}
    {% set videoDuration = null %}
    
    {% if youtubeMetadata is defined and youtubeMetadata is not null %}
        {% if youtubeMetadata.title is defined and youtubeMetadata.title is not empty %}
            {% set videoTitle = youtubeMetadata.title %}
        {% endif %}
        
        {% if youtubeMetadata.aiDescription is defined and youtubeMetadata.aiDescription is not empty %}
            {% set videoDescription = youtubeMetadata.aiDescription %}
        {% elseif youtubeMetadata.description is defined and youtubeMetadata.description is not empty %}
            {% set videoDescription = youtubeMetadata.description %}
        {% endif %}
        
        {% if youtubeMetadata.thumbnailUrl is defined and youtubeMetadata.thumbnailUrl is not empty %}
            {% set videoThumbnailUrl = youtubeMetadata.thumbnailUrl %}
        {% endif %}
        
        {% if youtubeMetadata.uploadDate is defined and youtubeMetadata.uploadDate is not empty %}
            {% set videoUploadDate = youtubeMetadata.uploadDate %}
        {% endif %}
        
        {% if youtubeMetadata.duration is defined and youtubeMetadata.duration is not empty %}
            {% set videoDuration = youtubeMetadata.duration %}
        {% endif %}
        
        {% if youtubeMetadata.contentUrl is defined and youtubeMetadata.contentUrl is not empty %}
            {% set videoContentUrl = youtubeMetadata.contentUrl %}
        {% endif %}
        
        {% if youtubeMetadata.embedUrl is defined and youtubeMetadata.embedUrl is not empty %}
            {% set videoEmbedUrl = youtubeMetadata.embedUrl %}
        {% endif %}
    {% endif %}
    
    {% set videoJsonData = {
        "@context": "https://schema.org",
        "@type": "VideoObject",
        "name": videoTitle,
        "description": videoDescription|striptags,
        "thumbnailUrl": videoThumbnailUrl,
        "uploadDate": videoUploadDate,
        "contentUrl": videoContentUrl,
        "embedUrl": videoEmbedUrl
    } %}
    
    {% if videoDuration is not null %}
        {% set videoJsonData = videoJsonData|merge({
            "duration": videoDuration
        }) %}
    {% endif %}
    
    {% if youtubeMetadata is defined and youtubeMetadata is not null and youtubeMetadata.viewCount is defined and youtubeMetadata.viewCount is not null %}
        {% set videoJsonData = videoJsonData|merge({
            "interactionStatistic": {
                "@type": "InteractionCounter",
                "interactionType": { "@type": "WatchAction" },
                "userInteractionCount": youtubeMetadata.viewCount
            }
        }) %}
    {% endif %}

    <script type="application/ld+json">
      {{ videoJsonData|json_encode(constant('JSON_PRETTY_PRINT'))|raw }}
    </script>
{% endblock %}

{% block page_content %}
    <div class="container mt-5">
        {% block page_breadcrumb %}
            <a class="go-back-link pt-2" href="{{ seoUrl('frontend.video.library.overview') }}" itemprop="item"><span
                        itemprop="name"> {{ 'video-library.detail.backBreadcrumb'|trans|sw_sanitize }}</span></a>
        {% endblock %}
    </div>
    <div class="cms-page">
        {% block page_content_blocks %}
            {% sw_include "@Storefront/storefront/page/content/detail.html.twig" with {'cmsPage': page.videoLibrary.cmsPage} %}
        {% endblock %}
    </div>
{% endblock %}
