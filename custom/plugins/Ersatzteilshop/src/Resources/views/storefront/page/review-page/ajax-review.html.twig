{% for review in shopReviews %}

    <div class="mt-3">
        <div class="main-box no margin mb-3">
            <div class="header row">
                <div class="review-stars col-auto pr-9">
                    <div class="loading-line">
                        {% for i in 1..5 %}
                            {% if i <= review.stars %}
                                {% sw_icon 'star' style { 'color': 'review', 'size': size, 'pack': 'solid' } %}
                            {% else %}
                                {% sw_icon 'star' style { 'color': 'light', 'size': size, 'pack': 'solid' } %}
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
                <div class="d-none d-md-block col-md-6">
                    <a href="#" class="author-info">{{ review.autor }}</a>
                </div>
                <div class="date-loader review-meta col-auto ml-auto">
                    <p><small>{{ review.createdAt|date }}</small></p>
                </div>
                <div class="d-block d-md-none col-md-6">
                    <a href="#" class="author-info">{{ review.autor }}</a>
                </div>
            </div>
            <div class="review-description">
                <p>
                    {{ review.text }}
                </p>
            </div>
            <div class="footer row">
                <div class="review-stars col-auto pr-9">
                    <div class="loading-line">
                        <span class="d-none d-md-inline">
                            <img height="20px" width="auto" src="{{ asset('bundles/ersatzteilshop/assets/icons/Upvotebutton.png') }}">
                            ( {{ review.numberOfLikes }} )
                        </span>
                        <span class="d-none d-md-inline">&nbsp;</span>
                        <span class="d-none d-md-inline">{{ 'reviewPage.review.helpful'|trans }}</span>
                    </div>
                </div>
                <div class="col-12 col-md-6">
                    <a href="#" class="spacer"></a>
                </div>
                <div class="date-loader review-meta col-auto ml-auto">
                    <img height="20px" width="auto" src="{{ asset('bundles/ersatzteilshop/assets/icons/verification.png') }}">
                    {{ 'reviewPage.review.verified'|trans }}</div>
            </div>
        </div>
    </div>
{% endfor %}
