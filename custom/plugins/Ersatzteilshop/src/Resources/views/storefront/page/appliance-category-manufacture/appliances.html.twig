{% sw_extends '@Storefront/storefront/page/_page.html.twig' %}

{% block base_head %}
    {% sw_include '@Storefront/storefront/page/appliance-category-manufacture/appliances-meta.html.twig' %}
    <link rel="preload" href="{{ asset('bundles/ersatzteilshop/assets/css/datatable.css') }}" type="text/css" as="style" onload="this.onload=null;this.rel='stylesheet';">
{% endblock %}
{% block base_main_container %}
    <div class="container-main">
        {% block page_main_content %}
            {% block page_breadcrumb %}
                <ul class="breadcrumb" itemscope="" itemtype="http://schema.org/BreadcrumbList">
                    <li class="breadcrumb-item" itemprop="itemListElement" itemscope=""
                        itemtype="http://schema.org/ListItem">
                        <a href="{{ seoUrl('frontend.home.page') }}" itemprop="item"><span
                                itemprop="name">{{ 'ersatzteilshop.breadcrumb.home'|trans|sw_sanitize }}</span></a>
                        <meta itemprop="position" content="1">
                    </li>
                    <li class="breadcrumb-item" itemprop="itemListElement" itemscope=""
                        itemtype="http://schema.org/ListItem">
                        <a href="/ersatzteile-fuer/uebersicht" itemprop="item">
                            <span itemprop="name">{{ 'ersatzteilshop.breadcrumb.spareparts'|trans|sw_sanitize }}</span></a>
                        <meta itemprop="position" content="2">
                    </li>
                    {% if page.category %}
                        <li class="breadcrumb-item" itemprop="itemListElement" itemscope=""
                            itemtype="http://schema.org/ListItem">
                            <a href="{{ seoUrl('frontend.appliance.category.page', {'categoryId': page.category.id}) }}"
                               itemprop="item">
                                <span itemprop="name">{{ page.category.translated.name }}</span>
                            </a>
                            <meta itemprop="position" content="3">
                        </li>
                    {% endif %}
                    {% if page.manufacturer %}
                        <li class="breadcrumb-item" itemprop="itemListElement" itemscope=""
                            itemtype="http://schema.org/ListItem">
                            <span itemprop="name">{{ page.manufacturer.translated.name }}</span>
                            <meta itemprop="position" content="4">
                        </li>
                    {% endif %}
                </ul>
            {% endblock %}
            {% block page_content %}
                <main class="content-main">
                    {% block base_content_heading %}
                        <h1 class="pt-3">
                            {{ page.metaInformation.extensions.headingText.value | raw }}
                        </h1>
                    {% endblock %}

                    {% block appliance_manufacturers %}
                        <div id="product-appliances" class="product-appliances">
                            <div class="appliance-list">
                                <div class="dataTable-search w-100 p-0 k11-mt-25 k11-mb-20">
                                    <div class="d-grid k11-grid-template-1 k11-md-grid-template-2 k11-gap-10">
                                        {% set queryString = app.request.query.get('search') %}
                                        {% set seoUrl = seoUrl('frontend.appliance.manufacturer-category.page', {'categoryId': page.category.id, 'manufacturerId': page.manufacturer.id}) %}
                                        <form action="{{ seoUrl }}" method="get" class="k11-border-active d-flex align-items-center">
                                            <input class="input-group search-input border-0 k11-border-radius-8 k11-pl-15 form-control"
                                                   placeholder="Typennummer / Modellnummer eingeben"
                                                   type="search" name="search"
                                                   autocomplete="off" autocapitalize="off"
                                                   maxlength="30" value="{{ queryString ? queryString : '' }}">
                                            <div class="svg-container">
                                                <button class="search-input--btn border-0 p-0 outline-none bg-none" type="submit">
                                                    <svg class="k11-p-2" width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <rect width="34" height="34" rx="5" fill="#202E3D"/>
                                                        <path d="M5 27.1333L13.4 18.7333C12.8667 18.0667 12.4444 17.3 12.1333 16.4333C11.8222 15.5667 11.6667 14.6444 11.6667 13.6667C11.6667 11.2444 12.5056 9.19444 14.1833 7.51667C15.8611 5.83889 17.9111 5 20.3333 5C22.7556 5 24.8056 5.83889 26.4833 7.51667C28.1611 9.19445 29 11.2444 29 13.6667C29 16.0889 28.1611 18.1389 26.4833 19.8167C24.8056 21.4944 22.7556 22.3333 20.3333 22.3333C19.3556 22.3333 18.4333 22.1778 17.5667 21.8667C16.7 21.5556 15.9333 21.1333 15.2667 20.6L6.86667 29L5 27.1333ZM14.3333 13.6667C14.3333 15.3333 14.9167 16.75 16.0833 17.9167C17.25 19.0833 18.6667 19.6667 20.3333 19.6667C22 19.6667 23.4167 19.0833 24.5833 17.9167C25.75 16.75 26.3333 15.3333 26.3333 13.6667C26.3333 12 25.75 10.5833 24.5833 9.41667C23.4167 8.25 22 7.66667 20.3333 7.66667C18.6667 7.66667 17.25 8.25 16.0833 9.41667C14.9167 10.5833 14.3333 12 14.3333 13.6667Z" fill="white"/>
                                                    </svg>
                                                </button>
                                            </div>
                                        </form>
                                        <div class="k11-border d-flex justify-content-center k11-pb-5">
                                            <a class="k11-mt-5 text-decoration-none"
                                               style="color: #000000"
                                               href="/widgets/type-number"
                                               data-toggle="modal"
                                               title="Hilfe zur Bestimmung der Modellnummer"
                                               data-url="/widgets/type-number"
                                               data-modal-class="search-widget-modal"
                                            >
                                                <svg class="k11-mr-10" width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <mask id="mask0_2458_566290" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="25">
                                                        <rect x="0.5" y="0.766968" width="24" height="24" fill="#D9D9D9"/>
                                                    </mask>
                                                    <g mask="url(#mask0_2458_566290)">
                                                        <path d="M12.5 18.767C12.85 18.767 13.1458 18.6461 13.3875 18.4045C13.6292 18.1628 13.75 17.867 13.75 17.517C13.75 17.167 13.6292 16.8711 13.3875 16.6295C13.1458 16.3878 12.85 16.267 12.5 16.267C12.15 16.267 11.8542 16.3878 11.6125 16.6295C11.3708 16.8711 11.25 17.167 11.25 17.517C11.25 17.867 11.3708 18.1628 11.6125 18.4045C11.8542 18.6461 12.15 18.767 12.5 18.767ZM16.1 9.71697C16.1 8.81697 15.7958 8.1003 15.1875 7.56697C14.5792 7.03363 13.775 6.76697 12.775 6.76697C12.025 6.76697 11.3625 6.92113 10.7875 7.22947C10.2125 7.5378 9.75833 7.98363 9.425 8.56697C9.30833 8.7503 9.30417 8.9503 9.4125 9.16697C9.52083 9.38363 9.69167 9.54197 9.925 9.64197C10.125 9.7253 10.3333 9.72947 10.55 9.65447C10.7667 9.57947 10.95 9.44197 11.1 9.24197C11.2833 8.99197 11.5125 8.8003 11.7875 8.66697C12.0625 8.53363 12.3583 8.46697 12.675 8.46697C13.1417 8.46697 13.5208 8.59197 13.8125 8.84197C14.1042 9.09197 14.25 9.40863 14.25 9.79197C14.25 10.092 14.15 10.4086 13.95 10.742C13.75 11.0753 13.45 11.4086 13.05 11.742C12.6167 12.1253 12.2917 12.4836 12.075 12.817C11.8583 13.1503 11.7167 13.542 11.65 13.992C11.6167 14.2253 11.6875 14.4378 11.8625 14.6295C12.0375 14.8211 12.2583 14.917 12.525 14.917C12.7583 14.917 12.9708 14.8378 13.1625 14.6795C13.3542 14.5211 13.475 14.3086 13.525 14.042C13.575 13.7753 13.6667 13.5378 13.8 13.3295C13.9333 13.1211 14.1667 12.8503 14.5 12.517C15.1333 11.8836 15.5583 11.3753 15.775 10.992C15.9917 10.6086 16.1 10.1836 16.1 9.71697ZM5.5 21.767C4.95 21.767 4.47917 21.5711 4.0875 21.1795C3.69583 20.7878 3.5 20.317 3.5 19.767V5.76697C3.5 5.21697 3.69583 4.74613 4.0875 4.35447C4.47917 3.9628 4.95 3.76697 5.5 3.76697H19.5C20.05 3.76697 20.5208 3.9628 20.9125 4.35447C21.3042 4.74613 21.5 5.21697 21.5 5.76697V19.767C21.5 20.317 21.3042 20.7878 20.9125 21.1795C20.5208 21.5711 20.05 21.767 19.5 21.767H5.5Z" fill="#202E3D"/>
                                                    </g>
                                                </svg>
                                                <span><u>{{ "search.searchWidgetLink"|trans|striptags }}</u></span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="appliance-list-container" style="max-height: 500px;overflow-y: scroll;clear: both; border-bottom: 1px solid #d9d9d9">
                                    <table class="table appliance-list-table mb-0">
                                        <thead class="thead-dark">
                                        <tr>
                                            <th scope="col">{{ 'ersatzteilshop.appliance.listing.manufacturer'|trans|sw_sanitize }}</th>
                                            <th scope="col">{{ 'ersatzteilshop.appliance.listing.modelName'|trans|sw_sanitize }}</th>
                                            <th scope="col">{{ 'ersatzteilshop.appliance.listing.modelCode'|trans|sw_sanitize }}</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        {% set baseUrl = app.request.attributes.get('sw-storefront-url') %}
                                        {% set salesChannelId = app.request.attributes.get('sw-sales-channel-id') %}
                                        {% set appliances = appliancesData['appliances'] %}
                                        {% set pagination = appliancesData['pagination'] %}
                                        {% for appliance in appliances %}
                                            {% set applianceUrl = (baseUrl | trim('/', 'right') ~ '/' ~ appliance['seo_url'] | trim('/', 'left')) %}
                                            <tr data-target-url="{{ applianceUrl }}" style="transform: rotate(0);">
                                                <td scope="row">
                                                    <a class="appliance-link stretched-link"
                                                       href="{{ applianceUrl }}">
                                                        {{ appliance['manufacturer'] }}
                                                    </a>
                                                </td>
                                                <td>
                                                    <span>
                                                        {{ appliance['model_name'] }}
                                                    </span></td>
                                                <td>
                                                    <span>
                                                        {{ appliance['model_code'] }}
                                                    </span>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                <div class="cms-element-product-listing-actions row pagination-bottom pb-3 pt-4">
                                    <div class="col-md-auto">
                                        {% sw_include '@Storefront/storefront/page/appliance-category-manufacture/pagination.html.twig' with {
                                            current: pagination['current'],
                                            total: pagination['total']
                                        } %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endblock %}

                    {% block category_description %}
                        <div class="category-description">
                            {% if page.category.translated.description %}
                                {{ page.category.translated.description|raw }}
                            {% endif %}
                        </div>
                    {% endblock %}

                    {% block category_seo %}
                        <div class="category-seo">
                            {% if page.category.translated.customFields.category_seo_desc %}
                                {{ page.category.translated.customFields.category_seo_desc|raw }}
                            {% endif %}
                        </div>
                    {% endblock %}

                    {% block page_question_one %}
                        <div class="pt-5">
                            <h3>{{ 'ersatzteilshop.landingpage.questionOne'|trans }}</h3>
                            {{ 'ersatzteilshop.landingpage.answerOne'|trans|raw }}
                        </div>
                    {% endblock %}

                    {% block page_question_two %}
                        <div class="pt-4">
                            <h3>{{ 'ersatzteilshop.landingpage.questionTwo'|trans }}</h3>
                            {{ 'ersatzteilshop.landingpage.answerTwo'|trans|raw }}
                            <p><a href="/hilfe/fragen-zu-artikeln/das-typenschild-meines-geraetes-finden"
                                  style="text-decoration: underline">{{ 'ersatzteilshop.landingpage.link'|trans }}</a>
                            </p>
                        </div>
                    {% endblock %}

                    {% block page_question_three %}
                        <div class="pt-4">
                            <h3>{{ 'ersatzteilshop.landingpage.questionThree'|trans }}</h3>
                            {{ 'ersatzteilshop.landingpage.answerThree'|trans|raw }}
                        </div>
                    {% endblock %}
                </main>
            {% endblock %}
        {% endblock %}
    </div>
{% endblock %}
