{% sw_extends '@Ersatzteilshop/storefront/page/category/index.v2.html.twig' %}
{#
  Template is used for websites like https://www.ersatzteilshop.de/ofen/bosch/
  ( So Category LVL 3 with Manufacturer )
#}

{% block base_head %}
    {% sw_include '@Storefront/storefront/page/category/product/meta.html.twig' %}
{% endblock %}

{% block page_sidebar_sections %}
    {% if page.category.extensions.journey.getData() %}
        {% set manufacturerSeoName = page.manufacturer.translated.name ? page.manufacturer.translated.name|lower : '' %}
        {% set manufacturerSeoName = manufacturerSeoName|replace({' ': '-'}) %}
        {% set manufacturerSeoName = manufacturerSeoName|replace({':': '-'}) %}
        {% set manufacturerSeoName = manufacturerSeoName|replace({'&': '-'}) %}
        {% set manufacturerSeoName = manufacturerSeoName|replace({'/': '-'}) %}
        {% set manufacturerSeoName = manufacturerSeoName|trim('-') %}
        {% set manufacturerSeoName = manufacturerSeoName ? manufacturerSeoName ~ '/' : '' %}
        {{ page.category.extensions.journey.getData().sidebar|replace({'%manufacturer%': manufacturerSeoName})|raw }}
    {% else %}
        {% sw_include '@Ersatzteilshop/storefront/page/category/product/sidebar.html.twig' %}
    {% endif %}
{% endblock %}

{% block page_breadcrumb %}
    {% sw_include '@Storefront/storefront/page/category/product/breadcrumb.html.twig' with {
        category: page.category
    } %}
    {% block page_back_button %}
        {% if page.category.level == 2 %}
            <a class="account-page-back" data-link-masking=true data-href="{{ path('frontend.home.page')|maskHref }}">{{ 'account.backButton'|trans|sw_sanitize }}</a>
        {% else %}
            <a class="account-page-back" data-link-masking=true data-href="{{ path('frontend.navigation.page', { navigationId: page.category.parentId })|maskHref }}">{{ 'account.backButton'|trans|sw_sanitize }}</a>
        {% endif %}
    {% endblock %}
{% endblock %}

{% block page_content_headline %}
    <h1>{{ page.metaInformation.extensions.headingText.value | raw }}</h1>
{% endblock %}

{% block page_content %}
    {% set loadManufacturerSlider = "true" %}
    {{ block('page_content_tabs') }}
    {{ block('page_content_headline') }}

    {% sw_include "@Storefront/storefront/page/category/product/product-category-filter.html.twig" %}

    <div class="category-description">
        {% if page.categoryShortDesc %}
            {% sw_include '@Ersatzteilshop/storefront/component/category/expandable-description.html.twig' with {
                content: page.categoryShortDesc,
                id: 'category-short-desc-v2'
            } %}
        {% endif %}

    </div>

    {# Nicht anzeigen, wenn ein Hersteller bereits ausgewählt ist #}
    {% if page.manufacturer == null %}
        {# Top manufacturers #}
        <div>
            {% sw_include '@Storefront/storefront/component/manufacturer/top-brands.html.twig' with {
                manufacturer: page.manufacturers
            } %}
        </div>

        {# All Brands #}
        {% sw_include '@Storefront/storefront/component/manufacturer/all-brands.html.twig' with {
            manufacturer: page.manufacturers
        } %}
    {% endif %}

    {# Top categories #}
    {% if page.extensions.topCategories and page.extensions.topCategories.count %}
        <div class="top-categories">
            <h2 class="category-section-title">{{ 'ersatzteilshop.page.topCategoryHeading'|trans({'%category%': page.category.translated.name}) }}</h2>
            {% sw_include '@Storefront/storefront/component/category/category-slider.html.twig' with {
                categories: page.extensions.topCategories,
                manufacturer: page.manufacturer
            } %}
        </div>
    {% endif %}

    {# Mobile category view #}
    {% if page.extensions.childrenCategories and page.extensions.childrenCategories.count %}
        <div class="mobile-all-categories">
            <h2 class="category-section-title">{{ 'ersatzteilshop.page.allCategoryHeading'|trans({'%category%': page.category.translated.name}) }}</h2>
            {% sw_include '@Storefront/storefront/component/category/mobile-category-view.html.twig' with {
                categories: page.extensions.childrenCategories,
                manufacturer: page.manufacturer
            } %}
        </div>
    {% endif %}

    {# featured products #}
    {% if page.extensions.featuredProducts and page.extensions.featuredProducts.count %}
        <div class="featured-products">
            <h2 class="category-section-title">{{ 'ersatzteilshop.page.featureProductHeading'|trans({'%category%': page.category.translated.name}) }}</h2>
            {% sw_include '@Storefront/storefront/component/product/product-slider.html.twig' with {
                products: page.extensions.featuredProducts,
                numberItems: 4
            } %}
        </div>
    {% endif %}

    {# everdrop slider #}
    {% if page.extensions.cleaningProducts and page.extensions.cleaningProducts.count %}
        <div class="featured-products">
            <h2 class="category-section-title">{{ 'ersatzteilshop.page.cleaningProductHeading'|trans }}</h2>
            {% sw_include '@Storefront/storefront/component/product/product-slider.html.twig' with {
                products: page.extensions.cleaningProducts,
                numberItems: 4
            } %}
        </div>
    {% endif %}

    {# repair guides #}
    {% if page.extensions.repairGuides and page.extensions.repairGuides.count %}
        <div class="suitable-repair-guides">
            <h2 class="category-section-title">{{ 'ersatzteilshop.repairGuide.suitableRepairGuideHeading'|trans({'%category%': page.category.translated.name}) }}</h2>

            {% if page.category.customFields.category_repair_guide_desc %}
                <p>{{ page.category.customFields.category_repair_guide_desc|raw }}</p>
            {% endif %}

            <p>{{ 'ersatzteilshop.repairGuide.suitableRepairGuideIntro'|trans }}</p>

            <div class="repair-guide-list">
                {% for repairGuide in page.extensions.repairGuides %}
                    <div class="suitable-repair-guide-item">
                        <a href="{{ seoUrl('frontend.repair.guide.page', { repairGuideId: repairGuide.id }) }}"
                           title="{{ repairGuide.translated.title }}">{{ repairGuide.translated.title }}</a>
                    </div>
                {% endfor %}
            </div>
            <div class="row">
                <div class="offset-md-6 col-md-6">
                    {% if page.extensions.repairGuides|first.categoryId %}
                        <a href="{{ seoUrl('frontend.navigation.page', {'navigationId': page.extensions.repairGuides|first.categoryId}) }}"
                           class="btn btn-primary btn-repair-guide">
                            {{ 'ersatzteilshop.repairGuide.btnRepairGuide'|trans }}
                        </a>
                    {% else %}
                        <a href="{{ 'ersatzteilshop.repairGuide.linkRepairGuide'|trans }}"
                           class="btn btn-primary btn-repair-guide">
                            {{ 'ersatzteilshop.repairGuide.btnRepairGuide'|trans }}
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    {% endif %}

    {# banner #}
    {% for tag in page.category.tags %}
        {% if tag.name is same as('banner') %}
            <a href="https://ersatzteilshopde.perspectivefunnel.com/61333546b046d30020786450/628b8b3921fd6702033651da/page_1edsloh/"
               target="_blank">
                <div class="banner-thermomix mt-5">

                </div>
            </a>
        {% endif %}
    {% endfor %}

    {# videoLibrary #}
    {% if page.extensions.videos and page.extensions.videos.count %}
        {% sw_include '@Storefront/storefront/component/video/video-library-slider.html.twig' with {
            videos: page.extensions.videos
        } %}
    {% endif %}

    {# blog #}
    {% if page.extensions.blogs and page.extensions.blogs.count %}
        {% sw_include '@Storefront/storefront/component/blog/blog-slider.html.twig' with {
            blogs: page.extensions.blogs
        } %}
    {% endif %}

    {# Repair Instruction #}
    {% if page.extensions.repairInstructions and page.extensions.repairInstructions.count %}
        {% sw_include '@Storefront/storefront/component/repair-instruction/repair-instruction-slider.html.twig' with {
            repairInstructions: page.extensions.repairInstructions
        } %}
    {% endif %}

    {# product list #}
    {% if page.extensions.products %}
        {% set dataUrl = url('frontend.listing.page', { navigationId: page.category.id }) %}
        {% set params = {} %}
        {% set showRepartlyBanner = page.category.id in [
            'cfc66d18ef7c42b597b22ca5bebe5f18',
            'c9c32be946124357bc1bf3c3c802e6da',
            '2ad8bb76348a4b55b148a1aeea12cf64',
            '68f8a6a7a94c420c9b4deb0432e0dae5',
            '4c8df49ae177459d9fb2108f4451b835',
            'a0310176b38b40b4be1c70508f25f455',
            '4cebb76cd8ff4d4283eff3bd0f1ace15',
            '7165b675892e43f08d1522fb486508a2',
            'b432eb0cb7344f3597e2f44f751aa783',
            'c656a74956ad4299bc8a44b3e2576e91',
            'c01723e37fa9486ba10596355b42b021'
            ] %}

        {% if page.manufacturer %}
            {% set params = { manufacturer: page.manufacturer.id } %}
        {% endif %}
        <div class="product-listing-wrapper">
            {% sw_include '@Storefront/storefront/component/product/listing.html.twig' with {
                searchResult: page.extensions.products,
                listingColumns: 'col-md-12',
                boxLayout: 'full',
                filterUrl: '',
                dataUrl: dataUrl,
                params: params,
                showRepartlyBanner: showRepartlyBanner
            } %}
        </div>
    {% endif %}

    <div class="category-seo">
        {{ page.categoryLongDesc|raw }}
    </div>

{% endblock %}
