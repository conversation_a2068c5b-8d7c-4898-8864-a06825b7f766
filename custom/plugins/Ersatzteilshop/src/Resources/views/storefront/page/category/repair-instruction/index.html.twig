{% sw_extends '@Ersatzteilshop/storefront/page/category/index.html.twig' %}

{% block page_sidebar_sections %}
    {% sw_include '@Storefront/storefront/page/category/repair-instruction/sidebar.html.twig' %}
{% endblock %}

{% block page_breadcrumb %}
    {% sw_include '@Storefront/storefront/page/category/repair-instruction/breadcrumb.html.twig' %}
    {% block page_back_button %}
        {% if page.category.level == 1 %}
            <a class="account-page-back" data-link-masking=true data-href="{{ path('frontend.home.page')|maskHref }}">{{ 'account.backButton'|trans|sw_sanitize }}</a>
        {% else %}
            <a class="account-page-back" data-link-masking=true data-href="{{ path('frontend.navigation.page', { navigationId: page.category.parentId })|maskHref }}">{{ 'account.backButton'|trans|sw_sanitize }}</a>
        {% endif %}
    {% endblock %}
{% endblock %}

{% block page_content %}
    <div class="repair-instruction-main" {% if page.extensions.repairInstructions|length > 0 %} itemscope itemtype="https://schema.org/FAQPage" {% endif %}>
        <h1 class="repair-instruction-heading">
            {% if page.category.level < 3 or page.category.media is null %}
                <i class="fa fa-book"></i>
            {% else %}
                <img src="{{ page.category.media.url }}" alt="{{ page.category.translated.name }}"/>
            {% endif %}
            <span itemprop="title">
                {% if page.category.level == 1 %}
                    {{ 'ersatzteilshop.repairInstruction.heading'|trans }}
                {% else %}
                    {{ 'ersatzteilshop.repairInstruction.categoryTitle'|trans({'%category%': page.category.translated.name}) }}
                {% endif %}
            </span>
        </h1>
        {{ block('page_content_tabs') }}

        {% if page.extensions.cmsPage %}
            <div class="repair-guide-cms-block">
                {% sw_include "@Storefront/storefront/page/content/detail.html.twig" with {'cmsPage': page.extensions.cmsPage} %}
                <br>
            </div>
        {% endif %}

        {% if page.category.level == 1 %}

            <div class="container repair-instruction-banner">
                <a class="repair-instruction-banner-link" href="{{ path('frontend.customer.repair.instruction.new') }}">
                    {{ 'ersatzteilshop.repairInstruction.bannerText'|trans }}
                </a>
            </div>
        {% endif %}

        {% if page.category.level == 2 %}
            <h2 class="repair-instruction-sub-categories-heading">
                {{ 'ersatzteilshop.repairInstruction.secondCategoryHeading'|trans({'%category%': page.category.translated.name}) }}
            </h2>
            <div class="repair-instruction-sub-categories-section">
                {% for category in page.extensions.childrenCategories %}
                    {% if category.media %}
                        {% set categoryImg = category.media.url %}
                    {% else %}
                        {% set categoryImg = asset('bundles/ersatzteilshop/assets/img/universelle-ersatzteile.jpg') %}
                    {% endif %}
                    <div class="repair-instruction-sub-category">
                        <a class="page-sidebar-category--item"
                           href="{{ seoUrl('frontend.navigation.page', { navigationId: category.id }) }}"
                           title="{{ category.translated.name }}">
                            <img src="{{ categoryImg }}" alt="{{ category.translated.name }}" loading="lazy"/>
                            <p>{{ category.translated.name }}</p>
                        </a>
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        {% if page.category.level < 3 %}
        <div class="category-seo">
            {{ page.categoryShortDesc|raw }}
        </div>
        {% endif %}

        {% if page.extensions.repairInstructions and page.extensions.repairInstructions.count %}
            {% sw_include "@Storefront/storefront/page/category/repair-instruction/list-repair-instruction.html.twig" with {
                repairInstructions: page.extensions.repairInstructions,
            } only %}
        {% endif %}
    </div>
{% endblock %}

{% block bottom_container %}

    {% if page.extensions.mostViewedRepairInstructions and page.extensions.mostViewedRepairInstructions.count %}
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    {% sw_include "@Storefront/storefront/page/category/repair-instruction/most-viewed-repair-instruction.html.twig" with {
                        repairInstructions: page.extensions.mostViewedRepairInstructions,
                        sliderTitle: 'ersatzteilshop.repairInstruction.mostViewedHeading'|trans
                    } only %}
                </div>
            </div>
        </div>
        <br>
    {% endif %}

    {% if page.extensions.topCategories and page.extensions.topCategories.count  %}
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <h3>{{ 'ersatzteilshop.repairInstruction.topCategoryHeading'|trans }}</h3>
                    {% sw_include '@Storefront/storefront/page/category/repair-instruction/category-slider.html.twig' with {
                        categories: page.extensions.topCategories
                    } %}
                </div>
            </div>
        </div>
        <br>
    {% endif %}
{% endblock %}
