{% sw_extends '@Storefront/storefront/page/_page.html.twig' %}

{% block base_script_hmr_mode %}
    <script type="text/javascript" src="{{ asset('bundles/ersatzteilshop/assets/js/vendor-node.js') }}"></script>
    <script type="text/javascript" src="{{ asset('bundles/ersatzteilshop/assets/js/vendor-shared.js') }}"></script>
    <script type="text/javascript" src="{{ asset('bundles/ersatzteilshop/assets/js/runtime.js') }}"></script>
    <script type="text/javascript" src="{{ asset('bundles/ersatzteilshop/assets/js/cms-detail.js') }}"></script>
{% endblock %}

{% block page_sidebar_sections %}
    <div class="page-sidebar-section">
        {% if page.category %}
            {% set parentCategories = page.category.breadcrumb|reverse %}
            <p class="page-sidebar-category-parent">
                <a href="{{ seoUrl('frontend.navigation.page', { navigationId: page.category.parentId }) }}">
                    <span class="parent-category-name">
                        {{ 'ersatzteilshop.repairGuide.sidebarHeading'|trans({'%category%': parentCategories[1]}) }}
                    </span>
                </a>
            </p>
            <div class="page-sidebar-category-active">{{ 'ersatzteilshop.repairGuide.categoryTitle'|trans({'%category%': page.category.translated.name}) }}</div>
        {% endif %}

        <ul class="page-sidebar-category">
            {% for repairGuide in page.sameCategoryRepairGuides %}
                <li class="repair-guide-sidebar {% if repairGuide.id == page.repairGuide.id %} active{% endif %}">
                    <a href="{{ seoUrl('frontend.repair.guide.page', { repairGuideId: repairGuide.id }) }}"
                       title="{{ repairGuide.translated.title }}">
                        {{ repairGuide.translated.title }}
                    </a>
                </li>
            {% endfor %}
        </ul>
    </div>
{% endblock %}

{% block page_breadcrumb %}
    {% set breadcrumb = sw_breadcrumb(page.category, context.salesChannel.navigationId) %}
    <ul class="breadcrumb" itemscope="" itemtype="http://schema.org/BreadcrumbList">
        <li class="breadcrumb-item" itemprop="itemListElement" itemscope="" itemtype="http://schema.org/ListItem">
            <a href="{{ seoUrl('frontend.home.page') }}" itemprop="item"><span itemprop="name">{{ 'ersatzteilshop.breadcrumb.home'|trans|sw_sanitize }}</span></a>
            <meta itemprop="position" content="1">
        </li>

        {% for key, item in breadcrumb %}
            <li class="breadcrumb-item" itemprop="itemListElement" itemscope="" itemtype="http://schema.org/ListItem">
                <a href="{{ seoUrl('frontend.navigation.page', { navigationId: key }) }}" itemprop="item">
                    <span itemprop="name">{{ item }}</span>
                </a>
                <meta itemprop="position" content="{{ loop.index + 1 }}">
            </li>
        {% endfor %}
        <li class="breadcrumb-item" itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
            {% set url = (app.request.attributes.get('sw-canonical-link') ?? (app.request.attributes.get('sw-sales-channel-absolute-base-url') ~ app.request.attributes.get('sw-original-request-uri')))|split('?') %}
            {% set url = url[0] %}
            <span itemprop="name">{{ page.repairGuide.translated.title }}</span>
            <meta itemprop="position" content="{{ breadcrumb|length + 2 }}">
            <meta itemprop="item" content="{{ url }}">
        </li>
    </ul>
    {% block page_back_button %}
        {% if page.category %}
            {% sw_include '@Ersatzteilshop/storefront/page/category/back-button.html.twig' with {
                category: page.category
            } %}
        {% endif %}
    {% endblock %}
{% endblock %}

{% block page_content %}
    {% set stepsCount = page.repairGuideSteps|length %}
    <div itemscope {% if stepsCount > 1 %}itemtype="https://schema.org/HowTo"{% endif %}>
        {% if page.repairGuide.customFields.ersatzteilshop_repair_guide_image.url %}
            <meta itemprop="image" content="{{ page.repairGuide.customFields.ersatzteilshop_repair_guide_image.url }}" />
        {% endif %}
        {% if page.repairGuide.customFields.ersatzteilshop_repair_guide_duration_iso_8601 %}
            <meta itemprop="totalTime" content="{{ page.repairGuide.customFields.ersatzteilshop_repair_guide_duration_iso_8601 }}" />
        {% endif %}

        {% block page_content_headline %}
            <h1 class="repair-guide-heading">
                {% if page.category.media %}
                    <img src="{{ page.category.media.url }}" alt="{{ page.category.translated.name }}">
                {% else %}
                    <img src="{{ asset('bundles/ersatzteilshop/assets/img/no_icon_cat.svg') }}" alt="{{ page.category.translated.name }}">
                {% endif %}
                <span itemprop="name">{{ page.metaInformation.extensions.headingText.value | raw }}</span>
            </h1>
        {% endblock %}

        <div class="repairguide-detail-container">
            <div class="info-container" itemprop="description">
                {{ page.repairGuide.translated.description|raw }}
            </div>

            <div class="steps-intro">
                {{ 'ersatzteilshop.repairGuide.stepIntro'|trans }}
            </div>
            {% set stepUrl = app.request.attributes.get('sw-canonical-link') %}
            {% for step in page.repairGuideSteps %}
            <div class="panel hide" itemprop="step" itemscope itemtype="https://schema.org/HowToStep">
                <meta itemprop="name" content="{{ step.translated.title  }}" />
                <a href="javascript:void(0);"
                   class="panel-head">
                    {% if loop.index == 1 %}
                        <span>{{ 'ersatzteilshop.repairGuide.step1Title'|trans({'%title%': step.translated.title})  }}</span>
                    {% else %}
                        <span>{{ 'ersatzteilshop.repairGuide.stepTitle'|trans({'%title%': step.translated.title, '%step%': loop.index})  }}</span>
                    {% endif %}
                    <svg class="toggle-icon" width="12" height="17" viewBox="0 0 12 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M2.26562 2.01172L8.67575 8.42184L2.26563 14.832" stroke="#202E3D" stroke-width="3.84607"/>
                    </svg>
                </a>
                <div class="panel-body" >
                    <div itemprop="text">
                        {% if step.translated.youtube %}
                        <div class="row">
                            <div class="col12 col-md-6">
                                {{ step.translated.description|raw }}
                            </div>
                            <div class="col-12 col-md-6">
                                <iframe class="full_width" src="{{ step.translated.youtube }}" width="300" height="300" frameborder="0" allowfullscreen="allowfullscreen"></iframe>
                            </div>
                        </div>
                        {% else %}
                            {{ step.translated.description|raw }}
                        {% endif %}
                    </div>
                    {% if step.isOfferProduct %}
                        {% sw_include '@Ersatzteilshop/storefront/page/repair-guide/search-widget.html.twig' %}
                    {% endif %}
                    <meta itemprop="url" content="{{ stepUrl }}" />
                </div>
            </div>
            {% endfor %}
        </div>

        {# banner #}
        {% if page.bannerVisible %}
            <a href="https://ersatzteilshopde.perspectivefunnel.com/61333546b046d30020786450/628b8b3921fd6702033651da/page_1edsloh/" target="_blank">
                <div class="banner-thermomix mt-5">
                </div>
            </a>
        {% endif %}

        <div class="k11-mt-20">
            {% sw_include '@Ersatzteilshop/storefront/component/widgets/modelnumber-scanner-widget/modelnumber-scanner-widget.html.twig' %}
        </div>
    </div>
{% endblock %}
