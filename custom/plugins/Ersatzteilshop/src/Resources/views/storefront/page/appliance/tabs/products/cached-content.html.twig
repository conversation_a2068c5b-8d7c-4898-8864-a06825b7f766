{% set category = app.request.get('category', "") %}
<template id="no-products">
    {% if category == "" %}
        <div class="content-heading k11-mx-15" style="margin-top: 0px;">
            <div class="col text-left k11-px-20 k11-py-17">
                <span>
                    <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <mask id="mask0_3754_3663" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="25">
                            <rect y="0.5" width="24" height="24" fill="#D9D9D9"/>
                        </mask>
                        <g mask="url(#mask0_3754_3663)">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M9 20.5L12 23.5L15 20.5H19C19.55 20.5 20.0208 20.3042 20.4125 19.9125C20.8042 19.5208 21 19.05 21 18.5V4.5C21 3.95 20.8042 3.47917 20.4125 3.0875C20.0208 2.69583 19.55 2.5 19 2.5H5C4.45 2.5 3.97917 2.69583 3.5875 3.0875C3.19583 3.47917 3 3.95 3 4.5V18.5C3 19.05 3.19583 19.5208 3.5875 19.9125C3.97917 20.3042 4.45 20.5 5 20.5H9ZM12 8.5C12.2833 8.5 12.5208 8.40417 12.7125 8.2125C12.9042 8.02083 13 7.78333 13 7.5C13 7.21667 12.9042 6.97917 12.7125 6.7875C12.5208 6.59583 12.2833 6.5 12 6.5C11.7167 6.5 11.4792 6.59583 11.2875 6.7875C11.0958 6.97917 11 7.21667 11 7.5C11 7.78333 11.0958 8.02083 11.2875 8.2125C11.4792 8.40417 11.7167 8.5 12 8.5ZM11 9.5V16.5H13V9.5H11Z" fill="#202E3D"/>
                        </g>
                    </svg>
                </span>
                <span>
                    {{ 'ersatzteilshop.appliance.search.empty.description'|trans|sw_sanitize }}
                </span>
            </div>
        </div>
    {% endif %}
</template>
<div class="tab-pane fade{% if active %} show active{% endif %}"
     id="product-list"
     role="tabpanel"
     aria-labelledby="product-list-tab">

    <div id="appliance-category-filter"
         data-appliance-category="true"
         data-appliance-category-options="{{ {
             url: seoUrl('frontend.appliance.categories', {applianceId: appliance.id}),
             title: 'ersatzteilshop.appliance.filteredByCategory'|trans|sw_sanitize
         }|json_encode }}">
            <span class="input-loading-state">
                <svg width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect width="24" height="24" fill="white"/>
                    <path d="M12 22C10.6333 22 9.34167 21.7375 8.125 21.2125C6.90833 20.6875 5.84583 19.9708 4.9375 19.0625C4.02917 18.1542 3.3125 17.0917 2.7875 15.875C2.2625 14.6583 2 13.3667 2 12C2 10.6167 2.2625 9.32083 2.7875 8.1125C3.3125 6.90417 4.02917 5.84583 4.9375 4.9375C5.84583 4.02917 6.90833 3.3125 8.125 2.7875C9.34167 2.2625 10.6333 2 12 2C12.2833 2 12.5208 2.09583 12.7125 2.2875C12.9042 2.47917 13 2.71667 13 3C13 3.28333 12.9042 3.52083 12.7125 3.7125C12.5208 3.90417 12.2833 4 12 4C9.78333 4 7.89583 4.77917 6.3375 6.3375C4.77917 7.89583 4 9.78333 4 12C4 14.2167 4.77917 16.1042 6.3375 17.6625C7.89583 19.2208 9.78333 20 12 20C14.2167 20 16.1042 19.2208 17.6625 17.6625C19.2208 16.1042 20 14.2167 20 12C20 11.7167 20.0958 11.4792 20.2875 11.2875C20.4792 11.0958 20.7167 11 21 11C21.2833 11 21.5208 11.0958 21.7125 11.2875C21.9042 11.4792 22 11.7167 22 12C22 13.3667 21.7375 14.6583 21.2125 15.875C20.6875 17.0917 19.9708 18.1542 19.0625 19.0625C18.1542 19.9708 17.0958 20.6875 15.8875 21.2125C14.6792 21.7375 13.3833 22 12 22Z" fill="#202E3D"/>
                </svg>
            </span>
    </div>

    <div class="k11-border-green d-flex align-items-center k11-mt-20 k11-mb-40" id="appliance-search">
        <div class="input-group search-input border-0 k11-border-radius-8 k11-pl-15 form-control" style="line-height: 1;">
            <input type="text" placeholder="Suchbegriff" name="search" id="appliance-search-input" class="input-group search-input border-0 outline-none bg-none w-100" autocomplete="off"/>
            <input type="text" class="d-none" name="category"/>
        </div>
        <div class="svg-container">
            <button class="search-input--btn border-0 p-0 outline-none bg-none" type="submit">
                <svg class="search-input--btn border-0 outline-none bg-none" style="margin: 2px;" width="35" height="34" viewBox="0 0 35 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.473633" width="34" height="34" rx="4" fill="#74CB7B"/>
                    <mask id="mask0_4504_106546" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="5" y="5" width="25" height="24">
                        <rect x="5.47363" y="5" width="24" height="24" fill="#D9D9D9"/>
                    </mask>
                    <g mask="url(#mask0_4504_106546)">
                        <path d="M25.0736 26L18.7736 19.7C18.2736 20.1 17.6986 20.4167 17.0486 20.65C16.3986 20.8833 15.707 21 14.9736 21C13.157 21 11.6195 20.3708 10.3611 19.1125C9.1028 17.8542 8.47363 16.3167 8.47363 14.5C8.47363 12.6833 9.1028 11.1458 10.3611 9.8875C11.6195 8.62917 13.157 8 14.9736 8C16.7903 8 18.3278 8.62917 19.5861 9.8875C20.8445 11.1458 21.4736 12.6833 21.4736 14.5C21.4736 15.2333 21.357 15.925 21.1236 16.575C20.8903 17.225 20.5736 17.8 20.1736 18.3L26.4736 24.6L25.0736 26ZM14.9736 19C16.2236 19 17.2861 18.5625 18.1611 17.6875C19.0361 16.8125 19.4736 15.75 19.4736 14.5C19.4736 13.25 19.0361 12.1875 18.1611 11.3125C17.2861 10.4375 16.2236 10 14.9736 10C13.7236 10 12.6611 10.4375 11.7861 11.3125C10.9111 12.1875 10.4736 13.25 10.4736 14.5C10.4736 15.75 10.9111 16.8125 11.7861 17.6875C12.6611 18.5625 13.7236 19 14.9736 19Z" fill="#1C1B1F"/>
                    </g>
                </svg>
            </button>
        </div>
    </div>

    <div class="cms-element-product-listing">
        <div class="cms-listing-row js-listing-wrapper">
            {% sw_include '@Ersatzteilshop/storefront/page/appliance/components/cached/product-table.html.twig' with {positionNumbers: positionNumbers} %}
        </div>
    </div>
</div>
