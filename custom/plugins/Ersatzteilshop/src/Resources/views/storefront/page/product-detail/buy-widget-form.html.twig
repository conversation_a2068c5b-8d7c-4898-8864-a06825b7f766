{% sw_extends '@Storefront/storefront/page/product-detail/buy-widget-form.html.twig' %}

{% block page_product_detail_buy_quantity_container %}
    <div class="col availability">
        <span>{{ 'ersatzteilshop.productDetail.deliveryTimeLabel'|trans|sw_sanitize }}</span>
        <br>
        {% sw_include '@Storefront/storefront/component/product/delivery-time.html.twig' with {
            enableNotification: false
        } %}
        {% sw_include '@Ersatzteilshop/storefront/component/product/delivery-note.html.twig' ignore missing %}
    </div>
    {% if page.product.active %}
        <div class="col-6 col-md-3 quantity">
            {% block page_product_detail_buy_quantity %}
                <label>
                    {{ 'ersatzteilshop.productDetail.quantityInputLabel'|trans|sw_sanitize }}
                </label>
                <select name="lineItems[{{ product.id }}][quantity]"
                        class="custom-select product-detail-quantity-select" data-product-detail-quantity>
                    {% for quantity in range(product.minPurchase, product.calculatedMaxPurchase, product.purchaseSteps) %}
                        <option value="{{ quantity }}">
                            {{ quantity }}
                            {% if quantity == 1 %}
                                {% if product.packUnit %} {{ product.packUnit }}{% endif %}
                            {% else %}
                                {% if product.packUnitPlural %}
                                    {{ product.packUnitPlural }}
                                {% elseif product.packUnit %}
                                    {{ product.packUnit }}
                                {% endif %}
                            {% endif %}
                        </option>
                    {% endfor %}
                </select>
            {% endblock %}
        </div>
    {% endif %}
{% endblock %}

{% block page_product_detail_buy_button_container %}
    {% if page.product.active %}
        <div class="col-sm-12 col-md-6 buttons">
            {% block page_product_detail_buy_button %}
                <button class="btn btn-primary btn-block btn-buy"
                        style="width: 100%"
                        title="{{ "detail.addProduct"|trans|striptags }}"
                        aria-label="{{ "detail.addProduct"|trans|striptags }}">
                    {{ "detail.addProduct"|trans|sw_sanitize }}
                </button>

                {% set buyable = product.stock > 0 %}
                {% if buyable %}
                    {# @var \Swag\PayPal\Checkout\ExpressCheckout\ExpressCheckoutButtonData expressSettings #}
                    {% set expressSettings = page.extensions[constant('Swag\\PayPal\\Checkout\\ExpressCheckout\\ExpressCheckoutSubscriber::PAYPAL_EXPRESS_CHECKOUT_BUTTON_DATA_EXTENSION_ID')] %}

                    {% if expressSettings.productDetailEnabled %}
                        <div class="button_fix w-100 button_spacer">——— &nbsp; &nbsp;{{ 'checkout.or'|trans|striptags }}
                            &nbsp; &nbsp; ———
                        </div>
                        {% if app.request.session.get(constant('Ersatzteilshop\\Util\\ABTesting::LAZY_PAYPAL_BUTTON')) is same as(constant('Ersatzteilshop\\Util\\ABTesting::LAZY_PAYPAL_BUTTON_VERSION_A')) %}
                            {% sw_include '@SwagPayPal/storefront/component/ecs-spb-checkout/ecs-button.html.twig' with {button_class: 'paypal-button'} %}
                        {% else %}
                            <img class="placeholder-paypal-image d-none" width="100%" src="{{ asset('bundles/ersatzteilshop/assets/img/paypal-button.png') }}" />
                            <a href="#"
                               data-lazy-paypal-button="true"
                               data-lazy-paypal-button-options="{{ {
                                   paypalButtonClass: 'paypal-button',
                                   paypalExpressButtonOptions: expressSettings,
                                   paypalAddErrorToken: sw_csrf('payment.paypal.add_error', {"mode": "token"}),
                                   paypalBannerOptions: page.extensions[constant('Swag\\PayPal\\Installment\\Banner\\InstallmentBannerSubscriber::PAYPAL_INSTALLMENT_BANNER_DATA_EXTENSION_ID')],
                                   paypalButtonNote: 'checkout.paypalButtonNote'|trans|sw_sanitize
                               }|json_encode }}">
                                <img width="100%" src="{{ asset('bundles/ersatzteilshop/assets/img/paypal-button.png') }}" />
                            </a>
                        {% endif %}
                    {% endif %}

                {% endif %}

            {% endblock %}
        </div>
    {% endif %}
{% endblock %}

{% block page_product_detail_buy_container_paypal %}
    {% set buyable = product.stock > 0 %}
    {# @var \Swag\PayPal\Installment\Banner\BannerData installmentBanner #}
    {% set installmentBanner = page.extensions[constant('Swag\\PayPal\\Installment\\Banner\\InstallmentBannerSubscriber::PAYPAL_INSTALLMENT_BANNER_DATA_EXTENSION_ID')] %}

    {% if installmentBanner is not null %}
        <div class="form-row mt-3 mb-4 justify-content-end">
            <div class="{{ buyable ? 'col-8' : 'col-12' }}" data-swag-paypal-installment-banner="true"
                 data-swag-pay-pal-installment-banner-options="{{ installmentBanner|json_encode }}">
            </div>
        </div>
    {% endif %}
{% endblock %}


{% block page_product_detail_buy_container %}
    {% set buyable = product.stock > 0 %}

    {{ parent() }}
{% endblock %}
