{% sw_extends '@Storefront/storefront/page/_page.html.twig' %}

{% set deviceType = app.request.headers.get('User-Agent')|lower %}

{% if 'mobile' in deviceType %}
    {% set height = '210' %}
    {% set width = '380' %}
{% else %}
    {% set height ='165' %}
    {% set width = '300' %}
{% endif %}

{% set videos = page.videos.elements %}
{% set sortedVideos = videos|sort((a, b) => (b.cmsPageId ? 1 : 0) - (a.cmsPageId ? 1 : 0)) %}
{% set cmsEntities = sortedVideos|filter(video => video.cmsPageId ) %}
{% set videosWithoutCms = sortedVideos|filter(video => not video.cmsPageId and video.url) %}
{% set questionsNoCmsNoUrl  = sortedVideos|filter(video => not video.cmsPageId and not video.url) %}

{% block base_head %}
    {% sw_include '@Storefront/storefront/page/video-library/meta.html.twig' with {overview: true} %}
{% endblock %}
{% block base_main_inner %}
    {% block page_filter_section %}
    {% endblock %}
    
    <div class="container" data-attribute-video-library="true">
        {% block base_main_container %}
            <h1 class="content-main-paragraph pt-0">{{ 'video-library.overview.repairWithVideos'|trans|sw_sanitize }}</h1>
            {% block base_content_banner %}
                <figure class="content-banner">
                    <img
                            src="{{ asset('bundles/ersatzteilshop/assets/img/video_library_banner.png') }}"
                            loading="eager"
                            height="226"
                            class="content-banner__image d-none d-md-block"
                            alt="Video Library Banner Desktop"
                            title="Video Library Banner Desktop"
                    />
                    <img
                            src="{{ asset('bundles/ersatzteilshop/assets/img/video_library_banner_mobile.png') }}"
                            loading="eager"
                            height="226"
                            class="content-banner__image d-block d-md-none"
                            alt="Video Library Banner Mobile"
                            title="Video Library Banner Mobile"
                    />
                </figure>
                <p class="d-none d-md-block mb-0">{{ 'video-library.overview.bannerText'|trans|sw_sanitize }}</p>
            {% endblock %}
            
            <div class="container-main video-library">
                <div class="table">
                    <p class="video-library_table_paragraph d-none d-md-block">
                        <strong>{{ 'video-library.overview.selectDevice'|trans|sw_sanitize }}</strong>
                    </p>
                    <ul class="result-list d-none d-md-flex">
                        {% for applianceType in page.applianceTypes|sort((a, b) => a.name <=> b.name) %}
                            {% if applianceType.name %}
                                <li class="filter-deselected {% if loop.index > 100 %} expandable {% endif %}">
                                    <span class="link-deselected" data-appliance="true" data-appliance-value="{{ applianceType.value }}">{{ applianceType.name }}</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                    </ul>
                    <div class="d-md-none collapsed mobile-toggle" data-toggle="collapse" data-target="#collapsible-1" role="button">
                        <div>
                            <p class="mb-0">{{ 'video-library.overview.selectDevice'|trans|sw_sanitize }}</p>
                        </div>
                        <button class="toggle-icon">
                            <svg width="12" height="17" viewBox="0 0 12 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path
                                        d="M2 2L8.41012 8.41012L2 14.8202"
                                        stroke="#202E3D"
                                        stroke-width="3.84607"
                                ></path>
                            </svg>
                        </button>
                    </div>
                    <div class="collapse" id="collapsible-1">
                        <ul class="result-list d-flex d-md-none">
                            {% for applianceType in page.applianceTypes %}
                                {% if applianceType.name %}
                                    <li class="filter-deselected {% if loop.index > 100 %} expandable {% endif %}">
                                        <span class="link-deselected" data-appliance="true" data-appliance-value="{{ applianceType.value }}">{{ applianceType.name }}</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                
                <div class="table">
                    <p class="video-library_table_paragraph d-none d-md-block">
                        <strong>{{ 'video-library.overview.selectManufacturer'|trans|sw_sanitize }}</strong>
                    </p>
                    <ul class="result-list d-none d-md-flex">
                        {% for manufacturerType in page.manufacturerTypes %}
                            {% if manufacturerType != "Alle" %}
                                <li class="filter-deselected {% if loop.index > 100 %} expandable {% endif %}">
                                    <span class="link-deselected" data-manufacturer="true" data-manufacturer-value="{{ manufacturerType }}">{{ manufacturerType }}</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                    </ul>
                    <div class="d-md-none collapsed mobile-toggle" data-toggle="collapse" data-target="#collapsible-2" role="button">
                        <div>
                            <p class="mb-0">{{ 'video-library.overview.selectManufacturer'|trans|sw_sanitize }}</p>
                        </div>
                        <button class="toggle-icon">
                            <svg width="12" height="17" viewBox="0 0 12 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path
                                        d="M2 2L8.41012 8.41012L2 14.8202"
                                        stroke="#202E3D"
                                        stroke-width="3.84607"
                                ></path>
                            </svg>
                        </button>
                    </div>
                    <div class="collapse" id="collapsible-2">
                        <ul class="result-list d-flex d-md-none">
                            {% for manufacturerType in page.manufacturerTypes %}
                                {% if manufacturerType != "Alle" %}
                                    <li class="filter-deselected {% if loop.index > 100 %} expandable {% endif %}">
                                        <span class="link-deselected" data-manufacturer="true" data-manufacturer-value="{{ manufacturerType }}">{{ manufacturerType }}</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                
                <div class="table">
                    <p class="video-library_table_paragraph d-none d-md-block">
                        <strong>{{ 'video-library.overview.selectVideoType'|trans|sw_sanitize }}</strong>
                    </p>
                    <ul class="result-list d-none d-md-flex">
                        {% for videoType in page.videoTypes %}
                            {% if videoType.name %}
                                <li class="filter-deselected {% if loop.index > 100 %} expandable {% endif %}">
                                    <span class="link-deselected" data-video-type="true" data-video-type-value="{{ videoType.value }}">{{ videoType.name }}</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                    </ul>
                    <div class="d-md-none collapsed mobile-toggle" data-toggle="collapse" data-target="#collapsible-3" role="button">
                        <div>
                            <p class="mb-0">{{ 'video-library.overview.selectVideoType'|trans|sw_sanitize }}</p>
                        </div>
                        <button class="toggle-icon">
                            <svg width="12" height="17" viewBox="0 0 12 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path
                                        d="M2 2L8.41012 8.41012L2 14.8202"
                                        stroke="#202E3D"
                                        stroke-width="3.84607"
                                ></path>
                            </svg>
                        </button>
                    </div>
                    <div class="collapse" id="collapsible-3">
                        <ul class="result-list d-flex d-md-none">
                            {% for videoType in page.videoTypes %}
                                {% if videoType.name %}
                                    <li class="filter-deselected {% if loop.index > 100 %} expandable {% endif %}">
                                        <span class="link-deselected" data-video-type="true" data-video-type-value="{{ videoType.value }}">{{ videoType.name }}</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                
                {% block page_content %}
                    <main class="content-main">
                        
                        {# --- search --- #}
                        <div class="k11-mb-20" data-video-search-container>
                            <div class="related-videos__search">
                                <form class="k11-border-active d-flex align-items-center">
                                    <input type="search"
                                           class="input-group search-input border-0 k11-border-radius-8 k11-pl-15 form-control"
                                           placeholder="Beitrag suchen..."
                                           data-video-search-input>
                                    <div class="svg-container">
                                        <button class="search-input--btn border-0 p-0 outline-none bg-none"
                                                type="button"
                                                data-video-search-btn
                                                disabled>
                                            <svg class="k11-p-2" width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <rect width="34" height="34" rx="5" fill="#202E3D"/>
                                                <path d="M5 27.1333L13.4 18.7333C12.8667 18.0667 12.4444 17.3 12.1333 16.4333C11.8222 15.5667 11.6667 14.6444 11.6667 13.6667C11.6667 11.2444 12.5056 9.19444 14.1833 7.51667C15.8611 5.83889 17.9111 5 20.3333 5C22.7556 5 24.8056 5.83889 26.4833 7.51667C28.1611 9.19445 29 11.2444 29 13.6667C29 16.0889 28.1611 18.1389 26.4833 19.8167C24.8056 21.4944 22.7556 22.3333 20.3333 22.3333C19.3556 22.3333 18.4333 22.1778 17.5667 21.8667C16.7 21.5556 15.9333 21.1333 15.2667 20.6L6.86667 29L5 27.1333ZM14.3333 13.6667C14.3333 15.3333 14.9167 16.75 16.0833 17.9167C17.25 19.0833 18.6667 19.6667 20.3333 19.6667C22 19.6667 23.4167 19.0833 24.5833 17.9167C25.75 16.75 26.3333 15.3333 26.3333 13.6667C26.3333 12 25.75 10.5833 24.5833 9.41667C23.4167 8.25 22 7.66667 20.3333 7.66667C18.6667 7.66667 17.25 8.25 16.0833 9.41667C14.9167 10.5833 14.3333 12 14.3333 13.6667Z" fill="white"/>
                                            </svg>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        {# --- cmsEntities --- #}
                        {% if cmsEntities|length > 0 %}
                            <div data-video-group>
                                <h3 data-video-group-heading>Relevanteste Beiträge</h3>
                                <div class="d-grid k11-gap-20 k11-md-gap-25 k11-grid-template-1 k11-md-grid-template-3 k11-mb-40"
                                     data-video-group-items>
                                    {% set manufacturerList = [] %}
                                    {% for videoId, video in cmsEntities %}
                                        {% set manufacturerList = [] %}
                                        {% for manufacturer in video.translated.manufacturers %}
                                            {% set manufacturerList = manufacturerList|merge([manufacturer]) %}
                                        {% endfor %}
                                        <div class="video-item searchable {% if loop.index > 6 %}d-none{% endif %}"
                                             data-video-item
                                             data-attribute-video="{% if video.translated.appliances is defined and video.translated.appliances %}{{ video.translated.appliances|join(' ') }}{% else %}{{ video.appliance|default('') }}{% endif %}"
                                             data-attribute-type="{{ video.videoType }}"
                                             data-attribute-manufacturer="{{ manufacturerList|join(' ') }}">
                                            <div class="question-input-container">
                                                <a class="k11-black-link k11-font-weight-500"
                                                   href="{{ seoUrl('frontend.video.library.page', {'videoLibraryId': video.id}) }}">
                                                    <u>{{ video.questionInput ?: video.title }}</u>
                                                </a>
                                            </div>
                                        </div>
                                    {% endfor %}
                                    
                                    {% if cmsEntities|length > 6 %}
                                        <div class="related-videos__actions" data-video-load-more>
                                            <button class="load-more-videos" data-load-more>Mehr anzeigen</button>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        {% endif %}
                        
                        {# --- videosWithoutCms --- #}
                        {% if videosWithoutCms|length > 0  %}
                            <div class="k11-my-40" data-video-group>
                                <h3 data-video-group-heading>Unsere Video-Beiträge</h3>
                                <div class="row" data-video-group-items>
                                    {% for video in videosWithoutCms %}
                                        {% set manufacturerList = [] %}
                                        {% for manufacturer in video.translated.manufacturers %}
                                            {% set manufacturerList = manufacturerList|merge([manufacturer]) %}
                                        {% endfor %}
                                        <div class="col-12 col-lg-4 video-item searchable {% if loop.index > 9 %}d-none{% endif %}"
                                             data-video-item
                                             data-attribute-video="{% if video.translated.appliances is defined and video.translated.appliances %}{{ video.translated.appliances|join(' ') }}{% else %}{{ video.translated.appliance|default('') }}{% endif %}"
                                             data-attribute-type="{{ video.translated.videoType }}"
                                             data-attribute-manufacturer="{{ manufacturerList|join(' ') }}">
                                            {% set loading = loop.index <= 9 ? 'load' : 'lazy' %}
                                            {% include '@Ersatzteilshop/storefront/page/video-library/youtube-video.html.twig' with {
                                                width: width,
                                                height: height,
                                                youtubeId: video.youtubeId,
                                                video: video,
                                                overview: true,
                                                loading: loading
                                            } only %}
                                        </div>
                                    {% endfor %}
                                </div>
                                
                                {% if videosWithoutCms|length > 9 %}
                                    <div class="related-videos__actions" data-video-load-more>
                                        <button class="load-more-videos" data-load-more>
                                            {{ 'video-library.overview.loadMore'|trans|sw_sanitize }}
                                        </button>
                                    </div>
                                {% endif %}
                                
                                <div class="no-results" style="display:none">
                                    <div class="d-block d-md-flex no-results-info-container" style="">
                                        <figure class="m-0">
                                            <img
                                                    src="{{ asset('bundles/ersatzteilshop/assets/img/no-video-banner.png') }}"
                                                    height="225px"
                                                    class="d-none d-md-block w-100"
                                                    alt="No Videos Banner Desktop"
                                                    title="No Videos Banner Desktop"
                                            />
                                            <img
                                                    src="{{ asset('bundles/ersatzteilshop/assets/img/no-video-banner_mobile.png') }}"
                                                    height="225px"
                                                    class="d-block d-md-none w-100"
                                                    alt="No Videos Banner Mobile"
                                                    title="No Videos Banner Mobile"
                                            />
                                        </figure>
                                        <div class="no-results-info-container_information">
                                            <p>
                                                {{ 'video-library.overview.noVideosParagraph'|trans|sw_sanitize }}
                                            </p>
                                            <p class="m-0">
                                                {{ 'video-library.overview.noVideosSubParagraph'|trans|sw_sanitize }}
                                            </p>
                                        </div>
                                    </div>
                                    <div class="no-results-video-container">
                                        {% for video in videosWithYoutube %}
                                            {% if video.id in page.noResultVideoIds %}
                                                <div class="no-result-item" style="display: block">
                                                    {% include
                                                        '@Ersatzteilshop/storefront/page/video-library/youtube-video.html.twig'
                                                        with {
                                                        width: width,
                                                        height: height,
                                                        youtubeId: video.youtubeId,
                                                        video: video,
                                                        overview: true,
                                                        loading: 'lazy'
                                                    } only
                                                    %}
                                                </div>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                        
                        {# --- questionsNoCmsNoUrl --- #}
                        {% if questionsNoCmsNoUrl|length > 0 %}
                            <div class="k11-my-40" data-video-group>
                                <h3 data-video-group-heading>Weitere Beiträge</h3>
                                <div class="d-grid k11-gap-20 k11-md-gap-25 k11-grid-template-1 k11-md-grid-template-3"
                                     data-video-group-items>
                                    {% set manufacturerList = [] %}
                                    {% for videoId, video in questionsNoCmsNoUrl %}
                                        {% set manufacturerList = [] %}
                                        {% for manufacturer in video.translated.manufacturers %}
                                            {% set manufacturerList = manufacturerList|merge([manufacturer]) %}
                                        {% endfor %}
                                        <div class="video-item searchable {% if loop.index > 6 %}d-none{% endif %}"
                                             data-video-item
                                             data-attribute-video="{% if video.translated.appliances is defined and video.translated.appliances %}{{ video.translated.appliances|join(' ') }}{% else %}{{ video.appliance|default('') }}{% endif %}"
                                             data-attribute-type="{{ video.videoType }}"
                                             data-attribute-manufacturer="{{ manufacturerList|join(' ') }}">
                                            <div class="question-input-container">
                                                <a class="k11-black-link k11-font-weight-500"
                                                   href="{{ seoUrl('frontend.video.library.page', {'videoLibraryId': video.id}) }}">
                                                    <u>{{ video.questionInput ?: video.title }}</u>
                                                </a>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                                
                                {% if questionsNoCmsNoUrl|length > 6 %}
                                    <div class="related-videos__actions k11-mt-20" data-video-load-more>
                                        <button class="load-more-videos" data-load-more>Mehr anzeigen</button>
                                    </div>
                                {% endif %}
                            </div>
                        {% endif %}
                    </main>
                {% endblock %}
            
            
            </div>
        {% endblock %}
    </div>
    
    {% block bottom_container %}
    {% endblock %}
{% endblock %}
{% block base_script_hmr_mode %}
    <script defer type="text/javascript" src="{{ asset('bundles/ersatzteilshop/assets/js/vendor-node.js') }}"></script>
    <script defer type="text/javascript" src="{{ asset('bundles/ersatzteilshop/assets/js/vendor-shared.js') }}"></script>
    <script defer type="text/javascript" src="{{ asset('bundles/ersatzteilshop/assets/js/runtime.js') }}"></script>
    <script defer type="text/javascript" src="{{ asset('bundles/ersatzteilshop/assets/js/video-library.js') }}"></script>
{% endblock %}
