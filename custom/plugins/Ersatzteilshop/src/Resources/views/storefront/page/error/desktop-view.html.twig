<div class="row error-page-content-desktop">
    {% block error_content_col_1 %}
        <div class="col-4">
            <h2>
                <img style="width: 40px; margin-right: 10px" src="{{ asset('bundles/ersatzteilshop/assets/icons/icon-nbr-1.svg') }}" alt="Schritt Eins">
                {{ "error.subheadingOne"|trans|sw_sanitize }}
            </h2>
            <p class="error-page-description">
                <b>{{ "error.errorDiagnosis"|trans|sw_sanitize }}</b>
                {{ "error.diagnosisParagraphOne"|trans|raw }}
            </p>
            <div class="button-wrapper">
                <a href="{{ 'ersatzteilshop.repairGuide.linkRepairGuide'|trans }}"
                   class="btn btn-primary btn-repair-guide">
                    {{ 'ersatzteilshop.repairGuide.btnRepairGuide'|trans }}
                </a>
            </div>
        </div>
    {% endblock %}

    {% block error_content_col_2 %}
        <div class="col-4">
            <h2>
                <img style="width: 40px; margin-right: 10px" src="{{ asset('bundles/ersatzteilshop/assets/icons/icon-nbr-2.svg') }}" alt="Schritt Zwei">
                {{ "error.subheadingTwo"|trans|sw_sanitize }}
            </h2>
            <p class="error-page-description">
                {{ "error.diagnosisParagraphTwo"|trans|raw }}
            </p>
            <p class="error-page-description">
                <strong>{{ "error.diagnosisColumnTwo"|trans|raw }}</strong>
            </p>
            {% block error_content_col_2_step_1 %}
                <p class="error-page-description">
                    <strong>{{ "error.diagnosisColumnTwoStep1"|trans|raw }}</strong>
                </p>
                <p class="error-page-description">
                    {{ "error.diagnosisColumnTwoStep1Description"|trans|raw }}
                </p>

                <a class="cms-element-search-widget-link"
                   href="{{ path('frontend.type-number.widget') }}"
                   data-toggle="modal"
                   title="{{ "search.searchWidgetLink"|trans|striptags }}"
                   data-url="{{ path('frontend.type-number.widget') }}"
                   data-modal-class="search-widget-modal">
                    &#x25B7 {{ "search.searchWidgetLink"|trans }}
                </a>
            {% endblock %}

            {% block error_content_col_2_step_2 %}
                <p class="error-page-description" style="margin-top: 10px">
                    <strong>{{ "error.diagnosisColumnTwoStep2"|trans|raw }}</strong>
                </p>
                <p class="error-page-description">
                    {{ "error.diagnosisColumnTwoStep2Description"|trans|raw }}
                </p>
            {% endblock %}

            {% block error_content_col_2_step_3 %}
                <p class="error-page-description">
                    <strong>{{ "error.diagnosisColumnTwoStep3"|trans|raw }}</strong>
                </p>
                <p class="error-page-description">
                    {{ "error.diagnosisColumnTwoStep3Description"|trans|raw }}
                </p>
            {% endblock %}
        </div>
    {% endblock %}

    {% block error_content_col_3 %}
        <div class="col-4">
            <h2>
                <img style="width: 40px; margin-right: 10px" src="{{ asset('bundles/ersatzteilshop/assets/icons/icon-nbr-3.svg') }}" alt="Schritt Drei">
                {{ "error.subheadingThree"|trans|sw_sanitize }}
            </h2>
            <p class="error-page-description">
                {{ "error.diagnosisColumnThree"|trans|raw }}
            </p>
            <div class="button-wrapper">
                <a href="{{ 'ersatzteilshop.repairGuide.linkRepairGuide'|trans }}"
                   class="btn btn-primary btn-repair-guide">
                    {{ 'ersatzteilshop.repairGuide.btnRepairGuide'|trans }}
                </a>
                <a href="{{ 'ersatzteilshop.repairInstruction.repairInstructionLink'|trans }}"
                   class="btn btn-primary btn-repair-guide">
                    {{ 'ersatzteilshop.repairInstruction.repairInstructionsLinkHeading'|trans }}
                </a>
            </div>
        </div>
    {% endblock %}
</div>

{% block search_widget_top_content %}
    <div class="col-md-12">
        {% sw_include '@Ersatzteilshop/storefront/page/search/search-widget-top.html.twig' %}
    </div>
{% endblock %}
