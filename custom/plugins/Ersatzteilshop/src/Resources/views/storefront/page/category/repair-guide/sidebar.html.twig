{#children categories#}
<div class="page-sidebar-section repair-guide-sidebar">
    {% if page.category.level == 1 %}
    <div class="page-sidebar-section-title">
        {{ 'ersatzteilshop.repairGuide.sidebarTitle'|trans|sw_sanitize }}
    </div>
    {% endif %}

    {% if page.category.breadcrumb|length > 1 %}
        {% set parentCategories = page.category.breadcrumb|reverse %}
        <p class="page-sidebar-category-parent">
            <a href="{{ seoUrl('frontend.navigation.page', { navigationId: page.category.parentId }) }}">
                 <span class="parent-category-name">
                    {{ 'ersatzteilshop.repairGuide.sidebarHeading'|trans({'%category%': parentCategories[1]}) }}
                 </span>
            </a>
        </p>
        <div class="page-sidebar-category-active">
            {% if page.category.type == 'repairGuide' %}
                {{ 'ersatzteilshop.repairGuide.categoryTitle'|trans({'%category%': page.category.translated.name}) }}
            {% else %}
                {{ page.category.translated.name }}
            {% endif %}
        </div>
    {% endif %}

    <ul class="page-sidebar-category {% if page.category.type == 'repairGuide' and page.category.level == 3 %}hide{% endif %}">
        {% if page.category.level < 3 %}
            {% for category in page.extensions.childrenCategories %}
                {% set categoryName =  category.translated.name %}
                {% if category.type == 'repairGuide' %}
                    {% set categoryName =  'ersatzteilshop.repairGuide.categoryTitle'|trans({'%category%': category.translated.name}) %}
                {% endif %}
                <li>
                    <a href="{{ seoUrl('frontend.navigation.page', { navigationId: category.id }) }}"
                       title="{{ categoryName }}">
                        {% if category.media %}
                            {% sw_include '@Ersatzteilshop/storefront/utilities/category-icon.html.twig' with {
                                'category': category
                            } %}
                        {% else %}
                            <img src="{{ asset('bundles/ersatzteilshop/assets/img/no_icon_cat.svg') }}" alt="{{ category.translated.name }}">
                        {% endif %}
                        <span>{{ categoryName }}</span>

                    </a>
                </li>
            {% endfor %}
        {% else %}
            {% for repairGuide in page.extensions.repairGuides %}
                <li class="repair-guide-sidebar">
                    <a href="{{ seoUrl('frontend.repair.guide.page', { repairGuideId: repairGuide.id }) }}"
                        title="{{ repairGuide.translated.title }}">
                        {{ repairGuide.translated.title }}
                    </a>
                </li>
            {% endfor %}
        {% endif %}
    </ul>
</div>

{% sw_include '@Storefront/storefront/page/category/allego.html.twig' %}
