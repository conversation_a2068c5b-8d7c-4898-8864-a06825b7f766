{% if appliances.elements is defined and appliances.elements is not empty %}
    {% for applianceId, appliance in appliances.elements %}
        {% set modelCode = appliance.modelCode %}
        {% set modelName = appliance.modelName %}
        {% set manufacturer = appliance.manufacturer.name %}
        {% set seoUrl = '' %}

        {% if appliance.seoUrls is defined and appliance.seoUrls.elements is not empty %}
            {% set seoUrl = appliance.seoUrls.elements|first.seoUrl %}
            {% if seoUrl is empty %}
                {% set seoUrl = '/appliance/' ~ applianceId %}
            {% endif %}
        {% else %}
            {% set seoUrl = '/appliance/' ~ applianceId %}
        {% endif %}

        {% if searchTerm is not empty %}
            {% set seoUrl = seoUrl ~ '?category=' ~ searchTerm %}
        {% endif %}

        {% set applianceInfo = modelCode ~ ' ' ~ modelName ~ ' ' ~ manufacturer %}

        {% set mobileMaxLength = 32 %}
        {% set desktopMaxLength = 46 %}

        {% set mobileInfo = applianceInfo|length > mobileMaxLength ? applianceInfo|slice(0, mobileMaxLength) ~ ' ...' : applianceInfo %}
        {% set desktopInfo = applianceInfo|length > desktopMaxLength ? applianceInfo|slice(0, desktopMaxLength) ~ ' ...' : applianceInfo %}

        <li style="border-bottom: 1px solid #dadbd2;">
            <a style="color: #000;" href="/{{ seoUrl }}" class="appliance-item" data-value="{{ modelCode }}">
                <p class="k11-my-10 d-block d-md-none">{{ mobileInfo }}</p>
                <p class="k11-my-10 d-none d-md-block">{{ desktopInfo }}</p>
            </a>
        </li>
    {% endfor %}
    <li>
        <a href="{{ path('frontend.search.page') }}?search={{ searchQuery }}"
           title="{{ 'video-library.overview.loadAll'|trans|sw_sanitize }}"
           class="k11-black-link">
            <p class="k11-mt-10"><u><strong>{{ 'video-library.overview.loadAll'|trans|sw_sanitize }}</strong></u></p>
        </a>
    </li>
{% endif %}