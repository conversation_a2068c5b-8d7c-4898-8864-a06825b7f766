{% set countContent = 1 %}
{% if page.category.extensions.mapToCategoryPages.repairInstruction|length > 0 %}
    {% set countContent = countContent + 1 %}
{% endif %}
{% if page.category.extensions.mapToCategoryPages.errorGroup|length > 0 %}
    {% set countContent = countContent + 1 %}
{% endif %}
{% if page.category.level == 3 and countContent > 1 %}
    <div class="header-title">
        <p>{{ 'ersatzteilshop.repairGuide.topHeading'|trans({'%category%': page.category.translated.name}) }}</p>
        <div class="anchor-link">
            <a href="javascript:void(0)" data-follow-anchor="true" data-follow-anchor-options="{{ {
                trigger: {
                    event: 'click',
                },
                scroll: true,
                toElementSelector: '#repairGuide'
            }|json_encode }}">
                ➝ <span>{{ 'ersatzteilshop.repairGuide.linkTitle'|trans }}
            </a><br>
            {% if (page.category.extensions.mapToCategoryPages.repairInstruction) %}
                <a href="javascript:void(0)" data-follow-anchor="true" data-follow-anchor-options="{{ {
                    trigger: {
                        event: 'click',
                    },
                    scroll: true,
                    toElementSelector: '#repairInstruction'
                }|json_encode }}">
                    ➝ <span>{{ 'ersatzteilshop.repairGuide.repairInstructionLinkTitle'|trans }}
                </a><br>
            {% endif %}
            {% if (page.category.extensions.mapToCategoryPages.errorGroup) %}
                <a href="javascript:void(0)" data-follow-anchor="true" data-follow-anchor-options="{{ {
                    trigger: {
                        event: 'click',
                    },
                    scroll: true,
                    toElementSelector: '#errorGroup'
                }|json_encode }}">
                    ➝ <span>{{ 'ersatzteilshop.repairGuide.errorGroupLinkTitle'|trans }}
                </a><br>
            {% endif %}
        </div>
    </div>
{% endif %}

{# Start Repair Guide Section #}
<div id="repairGuide">
    <h3 class="category-sub-title">
        {{ 'ersatzteilshop.repairGuide.subTitle'|trans({'%category%': page.category.translated.name}) }}
    </h3>
    {% if page.category.level == 1 %}
        {% if page.extensions.cmsPage %}
            <div class="repair-guide-cms-block">
                {% sw_include "@Storefront/storefront/page/content/detail.html.twig" with {'cmsPage': page.extensions.cmsPage} %}
                <br>
            </div>
        {% endif %}

        {% if page.categoryShortDesc %}
            <div class="category-seo">
                {{ page.categoryShortDesc|raw }}
            </div>
        {% endif %}

        {# repair-product-banner #}
        {% if config('Ersatzteilshop.config.repairItemId')  %}
            <a href="{{ seoUrl('frontend.detail.page', {'productId': config('Ersatzteilshop.config.repairItemId')}) }}">
                <div class="banner-repairProduct mt-5">
                </div>
            </a>
        {% endif %}
    {% endif %}

    {# level 1 #}
    {% if page.category.level == 2 %}
        <div class="repair-guide-sub-category-block">
            {% for category in page.extensions.childrenCategories %}
                <div class="repair-guide-sub-category">
                    <a href="{{ seoUrl('frontend.navigation.page', { navigationId: category.id }) }}" target="_self">
                        {% if category.media %}
                            <img src="{{ category.media.url }}" alt="{{ category.translated.name }}"
                                 loading="lazy">
                        {% else %}
                            <img src="{{ asset('bundles/ersatzteilshop/assets/img/universelle-ersatzteile.jpg') }}"
                                 alt="{{ category.translated.name }}">
                        {% endif %}

                        <p>{{ category.translated.name }}</p>
                    </a>
                </div>
            {% endfor %}
        </div>

        {# repair-product-banner #}
        {% if config('Ersatzteilshop.config.repairItemId')  %}
            <a href="{{ seoUrl('frontend.detail.page', {'productId': config('Ersatzteilshop.config.repairItemId')}) }}">
                <div class="banner-repairProduct mt-5">
                </div>
            </a>
        {% endif %}

        {# banner #}
        {% for tag in page.category.tags %}
            {% if tag.name is same as('banner') %}
                <a href="#">
                    <div class="banner-thermomix">
                    </div>
                </a>
            {% endif %}
        {% endfor %}


    {% endif %}

    {# level 2 #}
    {% if page.category.level == 3 %}
        {% set countItem = 0 %}
        {% for repairGuide in page.extensions.repairGuides %}
            {% set countItem = countItem + 1 %}
            <div class="repair-guide-item {% if countItem >= 5 %}hide{% endif %}">
                <a href="{{ seoUrl('frontend.repair.guide.page', { repairGuideId: repairGuide.id }) }}">{{ repairGuide.translated.title }}</a>
            </div>
        {% endfor %}
        {% if countItem >= 3 %}
            <div class="btn-bottom">
                <div class="btn btn-detail load-all" data-item-class="repair-guide-item" title="{{ 'ersatzteilshop.repairGuide.loadAll'|trans|sw_sanitize }}">
                    {{ 'ersatzteilshop.repairGuide.loadAll'|trans|sw_sanitize }}
                </div>
            </div>
        {% endif %}

        {# thermomix-banner #}
        {% for tag in page.category.tags %}
            {% if tag.name is same as('banner') %}
                <a href="https://ersatzteilshopde.perspectivefunnel.com/61333546b046d30020786450/628b8b3921fd6702033651da/page_1edsloh/"
                   target="_blank">
                    <div class="banner-thermomix mt-5">
                    </div>
                </a>
            {% endif %}
        {% endfor %}
    {% endif %}
</div>
{# End Repair Guide Section #}

{# Start Repair Instruction Section #}
{% if page.category.level == 3 and page.category.extensions.mapToCategoryPages.repairInstruction %}
    <div id="repairInstruction">
        {% set repairPages = page.category.extensions.mapToCategoryPages.repairInstruction %}
        {% for repairPage in repairPages %}
            <h2 class="category-sub-title">
                {{ 'ersatzteilshop.repairGuide.repairInstructionSubTitle'|trans({'%category%': repairPage.category.translated.name}) }}
            </h2>
            {% if repairPage.cmsPage %}
                <div class="repair-guide-cms-block">
                    {% sw_include "@Storefront/storefront/page/content/detail.html.twig" with {'cmsPage': page.extensions.cmsPage} %}
                    <br>
                </div>
            {% endif %}

            {% if repairPage.category.level == 1 %}

                <div class="container repair-instruction-banner">
                    <a class="repair-instruction-banner-link" href="{{ path('frontend.customer.repair.instruction.new') }}">
                        {{ 'ersatzteilshop.repairInstruction.bannerText'|trans }}
                    </a>
                </div>
            {% endif %}

            {% if repairPage.category.level == 2 %}
                <div class="repair-instruction-sub-categories-section">
                    {% for category in repairPage.extensions.childrenCategories %}
                        {% if category.media %}
                            {% set categoryImg = category.media.url %}
                        {% else %}
                            {% set categoryImg = asset('bundles/ersatzteilshop/assets/img/universelle-ersatzteile.jpg') %}
                        {% endif %}
                        <div class="repair-instruction-sub-category">
                            <a class="page-sidebar-category--item"
                               href="{{ seoUrl('frontend.navigation.page', { navigationId: category.id }) }}"
                               title="{{ category.translated.name }}">
                                <img src="{{ categoryImg }}" alt="{{ category.translated.name }}" loading="lazy"/>
                                <p>{{ category.translated.name }}</p>
                            </a>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
            {% if repairPage.extensions.repairInstructions and repairPage.extensions.repairInstructions.count %}
                {% sw_include "@Storefront/storefront/page/category/repair-instruction/list-repair-instruction.html.twig" with {
                    repairInstructions: repairPage.extensions.repairInstructions,
                } only %}
            {% endif %}

        {% endfor %}
    </div>
{% endif %}
{# End Repair Instruction #}

{# Start Error Group Section #}
{% if page.category.level == 3 and page.category.extensions.mapToCategoryPages.errorGroup %}
    <div id="errorGroup">
        {% set errorGroupPages = page.category.extensions.mapToCategoryPages.errorGroup %}
        {% for errorGroupPage in errorGroupPages %}
            <h2 class="category-sub-title">
                {{ 'ersatzteilshop.repairGuide.errorGroupSubTitle'|trans({'%category%': errorGroupPage.category.translated.name}) }}
            </h2>
            {% if errorGroupPage.category.level == 2 %}
                {# search widget block #}
                {% sw_include '@Ersatzteilshop/storefront/page/category/error-group/search_widget/search_widget.html.twig'
                    with {"categoryId": errorGroupPage.category.id} %}

                {# content seo block #}
                {% if errorGroupPage.cmsPage %}
                    <div class="error-group-cms-block">
                        {% sw_include "@Storefront/storefront/page/content/detail.html.twig" with {'cmsPage': errorGroupPage.cmsPage} %}
                    </div>
                {% endif %}
                <div>
                    {{ errorGroupPage.categoryShortDesc|raw }}
                </div>

                {# error group bottom block #}
                {% if (errorGroupPage.extensions.childrenCategories)|length %}
                    <div class="d-flex flex-row flex-wrap w-100 subcategories-promo-block">
                        {% for category in errorGroupPage.extensions.childrenCategories %}
                            <a class="text-center col-5 col-md-3 col-xl-3 subcategories-promo-block--item"
                               style="  margin: 1rem !important;"
                               href="{{ seoUrl('frontend.navigation.page', { navigationId: category.id }) }}" target="_self">
                                {% if category.media %}
                                    {% sw_thumbnails 'category-image-thumbnails' with {
                                        load: false,
                                        media:  category.media,
                                        attributes: {
                                            'alt': category.translated.name,
                                            'title': category.translated.name,
                                            'height': '127px',
                                            'class': 'p-2'
                                        },
                                        sizes: {
                                            'default': '150px'
                                        }
                                    } %}
                                {% else %}
                                    <img height="127px"
                                         src="{{ asset('bundles/ersatzteilshop/assets/img/universelle-ersatzteile.jpg') }}"
                                         alt="{{ category.translated.name }}">
                                {% endif %}

                                <p class="text-center">{{ category.translated.name }}</p>
                            </a>
                        {% endfor %}
                    </div>
                {% endif %}

                {% if errorGroupPage.extensions.popularManufacturers|length %}
                    <h3 class="pt-5">{{ 'ersatzteilshop.errorGroup.manufactureHeading'|trans }}</h3>
                    <div class="d-flex flex-row flex-wrap w-100 manufactures-promo-block">
                        {% for manufacturer in errorGroupPage.extensions.popularManufacturers %}
                            <a class="text-center m-3 col-5 col-md-3 col-xl-3 manufactures-promo-block--item"
                               href="{{ seoUrl('frontend.category.manufacturer.page', { navigationId: errorGroupPage.category.id, manufacturerId: manufacturer.id }) }}"
                               target="_self">
                                {% if manufacturer.media %}
                                    {% sw_thumbnails 'category-image-thumbnails' with {
                                        load: false,
                                        media:  manufacturer.media,
                                        attributes: {
                                            'alt': manufacturer.translated.name,
                                            'title': manufacturer.translated.name,
                                            'height': '70px',
                                            'class': 'p-2'
                                        },
                                        sizes: {
                                            'default': '100px'
                                        }
                                    } %}
                                {% else %}
                                    <img height="70px"
                                         src="{{ asset('bundles/ersatzteilshop/assets/img/universelle-ersatzteile.jpg') }}"
                                         alt="{{ manufacturer.translated.name }}">
                                {% endif %}

                                <p class="text-center">{{ manufacturer.translated.name }}</p>
                            </a>
                        {% endfor %}
                    </div>
                {% endif %}
            {% endif %}

            {% if errorGroupPage.category.level > 2 %}
                {# search widget block #}
                {% sw_include '@Ersatzteilshop/storefront/page/category/error-group/search_widget/search_widget.html.twig'
                    with {"categoryId": errorGroupPage.category.id} %}

                {# content seo block #}
                {% if errorGroupPage.cmsPage %}
                    <div class="error-group-cms-block">
                        {% sw_include "@Storefront/storefront/page/content/detail.html.twig" with {'cmsPage': errorGroupPage.cmsPage} %}
                    </div>
                {% endif %}
                <div class="category-seo">
                    {{ errorGroupPage.categoryShortDesc|raw }}
                </div>

                {# error group bottom block #}
                {% if errorGroupPage.extensions.errorGroups|length %}

                    {% set sliderOptions = {
                        slider: {
                            mouseDrag: true,
                            navPosition: 'bottom',
                            speed: 500,
                            nav: false,
                            controls: true,
                            items: 5,
                            loop:  true,
                            slideBy: 1,
                            responsive : {
                                xs: {
                                    items: 1
                                }
                            }
                        }
                    } %}

                    <h2 class="pt-5">{{ 'ersatzteilshop.errorGroup.manufactureHeading'|trans }}</h2>

                    <div class="base-slider has-nav"
                         data-base-slider="true"
                         data-base-slider-options="{{ sliderOptions|json_encode }}">
                        <div class="loader"></div>
                        <div class="image-slider-container d-flex align-items-stretch"
                             data-base-slider-container="true">
                            {% for errorGroup in errorGroupPage.extensions.errorGroups %}
                                <a class="text-center m-2 manufactures-promo-block--item"
                                   style="height: 150px; line-height: 50px; padding-top: 50px"
                                   href="{{ seoUrl('frontend.error.group.page', { id: errorGroup.id }) }}"
                                   target="_self">
                                    {% if errorGroup.manufacturer.media %}
                                        {% set thumbnails = errorGroup.manufacturer.media.thumbnails %}
                                        {% set thumbnailUrl = null %}

                                        {% if thumbnails|length > 0 %}
                                            {% for thumbnail in thumbnails %}
                                                {% if thumbnail.width is same as(150) %}
                                                    {% set thumbnailUrl = thumbnail.url %}
                                                {% endif %}
                                            {% endfor %}
                                        {% endif %}

                                        <img width="100px" src="{% if thumbnailUrl %}{{ thumbnailUrl }}{% else %}{{ errorGroup.manufacturer.media.url }}{% endif %}"
                                             alt="{{ errorGroup.manufacturer.translated.name }}" loading="lazy">
                                    {% else %}

                                        <img height="100px"
                                             src="{{ asset('bundles/ersatzteilshop/assets/img/universelle-ersatzteile.jpg') }}"
                                             alt="{{ errorGroup.manufacturer.translated.name }}">
                                    {% endif %}

                                    <!-- <p class="text-center">{{ errorGroup.manufacturer.translated.name }}</p> -->
                                </a>
                            {% endfor %}
                        </div>

                        <div class="image-slider-controls-container">
                            <div class="base-slider-controls"
                                 data-base-slider-controls="true">
                                <button class="base-slider-controls-prev image-slider-controls-prev is-nav-prev-outside">
                                    <span class="fa fa-chevron-left"></span>
                                </button>
                                <button class="base-slider-controls-next image-slider-controls-next is-nav-next-outside">
                                    <span class="fa fa-chevron-right"></span>
                                </button>
                            </div>
                        </div>
                    </div>
                {% endif %}
            {% endif %}

        {% endfor %}
    </div>
{% endif %}
{# End Error Group #}

{# Repair Service Banner For Level 2 #}
{% if page.category.level == 3 %}
    {% if config('Ersatzteilshop.config.repairItemId')  %}
        <a href="{{ seoUrl('frontend.detail.page', {'productId': config('Ersatzteilshop.config.repairItemId')}) }}">
            <div class="banner-repairProduct mt-5">
            </div>
        </a>
    {% endif %}
{% endif %}
