{#children categories#}
<div class="page-sidebar-section product-sidebar">
    <div class="page-sidebar-section-title">
        {{ 'ersatzteilshop.page.categorySection'|trans|sw_sanitize }}
    </div>
    {% if page.category.breadcrumb|length > 2 %}
        {% if page.manufacturer %}
            {% set seoUrl = seoUrl('frontend.category.manufacturer.page', { navigationId: page.category.parentId, manufacturerId: page.manufacturer.id }) %}
        {% else %}
            {% set seoUrl = seoUrl('frontend.navigation.page', { navigationId: page.category.parentId }) %}
        {% endif %}

        <p class="page-sidebar-category-parent">
            <a href="{{ seoUrl }}">
                <span class="parent-category-name">
                    {{ page.extensions.parentCategory.translated.name }}
                </span>
            </a>
        </p>
    {% else %}
        <p class="page-sidebar-category-parent">
            <a href="{{ seoUrl('frontend.home.page') }}">
                 <span class="parent-category-name">
                    {{ 'ersatzteilshop.breadcrumb.home'|trans|sw_sanitize }}
                </span>
            </a>
        </p>
    {% endif %}
    <div class="page-sidebar-category-active">{{ page.category.translated.name }}</div>

    <ul class="page-sidebar-category">
        {% for category in page.extensions.childrenCategories.sortByName() %}
            {% if page.manufacturer %}
                {% set seoUrl = seoUrl('frontend.category.manufacturer.page', { navigationId: category.id, manufacturerId: page.manufacturer.id }) %}
            {% else %}
                {% set seoUrl =  seoUrl('frontend.navigation.page', { navigationId: category.id }) %}
            {% endif %}
            <li>
                <a href="{{ seoUrl }}"
                   title="{{ category.translated.name }}">
                    {% if category.media %}
                        {% sw_include '@Ersatzteilshop/storefront/utilities/category-icon.html.twig' with {
                            'category': category
                        } %}
                    {% else %}
                        <img src="{{ asset('bundles/ersatzteilshop/assets/img/no_icon_cat.svg') }}" alt="{{ category.translated.name }}">
                    {% endif %}
                    <span>{{ category.translated.name }}</span>
                </a>
            </li>
        {% endfor %}
    </ul>
</div>

{% sw_include '@Storefront/storefront/page/category/allego.html.twig' %}
