{% block product_search_result %}
    {% set categoryFacets = page.listing.extensions.facets.categories %}
    {% set manufacturerFacets = page.listing.extensions.facets.manufacturers %}
    {% set filteredCategories = page.listing.currentFilter('category')|join('|') %}
    {% set filteredManufacturers = page.listing.currentFilter('manufacturer')|join('|') %}

    <div class="row">
        {# sidebar #}
        <div class="col-md-3">
            <div class="page-sidebar">
                {% if filteredCategories|length or filteredManufacturers|length %}
                    <div class="page-sidebar-section page-sidebar-selected">
                        <div
                            class="page-sidebar-section-title">{{ 'ersatzteilshop.page.yourSelection'|trans|sw_sanitize }}</div>

                        <ul class="page-sidebar-category">
                            {% for categoryFacet in categoryFacets %}
                                {% if categoryFacet.filtered %}
                                    <li>
                                        <a href="{{ url('frontend.search.page', {search: page.searchTerm , manufacturer: filteredManufacturers }) }}"
                                           title="{{ categoryFacet.entity.translated.name }}">
                                            {% if categoryFacet.entity.media %}
                                                {% sw_thumbnails 'category-image-thumbnails' with {
                                                    load: false,
                                                    media:  categoryFacet.entity.media,
                                                    attributes: {
                                                        'alt': categoryFacet.entity.translated.name,
                                                        'title': categoryFacet.entity.translated.name,
                                                        'class': 'mx-1 category-icon',
                                                        'width': '20px',
                                                        'height': '20px',
                                                    },
                                                    sizes: {
                                                        'default': '20px',
                                                    }
                                                } %}
                                            {% else %}
                                                <img src="{{ asset('bundles/ersatzteilshop/assets/img/no_icon_cat.svg') }}"
                                                     alt="{{ categoryFacet.translated.name }}">
                                            {% endif %}
                                            <span>{{ categoryFacet.entity.translated.name }}</span>
                                        </a>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% for manufacturerFacet in manufacturerFacets %}
                                {% if manufacturerFacet.filtered %}
                                    <li>
                                        <a href="{{ url('frontend.search.page', {search: page.searchTerm , category: filteredCategories }) }}">
                                            {% if manufacturerFacet.entity.media %}
                                                {% sw_thumbnails 'category-image-thumbnails' with {
                                                    load: false,
                                                    media:  manufacturerFacet.entity.media,
                                                    attributes: {
                                                        'alt': manufacturerFacet.entity.translated.name,
                                                        'title': manufacturerFacet.entity.translated.name,
                                                        'class': 'mx-1 category-icon',
                                                        'width': '20px',
                                                        'height': '20px',
                                                    },
                                                    sizes: {
                                                        'default': '20px',
                                                    }
                                                } %}
                                            {% else %}
                                                <img src="{{ asset('bundles/ersatzteilshop/assets/img/no_icon_cat.svg') }}"
                                                     alt="{{ manufacturerFacet.translated.name }}">
                                            {% endif %}
                                            <span>{{ manufacturerFacet.entity.translated.name }}</span>
                                        </a>
                                    </li>
                                {% endif %}
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}

                <div class="page-sidebar-section">
                    <div class="page-sidebar-section-title">
                        {{ 'ersatzteilshop.page.categorySection'|trans|sw_sanitize }}
                    </div>

                    <ul class="page-sidebar-category">
                        {% for categoryFacet in categoryFacets %}
                            <li>
                                <a href="{{ url('frontend.search.page', {search: page.searchTerm, category: categoryFacet.entity.id , manufacturer: filteredManufacturers }) }}"
                                   title="{{ categoryFacet.entity.translated.name }}">
                                    {% if categoryFacet.entity.media %}
                                        {% sw_thumbnails 'category-image-thumbnails' with {
                                            load: false,
                                            media:  categoryFacet.entity.media,
                                            attributes: {
                                                'alt': categoryFacet.entity.translated.name,
                                                'title': categoryFacet.entity.translated.name,
                                                'class': 'mx-1 category-icon',
                                                'width': '20px',
                                                'height': '20px'
                                            },
                                            sizes: {
                                                'default': '20px',
                                            }
                                        } %}
                                    {% else %}
                                        <img src="{{ asset('bundles/ersatzteilshop/assets/img/no_icon_cat.svg') }}"
                                             alt="{{ categoryFacet.translated.name }}">
                                    {% endif %}
                                    <span>{{ categoryFacet.entity.translated.name }} ({{ categoryFacet.count }})</span>
                                </a>
                            </li>
                        {% endfor %}

                    </ul>
                </div>

                <div class="page-sidebar-section">
                    <div class="page-sidebar-section-title">
                        {{ 'ersatzteilshop.page.brandSection'|trans|sw_sanitize }}
                    </div>

                    <ul class="page-sidebar-category">
                        {% for manufacturerFacet in manufacturerFacets %}
                            <li>
                                <a href="{{ url('frontend.search.page', {search: page.searchTerm, category: filteredCategories, manufacturer: manufacturerFacet.entity.id }) }}"
                                   title="{{ manufacturerFacet.translated.name }}">
                                    {% if manufacturerFacet.media %}
                                        {% sw_thumbnails 'category-image-thumbnails' with {
                                            load: false,
                                            media:  manufacturerFacet.entity.media,
                                            attributes: {
                                                'alt': manufacturerFacet.entity.translated.name,
                                                'title': manufacturerFacet.entity.translated.name,
                                                'class': 'mx-1 category-icon',
                                                'width': '20px',
                                                'height': '20px',
                                            },
                                            sizes: {
                                                'default': '20px',
                                            }
                                        } %}
                                    {% else %}
                                        <img src="{{ asset('bundles/ersatzteilshop/assets/img/no_icon_cat.svg') }}"
                                             alt="{{ manufacturerFacet.translated.name }}">
                                    {% endif %}
                                    <span>{{ manufacturerFacet.entity.translated.name }} ({{ manufacturerFacet.count }})</span>
                                </a>
                            </li>
                        {% endfor %}

                    </ul>
                </div>
            </div>
        </div>

        {# main content #}
        <div class="col-md-9">
            <main class="page-main-content">
                {% sw_include '@Storefront/storefront/page/search/search-pagelet.html.twig' %}
            </main>
        </div>
    </div>
{% endblock %}
