{% sw_extends '@Storefront/storefront/page/product-detail/buy-widget-form.html.twig' %}

{% block page_product_detail_buy_quantity_container %}
    {% if page.product.active %}

        {% if product.maxPurchase %}
            {% set maxQuantity = product.maxPurchase * 1 %}
        {% else %}
            {% set maxQuantity = config('core.cart.maxQuantity') * 1 %}
        {% endif %}

        {% set productQuantityOptions = {
            quantityGroupSelector: '.quantity-input-group',
            quantitySelector: '.product-detail-quantity-select',
            minusSelector: '.minus',
            plusSelector: '.plus',
            quantityConfig: {
                min: product.minPurchase,
                max: maxQuantity,
                step: product.purchaseSteps,
            }
        } %}
    {% endif %}
{% endblock %}

{% block page_product_detail_buy_button_container %}
    {% if page.product.active %}
        <div class="col-sm-12 col-md-12 buttons k11-px-0 k11-py-0">
            {% block page_product_detail_buy_button %}
                <button class="add-to-cart__button btn-buy"
                        style="width: 100%"
                        title="{{ "detail.addProduct"|trans|striptags }}"
                        aria-label="{{ "detail.addProduct"|trans|striptags }}">
                    <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <mask id="mask0_1651_11325" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="24">
                            <rect x="0.947266" width="24" height="24" fill="#D9D9D9"/>
                        </mask>
                        <g mask="url(#mask0_1651_11325)">
                            <path d="M7.94727 22C7.39727 22 6.92643 21.8042 6.53477 21.4125C6.1431 21.0208 5.94727 20.55 5.94727 20C5.94727 19.45 6.1431 18.9792 6.53477 18.5875C6.92643 18.1958 7.39727 18 7.94727 18C8.49727 18 8.9681 18.1958 9.35977 18.5875C9.75143 18.9792 9.94727 19.45 9.94727 20C9.94727 20.55 9.75143 21.0208 9.35977 21.4125C8.9681 21.8042 8.49727 22 7.94727 22ZM17.9473 22C17.3973 22 16.9264 21.8042 16.5348 21.4125C16.1431 21.0208 15.9473 20.55 15.9473 20C15.9473 19.45 16.1431 18.9792 16.5348 18.5875C16.9264 18.1958 17.3973 18 17.9473 18C18.4973 18 18.9681 18.1958 19.3598 18.5875C19.7514 18.9792 19.9473 19.45 19.9473 20C19.9473 20.55 19.7514 21.0208 19.3598 21.4125C18.9681 21.8042 18.4973 22 17.9473 22ZM6.14727 4H20.8973C21.2806 4 21.5723 4.17083 21.7723 4.5125C21.9723 4.85417 21.9806 5.2 21.7973 5.55L18.2473 11.95C18.0639 12.2833 17.8181 12.5417 17.5098 12.725C17.2014 12.9083 16.8639 13 16.4973 13H9.04727L7.94727 15H18.9473C19.2306 15 19.4681 15.0958 19.6598 15.2875C19.8514 15.4792 19.9473 15.7167 19.9473 16C19.9473 16.2833 19.8514 16.5208 19.6598 16.7125C19.4681 16.9042 19.2306 17 18.9473 17H7.94727C7.19727 17 6.6306 16.6708 6.24727 16.0125C5.86393 15.3542 5.84727 14.7 6.19727 14.05L7.54727 11.6L3.94727 4H2.94727C2.66393 4 2.42643 3.90417 2.23477 3.7125C2.0431 3.52083 1.94727 3.28333 1.94727 3C1.94727 2.71667 2.0431 2.47917 2.23477 2.2875C2.42643 2.09583 2.66393 2 2.94727 2H4.57227C4.7556 2 4.9306 2.05 5.09727 2.15C5.26393 2.25 5.38893 2.39167 5.47227 2.575L6.14727 4Z" fill="white"/>
                        </g>
                    </svg>

                    <span class="information__text">{{ "detail.addProduct"|trans|sw_sanitize|upper }}</span>
                </button>

                {% set buyable = product.stock > 0 %}
                {% if buyable %}
                    {# @var \Swag\PayPal\Checkout\ExpressCheckout\ExpressCheckoutButtonData expressSettings #}
                    {% set expressSettings = page.extensions[constant('Swag\\PayPal\\Checkout\\ExpressCheckout\\ExpressCheckoutSubscriber::PAYPAL_EXPRESS_CHECKOUT_BUTTON_DATA_EXTENSION_ID')] %}

                    {% if expressSettings.productDetailEnabled %}
                        <div class="k11-py-10"></div>
                        <a href="#" class="paypal-button-link"
                           data-lazy-paypal-button="true"
                           data-lazy-paypal-button-options="{{ {
                               paypalButtonClass: 'paypal-button',
                               paypalExpressButtonOptions: expressSettings,
                               paypalAddErrorToken: sw_csrf('payment.paypal.add_error', {"mode": "token"}),
                               paypalBannerOptions: page.extensions[constant('Swag\\PayPal\\Installment\\Banner\\InstallmentBannerSubscriber::PAYPAL_INSTALLMENT_BANNER_DATA_EXTENSION_ID')],
                               paypalButtonNote: 'checkout.paypalButtonNote'|trans|sw_sanitize
                           }|json_encode }}">
                            <svg style="margin-top: 3px;" width="63px" height="20" viewBox="0 0 101 32" preserveAspectRatio="xMinYMin meet" xmlns="http:&#x2F;&#x2F;www.w3.org&#x2F;2000&#x2F;svg"><path fill="#003087" d="M 12.237 2.8 L 4.437 2.8 C 3.937 2.8 3.437 3.2 3.337 3.7 L 0.237 23.7 C 0.137 24.1 0.437 24.4 0.837 24.4 L 4.537 24.4 C 5.037 24.4 5.537 24 5.637 23.5 L 6.437 18.1 C 6.537 17.6 6.937 17.2 7.537 17.2 L 10.037 17.2 C 15.137 17.2 18.137 14.7 18.937 9.8 C 19.237 7.7 18.937 6 17.937 4.8 C 16.837 3.5 14.837 2.8 12.237 2.8 Z M 13.137 10.1 C 12.737 12.9 10.537 12.9 8.537 12.9 L 7.337 12.9 L 8.137 7.7 C 8.137 7.4 8.437 7.2 8.737 7.2 L 9.237 7.2 C 10.637 7.2 11.937 7.2 12.637 8 C 13.137 8.4 13.337 9.1 13.137 10.1 Z"></path><path fill="#003087" d="M 35.437 10 L 31.737 10 C 31.437 10 31.137 10.2 31.137 10.5 L 30.937 11.5 L 30.637 11.1 C 29.837 9.9 28.037 9.5 26.237 9.5 C 22.137 9.5 18.637 12.6 17.937 17 C 17.537 19.2 18.037 21.3 19.337 22.7 C 20.437 24 22.137 24.6 24.037 24.6 C 27.337 24.6 29.237 22.5 29.237 22.5 L 29.037 23.5 C 28.937 23.9 29.237 24.3 29.637 24.3 L 33.037 24.3 C 33.537 24.3 34.037 23.9 34.137 23.4 L 36.137 10.6 C 36.237 10.4 35.837 10 35.437 10 Z M 30.337 17.2 C 29.937 19.3 28.337 20.8 26.137 20.8 C 25.037 20.8 24.237 20.5 23.637 19.8 C 23.037 19.1 22.837 18.2 23.037 17.2 C 23.337 15.1 25.137 13.6 27.237 13.6 C 28.337 13.6 29.137 14 29.737 14.6 C 30.237 15.3 30.437 16.2 30.337 17.2 Z"></path><path fill="#003087" d="M 55.337 10 L 51.637 10 C 51.237 10 50.937 10.2 50.737 10.5 L 45.537 18.1 L 43.337 10.8 C 43.237 10.3 42.737 10 42.337 10 L 38.637 10 C 38.237 10 37.837 10.4 38.037 10.9 L 42.137 23 L 38.237 28.4 C 37.937 28.8 38.237 29.4 38.737 29.4 L 42.437 29.4 C 42.837 29.4 43.137 29.2 43.337 28.9 L 55.837 10.9 C 56.137 10.6 55.837 10 55.337 10 Z"></path><path fill="#009cde" d="M 67.737 2.8 L 59.937 2.8 C 59.437 2.8 58.937 3.2 58.837 3.7 L 55.737 23.6 C 55.637 24 55.937 24.3 56.337 24.3 L 60.337 24.3 C 60.737 24.3 61.037 24 61.037 23.7 L 61.937 18 C 62.037 17.5 62.437 17.1 63.037 17.1 L 65.537 17.1 C 70.637 17.1 73.637 14.6 74.437 9.7 C 74.737 7.6 74.437 5.9 73.437 4.7 C 72.237 3.5 70.337 2.8 67.737 2.8 Z M 68.637 10.1 C 68.237 12.9 66.037 12.9 64.037 12.9 L 62.837 12.9 L 63.637 7.7 C 63.637 7.4 63.937 7.2 64.237 7.2 L 64.737 7.2 C 66.137 7.2 67.437 7.2 68.137 8 C 68.637 8.4 68.737 9.1 68.637 10.1 Z"></path><path fill="#009cde" d="M 90.937 10 L 87.237 10 C 86.937 10 86.637 10.2 86.637 10.5 L 86.437 11.5 L 86.137 11.1 C 85.337 9.9 83.537 9.5 81.737 9.5 C 77.637 9.5 74.137 12.6 73.437 17 C 73.037 19.2 73.537 21.3 74.837 22.7 C 75.937 24 77.637 24.6 79.537 24.6 C 82.837 24.6 84.737 22.5 84.737 22.5 L 84.537 23.5 C 84.437 23.9 84.737 24.3 85.137 24.3 L 88.537 24.3 C 89.037 24.3 89.537 23.9 89.637 23.4 L 91.637 10.6 C 91.637 10.4 91.337 10 90.937 10 Z M 85.737 17.2 C 85.337 19.3 83.737 20.8 81.537 20.8 C 80.437 20.8 79.637 20.5 79.037 19.8 C 78.437 19.1 78.237 18.2 78.437 17.2 C 78.737 15.1 80.537 13.6 82.637 13.6 C 83.737 13.6 84.537 14 85.137 14.6 C 85.737 15.3 85.937 16.2 85.737 17.2 Z"></path><path fill="#009cde" d="M 95.337 3.3 L 92.137 23.6 C 92.037 24 92.337 24.3 92.737 24.3 L 95.937 24.3 C 96.437 24.3 96.937 23.9 97.037 23.4 L 100.237 3.5 C 100.337 3.1 100.037 2.8 99.637 2.8 L 96.037 2.8 C 95.637 2.8 95.437 3 95.337 3.3 Z"></path></svg>
                            <div class="paypal-button-loader">
                                <div class="paypal-button-spinner"></div>
                            </div>
                        </a>
                    {% endif %}
                {% endif %}
            {% endblock %}
        </div>
    {% endif %}
{% endblock %}

{% block page_product_detail_buy_container_paypal %}

{% endblock %}

{% block page_product_detail_buy_form_inner %}

    {% set product = page.product %}
    {# @var \PluszweiBackInStockReminder installmentBanner #}
    {% set showOnClearanceSale = config('PluszweiBackInStockReminder.config.showOnClearanceSale') %}
    {% if product.stock < 1 and (not product.isCloseout or showOnClearanceSale) %}
        {% if replacementProduct %}
            <div class="product-action">
                <a class="notify__button"
                   href="{{ seoUrl('frontend.detail.page', {'productId': product.customFields.replacement_product}) }}"
                   title="{{ "ersatzteilshop.productDetail.seeReplacementProduct"|trans|sw_sanitize }}">
                    <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <mask id="mask0_1675_416" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="24">
                            <rect x="0.447266" width="24" height="24" fill="#D9D9D9"/>
                        </mask>
                        <g mask="url(#mask0_1675_416)">
                            <path d="M7.44727 21L2.44727 16L7.44727 11L8.87227 12.4L6.27227 15H21.4473V17H6.27227L8.87227 19.6L7.44727 21ZM17.4473 13L16.0223 11.6L18.6223 9H3.44727V7H18.6223L16.0223 4.4L17.4473 3L22.4473 8L17.4473 13Z" fill="#1C1B1F"/>
                        </g>
                    </svg>
                    <span class="information__text">{{ "ersatzteilshop.productDetail.seeReplacementProduct"|trans|sw_sanitize|upper }}</span>
                </a>
                <p>
                    {% set replacementUrl = seoUrl('frontend.detail.page', {'productId': product.customFields.replacement_product}) %}
                    {% set replacementTitle = "ersatzteilshop.productDetail.seeReplacementProduct"|trans %}
                    {% set replacementText = replacementProduct|default('Ersatzartikel') %}
                    <strong>{{ "ersatzteilshop.productDetail.replacementProductNotice"|trans({'%product%': "<a href='" ~ replacementUrl ~ "' title='" ~ replacementTitle ~ "'>" ~ replacementText ~ "</a>"
                        })|raw }}</strong>
                </p>
            </div>
        {% else%}
            {% sw_include '@Storefront/storefront/page/product-detail/v3/subscribe-back-in-stock-form.html.twig' with {'product': product} %}
        {% endif %}
    {% else %}
        {{ parent() }}
    {% endif %}
{% endblock %}
