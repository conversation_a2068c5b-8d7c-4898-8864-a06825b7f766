{% sw_extends '@Storefront/storefront/page/_page.html.twig' %}

{% block base_main_container %}
    <div class="container-main" >
        {% block page_main_content %}
            {% block page_breadcrumb %}
                <ul class="breadcrumb" itemscope="" itemtype="http://schema.org/BreadcrumbList">
                    <li class="breadcrumb-item" itemprop="itemListElement" itemscope=""
                        itemtype="http://schema.org/ListItem">
                        <a href="{{ seoUrl('frontend.home.page') }}" itemprop="item"><span
                                    itemprop="name">{{ 'ersatzteilshop.breadcrumb.home'|trans|sw_sanitize }}</span></a>
                        <meta itemprop="position" content="1">
                    </li>
                    <li class="breadcrumb-item" itemprop="itemListElement" itemscope=""
                        itemtype="http://schema.org/ListItem">
                        <span itemprop="item">
                            <span itemprop="name">{{ 'ersatzteilshop.breadcrumb.spareparts'|trans|sw_sanitize }}</span>
                            <meta itemprop="position" content="2">
                        </span>
                    </li>
                </ul>
            {% endblock %}
            {% block page_content %}
                <main class="content-main"  data-dummy-product="true">
                    {% block base_content_heading %}
                        <h1 class="pt-3">
                            {{ 'ersatzteilshop.applianceGroup.applianceSearchWidget.heading'|trans|sw_sanitize }}
                        </h1>
                       {% sw_include '@Storefront/storefront/component/widgets/standard-appliance-search-widget.html.twig' %}
                    {% endblock %}

                    {% block appliance_categories %}
                        {% if page.applianceCategories and page.applianceCategories.count > 0 %}
                        <div class="pt-5">
                            <h2><strong>{{ 'ersatzteilshop.applianceGroup.categoryHeader'|trans }}</strong></h2>
                        </div>
                        <ul class="all-brands">
                            {% for category in page.applianceCategories %}
                                <li>
                                    <a title="{{ category.translated.name }}" href="{{ seoUrl('frontend.appliance.category.page', {'categoryId': category.id}) }}">
                                        {{ category.translated.name }}
                                    </a>
                                </li>
                            {% endfor %}
                        </ul>
                        {% endif %}
                    {% endblock %}

{#                    {% block model_group_table %}#}
{#                        {% if page.applianceGroups and page.applianceGroups.count > 0 %}#}
{#                            <div class="pt-5">#}
{#                                <h2><strong>{{ 'ersatzteilshop.landingpage.applianceGroupTable'|trans }}</strong></h2>#}
{#                                <table id="appliance-result" class="table table-responsive-sm">#}
{#                                    <thead>#}
{#                                    <tr>#}
{#                                        <th scope="col">{{ 'ersatzteilshop.applianceGroup.listing.category'|trans|sw_sanitize }} <br><span class="hide-pc">{{ 'ersatzteilshop.applianceGroup.listing.manufacturer'|trans|sw_sanitize }}</span></th>#}
{#                                        <th scope="col" class="hide-mb">{{ 'ersatzteilshop.applianceGroup.listing.manufacturer'|trans|sw_sanitize }}</th>#}
{#                                        <th scope="col" class="hide-mb">{{ 'ersatzteilshop.applianceGroup.listing.applianceGroupCode'|trans|sw_sanitize }}</th>#}
{#                                        <th scope="col"><span class="hide-pc">{{ 'ersatzteilshop.applianceGroup.listing.applianceGroupCode'|trans|sw_sanitize }}<span class="hide-pc">&#8205</span>#}
{#                                        <th scope="col"></th>#}
{#                                    </tr>#}
{#                                    </thead>#}
{#                                    <tbody>#}
{#                                    {% for item in page.applianceGroups %}#}
{#                                        {% set applianceGroupUrl = seoUrl('frontend.appliance.group.page', {'applianceGroupId': item.id}) %}#}
{#                                        <tr style="transform: rotate(0);">#}
{#                                            <td> <a class="rowlink table-td-inlined-break" title="{{ item.modelCode }}"#}
{#                                                    href="{{ applianceGroupUrl }}"> {{ item.category ? item.category.translated.name : '' }}</a>#}
{#                                                <br><span class="hide-pc">{{ item.manufacturer ? item.manufacturer.translated.name : '' }}</span>#}
{#                                            </td>#}
{#                                            <td class="hide-mb">#}
{#                                                <span class="table-td-inlined-break">#}
{#                                                    {{ item.manufacturer.translated.name }}#}
{#                                                </span>#}
{#                                            </td>#}
{#                                            <td class="hide-mb">#}
{#                                                <span class="table-td-inlined-break">#}
{#                                                    {% if item.modelCode %}{{ item.modelCode }}{% else %}&#8205{% endif %}#}
{#                                                </span>#}
{#                                            </td>#}
{#                                            <td>#}
{#                                                <span class="hide-pc table-td-inlined-break">#}
{#                                                    {% if item.modelCode %}{{ item.modelCode }}{% else %}&#8205{% endif %}#}
{#                                                </span>#}
{#                                                <span class="table-td-inlined-break">&#8205</span>#}
{#                                            </td>#}
{#                                            <td>#}
{#                                                <span class="appliance-link hide-mb look-like-link">#}
{#                                                    {{ 'ersatzteilshop.applianceGroup.show'|trans|sw_sanitize }}#}
{#                                                </span>#}
{#                                                <span class="appliance-link hide-pc look-like-link">#}
{#                                                    {{ 'ersatzteilshop.applianceGroup.showMb'|trans|sw_sanitize }}#}
{#                                                </span>#}
{#                                            </td>#}
{#                                        </tr>#}
{#                                    {% endfor %}#}
{#                                    </tbody>#}
{#                                </table>#}

{#                                <div class="cms-element-product-listing-actions row pagination-top" style="margin-right: -15px;">#}
{#                                    <div class="col-md-auto">#}
{#                                        {% sw_include '@Storefront/storefront/page/search/pagination.html.twig' with {#}
{#                                            entities: page.applianceGroups,#}
{#                                            criteria: page.applianceGroups.criteria#}
{#                                        } %}#}
{#                                    </div>#}
{#                                </div>#}

{#                            </div>#}
{#                        {% endif %}#}
{#                    {% endblock %}#}

{#                    {% block top_appliances_table %}#}
{#                        {% if page.appliances and page.appliances.count > 0 %}#}
{#                        <div class="pt-4">#}
{#                            <h2><strong>{{ 'ersatzteilshop.landingpage.applianceTable'|trans }}</strong></h2>                            {% block appliance_search_result %}#}
{#                                <div class="row">#}
{#                                    <div class="col-md-12">#}
{#                                        <table id="appliance-result" class="table table-responsive-sm">#}
{#                                            <thead>#}
{#                                            <tr>#}
{#                                                <th scope="col">{{ 'ersatzteilshop.appliance.listing.category'|trans|sw_sanitize }} <br><span class="hide-pc">{{ 'ersatzteilshop.appliance.listing.manufacturer'|trans|sw_sanitize }}</span></th>#}
{#                                                <th scope="col" class="hide-mb">{{ 'ersatzteilshop.appliance.listing.manufacturer'|trans|sw_sanitize }}</th>#}
{#                                                <th scope="col" class="hide-mb">{{ 'ersatzteilshop.appliance.listing.modelName'|trans|sw_sanitize }}</th>#}
{#                                                <th scope="col"><span class="hide-pc">{{ 'ersatzteilshop.appliance.listing.modelName'|trans|sw_sanitize }}<br></span>{{ 'ersatzteilshop.appliance.listing.modelCode'|trans|sw_sanitize }}</th>#}
{#                                                <th scope="col" class="action-btn"></th>#}
{#                                            </tr>#}
{#                                            </thead>#}
{#                                            <tbody>#}

{#                                            {% for item in page.appliances %}#}
{#                                                {% set applianceUrl = seoUrl('frontend.appliance.page', {'applianceId': item.id}) %}#}

{#                                                <tr style="transform: rotate(0);">#}
{#                                                    <td> <a class="rowlink table-td-inlined-break" title="{{ item.modelCode }}"#}
{#                                                            href="{{ applianceUrl }}"> {{ item.category ? item.category.translated.name : '' }}</a>#}
{#                                                        <br><span class="hide-pc">{{ item.manufacturer ? item.manufacturer.translated.name : '' }}</span>#}
{#                                                    </td>#}
{#                                                    <td class="hide-mb">#}
{#                                                        <span class="table-td-inlined-break">#}
{#                                                            {{ item.manufacturer.translated.name }}#}
{#                                                        </span>#}
{#                                                    </td>#}
{#                                                    <td class="hide-mb">#}
{#                                                        <span class="table-td-inlined-break">#}
{#                                                            {% if item.modelName %}{{ item.modelName }}{% else %}&#8205{% endif %}#}
{#                                                        </span>#}
{#                                                    </td>#}
{#                                                    <td>#}
{#                                                        <span class="hide-pc table-td-inlined-break">#}
{#                                                            {% if item.modelName %}{{ item.modelName }}{% else %}&#8205{% endif %}#}
{#                                                        </span>#}
{#                                                        <span class="table-td-inlined-break">#}
{#                                                            {% if item.modelCode %}{{ item.modelCode }}{% else %}&#8205{% endif %}#}
{#                                                        </span>#}
{#                                                    </td>#}
{#                                                    <td>#}
{#                                                        <span class="appliance-link hide-mb look-like-link">#}
{#                                                            {{ 'ersatzteilshop.appliance.ctaPcShow'|trans|sw_sanitize }}#}
{#                                                        </span>#}
{#                                                        <span class="appliance-link hide-pc look-like-link">#}
{#                                                            {{ 'ersatzteilshop.appliance.ctaMbShow'|trans|sw_sanitize }}#}
{#                                                        </span>#}
{#                                                    </td>#}
{#                                                </tr>#}
{#                                            {% endfor %}#}
{#                                            </tbody>#}
{#                                        </table>#}
{#                                    </div>#}
{#                                </div>#}
{#                            {% endblock %}#}
{#                        </div>#}
{#                        {% endif %}#}
{#                    {% endblock %}#}

{#                    {% block not_found_error_content %}#}
{#                        <div class="row error-page-content-desktop">#}
{#                            <div class="col-12">#}
{#                                <h2 class="error-page-heading-desktop pb-5" style="text-align: left !important;">#}
{#                                    <img src="{{ asset('bundles/ersatzteilshop/assets/icons/no_result_Asset.png') }}"#}
{#                                         alt="Keine Suchergebnisse">#}
{#                                    {{ 'ersatzteilshop.landingpage.subHeader'|trans }}#}
{#                                </h2>#}
{#                            </div>#}
{#                            {% block error_content_col_1 %}#}
{#                                <div class="col-4">#}
{#                                    <h3>#}
{#                                        <img style="width: 40px; margin-right: 10px" src="{{ asset('bundles/ersatzteilshop/assets/icons/icon-nbr-1.svg') }}" alt="Schritt Eins">#}
{#                                        {{ "error.subheadingOne"|trans|sw_sanitize }}#}
{#                                    </h3>#}
{#                                    <p class="error-page-description">#}
{#                                        {{ "error.errorDiagnosis"|trans|sw_sanitize }}#}
{#                                        {{ "error.diagnosisParagraphOne"|trans|raw }}#}
{#                                    </p>#}
{#                                    <div>#}
{#                                        <a href="{{ 'ersatzteilshop.repairGuide.linkRepairGuide'|trans }}"#}
{#                                           class="btn btn-primary btn-repair-guide">#}
{#                                            {{ 'ersatzteilshop.repairGuide.btnRepairGuide'|trans }}#}
{#                                        </a>#}
{#                                    </div>#}
{#                                </div>#}
{#                            {% endblock %}#}

{#                            {% block error_content_col_2 %}#}
{#                                <div class="col-4">#}
{#                                    <h3>#}
{#                                        <img style="width: 40px; margin-right: 10px" src="{{ asset('bundles/ersatzteilshop/assets/icons/icon-nbr-2.svg') }}" alt="Schritt Zwei">#}
{#                                        {{ "error.subheadingTwo"|trans|sw_sanitize }}#}
{#                                    </h3>#}
{#                                    <p class="error-page-description">#}
{#                                        {{ "error.diagnosisParagraphTwo"|trans|raw }}#}
{#                                    </p>#}
{#                                    <p class="error-page-description">#}
{#                                        <strong>{{ "error.diagnosisColumnTwo"|trans|raw }}</strong>#}
{#                                    </p>#}
{#                                    {% block error_content_col_2_step_1 %}#}
{#                                        <p class="error-page-description">#}
{#                                            <strong>{{ "error.diagnosisColumnTwoStep1"|trans|raw }}</strong>#}
{#                                        </p>#}
{#                                        <p class="error-page-description">#}
{#                                            {{ "error.diagnosisColumnTwoStep1Description"|trans|raw }}#}
{#                                        </p>#}

{#                                        <a class="cms-element-search-widget-link"#}
{#                                           href="{{ path('frontend.type-number.widget') }}"#}
{#                                           data-toggle="modal"#}
{#                                           title="{{ "search.searchWidgetLink"|trans|striptags }}"#}
{#                                           data-url="{{ path('frontend.type-number.widget') }}"#}
{#                                           data-modal-class="search-widget-modal">#}
{#                                            &#x25B7 {{ "search.searchWidgetLink"|trans }}#}
{#                                        </a>#}
{#                                    {% endblock %}#}

{#                                    {% block error_content_col_2_step_2 %}#}
{#                                        <p class="error-page-description" style="margin-top: 10px">#}
{#                                            <strong>{{ "error.diagnosisColumnTwoStep2"|trans|raw }}</strong>#}
{#                                        </p>#}
{#                                        <p class="error-page-description">#}
{#                                            {{ "error.diagnosisColumnTwoStep2Description"|trans|raw }}#}
{#                                        </p>#}
{#                                    {% endblock %}#}

{#                                    {% block error_content_col_2_step_3 %}#}
{#                                        <p class="error-page-description">#}
{#                                            <strong>{{ "error.diagnosisColumnTwoStep3"|trans|raw }}</strong>#}
{#                                        </p>#}
{#                                        <p class="error-page-description">#}
{#                                            {{ "error.diagnosisColumnTwoStep3Description"|trans|raw }}#}
{#                                        </p>#}
{#                                    {% endblock %}#}
{#                                </div>#}
{#                            {% endblock %}#}

{#                            {% block error_content_col_3 %}#}
{#                                <div class="col-4">#}
{#                                    <h3>#}
{#                                        <img style="width: 40px; margin-right: 10px" src="{{ asset('bundles/ersatzteilshop/assets/icons/icon-nbr-3.svg') }}" alt="Schritt Drei">#}
{#                                        {{ "error.subheadingThree"|trans|sw_sanitize }}#}
{#                                    </h3>#}
{#                                    <p class="error-page-description">#}
{#                                        {{ "error.diagnosisColumnThree"|trans|raw }}#}
{#                                    </p>#}
{#                                    <div>#}
{#                                        <a href="{{ 'ersatzteilshop.repairGuide.linkRepairGuide'|trans }}"#}
{#                                           class="btn btn-primary btn-repair-guide">#}
{#                                            {{ 'ersatzteilshop.repairGuide.btnRepairGuide'|trans }}#}
{#                                        </a>#}
{#                                        <a href="{{ 'ersatzteilshop.repairInstruction.repairInstructionLink'|trans }}"#}
{#                                           class="btn btn-primary btn-repair-guide">#}
{#                                            {{ 'ersatzteilshop.repairInstruction.repairInstructionsLinkHeading'|trans }}#}
{#                                        </a>#}
{#                                    </div>#}
{#                                </div>#}
{#                            {% endblock %}#}
{#                        </div>#}
{#                        {% sw_include '@Storefront/storefront/page/error/mobile-view.html.twig' %}#}
{#                    {% endblock %}#}

{#                    {% block page_question_one %}#}
{#                        <div class="pt-5">#}
{#                            <h3>{{ 'ersatzteilshop.landingpage.questionOne'|trans }}</h3>#}
{#                            {{ 'ersatzteilshop.landingpage.answerOne'|trans|raw }}#}
{#                        </div>#}

{#                    {% endblock %}#}


{#                    {% block page_question_two %}#}
{#                        <div class="pt-4">#}
{#                            <h3>{{ 'ersatzteilshop.landingpage.questionTwo'|trans }}</h3>#}
{#                            {{ 'ersatzteilshop.landingpage.answerTwo'|trans|raw }}#}
{#                            <p><a href="/typennummern/uebersicht.html" style="text-decoration: underline">{{ 'ersatzteilshop.landingpage.link'|trans }}</a></p>#}
{#                        </div>#}
{#                    {% endblock %}#}

{#                    {% block page_question_three %}#}
{#                        <div class="pt-4">#}
{#                            <h3>{{ 'ersatzteilshop.landingpage.questionThree'|trans }}</h3>#}
{#                            {{ 'ersatzteilshop.landingpage.answerThree'|trans|raw }}#}
{#                        </div>#}
{#                    {% endblock %}#}
                </main>
            {% endblock %}
        {% endblock %}
    </div>
{% endblock %}
