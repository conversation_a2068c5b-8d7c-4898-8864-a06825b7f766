{% sw_extends '@Storefront/storefront/component/product/card/box-standard.html.twig' %}

{% set thumbnailUrl = null %}
{% set thumbnails = product.cover.media.thumbnails %}
{% set awsoProduct = product.extensions.XantenAswo %}
{% set productUrl = awsoProduct ? app.request.attributes.get('sw-storefront-url') ~ product.extension('XantenAswo').url : seoUrl('frontend.detail.page', {'productId': product.id}) %}
{% set noImage = asset('bundles/ersatzteilshop/assets/img/no_product_image.png') %}

{% block component_product_box_rich_snippets %}
    {% if jsonSnippet %}
        <template data-schema-snippet="{{ jsonSnippet|json_encode }}">
        </template>
    {% endif %}
    {% if noMeta is not defined %}
        {{ parent() }}
    {% endif %}
{% endblock %}

{% block component_product_box_content %}
    <div class="card-body badge-container-mobile"
         data-product-id="{{ product.id }}"
         {% if product.customFields.ersProductId %}data-ers-product-id="{{ product.customFields.ersProductId }}"{% endif %}>
        <a class="card-body badge-container-mobile-content"
           data-product-url="true"
           href="{{ dummyProduct ? 'javascript:void(0)' : productUrl }}"
           title="{{ name }}"
                {% if awsoProduct %} rel="noindex, nofollow, noarchive, noimageindex, nosnippet" {% endif %}>
            {{ block('component_product_box_badges') }}
            {{ block('component_product_box_rich_snippets') }}
            {{ block('component_product_box_image') }}
            {{ block('component_product_box_info') }}
        </a>
        <div class="product-price-wrapper">
            {% block component_product_box_price %}
                {% sw_include '@Storefront/storefront/component/product/card/price-unit.html.twig' with {
                    displayStar: true
                } %}
            {% endblock %}
            {% if product.customFields.product_unit_price %}
                <p class="product-unit-price">{{ product.customFields.product_unit_price }}</p>
            {% endif %}

            {% if not isRepairProduct %}
                {% sw_include '@Storefront/storefront/component/product/delivery-time.html.twig' with {
                    enableNotification: true,
                    dummyProduct: dummyProduct
                } %}
            {% endif %}

            {% block component_product_box_rating %}
                {% if config('core.listing.showReview') %}
                    <div class="product-rating">
                        {% set productUrl = dummyProduct ? 'javascript:void(0)' : productUrl %}
                        {% if product.ratingAverage > 0 and config('core.listing.showReview') %}
                            <a href="{{ productUrl }}#reviewPanel" class="k11-black-link star-rating">
                                {% set rating = product.ratingAverage %}
                                {% set full_stars = rating|round(0, 'floor') %}
                                {% set partial_fill = (rating - full_stars) * 100 %}

                                {% for i in 1..5 %}
                                    {% if i <= full_stars %}
                                        {# Full star #}
                                        <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <mask id="mask0_4790_28545_{{ i }}" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="25">
                                                <rect x="0.526367" y="0.231689" width="24" height="24" fill="#D9D9D9"/>
                                            </mask>
                                            <g mask="url(#mask0_4790_28545_{{ i }})">
                                                <path d="M12.5264 2.23169L15.3264 8.85669L22.5264 9.48169L17.0764 14.2067L18.7014 21.2317L12.5264 17.5067L6.35137 21.2317L7.97637 14.2067L2.52637 9.48169L9.72637 8.85669L12.5264 2.23169Z" fill="#202E3D"/>
                                            </g>
                                        </svg>
                                    {% elseif i == full_stars + 1 and partial_fill > 0 %}
                                        {# Partially filled star #}
                                        <svg width="25" height="25" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 25">
                                            <mask id="mask0_4790_28547_{{ i }}" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="25">
                                                <rect x="0.526367" y="0.231689" width="24" height="24" fill="#D9D9D9"/>
                                            </mask>
                                            <g mask="url(#mask0_4790_28547_{{ i }})">
                                                <path d="M12.5264 2.23169L15.3264 8.85669L22.5264 9.48169L17.0764 14.2067L18.7014 21.2317L12.5264 17.5067L6.35137 21.2317L7.97637 14.2067L2.52637 9.48169L9.72637 8.85669L12.5264 2.23169ZM12.5264 6.55669L10.9514 10.3317L6.77637 10.7817L9.67637 13.2067L8.80137 17.3067L12.5264 15.1817L16.2514 17.3067L15.3764 13.2067L18.2764 10.7817L14.1014 10.3317L12.5264 6.55669Z" fill="#202E3D"/>
                                            </g>
                                            <clipPath id="star-clip-{{ i }}">
                                                {% set adjustedWidth = 21 * partial_fill / 100 %}
                                                {% set adjustedX = 2.526367 - (adjustedWidth * 0.05) %}
                                                <rect x="{{ adjustedX }}" y="0.231689" width="{{ adjustedWidth }}" height="24"/>
                                            </clipPath>
                                            <path clip-path="url(#star-clip-{{ i }})" d="M12.5264 2.23169L15.3264 8.85669L22.5264 9.48169L17.0764 14.2067L18.7014 21.2317L12.5264 17.5067L6.35137 21.2317L7.97637 14.2067L2.52637 9.48169L9.72637 8.85669L12.5264 2.23169Z" fill="#202E3D"/>
                                        </svg>
                                    {% else %}
                                        {# Empty star #}
                                        <svg width="25" height="25" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 25">
                                            <mask id="mask0" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="25">
                                                <rect x="0.526367" y="0.231689" width="24" height="24" fill="#D9D9D9"/>
                                            </mask>
                                            <g mask="url(#mask0)">
                                                <path d="M12.5264 2.23169L15.3264 8.85669L22.5264 9.48169L17.0764 14.2067L18.7014 21.2317L12.5264 17.5067L6.35137 21.2317L7.97637 14.2067L2.52637 9.48169L9.72637 8.85669L12.5264 2.23169ZM12.5264 6.55669L10.9514 10.3317L6.77637 10.7817L9.67637 13.2067L8.80137 17.3067L12.5264 15.1817L16.2514 17.3067L15.3764 13.2067L18.2764 10.7817L14.1014 10.3317L12.5264 6.55669Z" fill="#202E3D"/>
                                            </g>
                                        </svg>
                                    {% endif %}
                                {% endfor %}

                                <u>({{ product.extensions.rating.count }})</u>
                            </a>
                        {% endif %}
                    </div>
                {% endif %}
            {% endblock %}

            {% block component_product_box_action %}
                {% if isRepairProduct %}
                    {% sw_include '@Storefront/storefront/component/product/card/action.html.twig' with {
                        isRepairProduct: true
                    } %}
                {% elseif product.stock > 0 %}
                    {% if dummyProduct %}
                        <div class="delivery-information__badge delivery-information__badge--short k11-pointer w-100">
                            <svg class="k11-m-5" width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <mask id="mask0_3476_18337" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="25">
                                    <rect x="0.21875" y="0.71875" width="24" height="24" fill="#D9D9D9"/>
                                </mask>
                                <g mask="url(#mask0_3476_18337)">
                                    <path d="M21.8188 23.7188L18.7437 20.6688C18.4438 20.8521 18.1229 20.9896 17.7812 21.0813C17.4396 21.1729 17.0854 21.2188 16.7188 21.2188C15.6187 21.2188 14.6771 20.8271 13.8938 20.0438C13.1104 19.2604 12.7188 18.3188 12.7188 17.2188C12.7188 16.1187 13.1104 15.1771 13.8938 14.3938C14.6771 13.6104 15.6187 13.2188 16.7188 13.2188C17.8188 13.2188 18.7604 13.6104 19.5438 14.3938C20.3271 15.1771 20.7188 16.1187 20.7188 17.2188C20.7188 17.6021 20.6688 17.9646 20.5688 18.3062C20.4688 18.6479 20.3271 18.9688 20.1438 19.2688L23.2188 22.3188L21.8188 23.7188ZM5.71875 21.2188C4.61875 21.2188 3.67708 20.8271 2.89375 20.0438C2.11042 19.2604 1.71875 18.3188 1.71875 17.2188C1.71875 16.1187 2.11042 15.1771 2.89375 14.3938C3.67708 13.6104 4.61875 13.2188 5.71875 13.2188C6.81875 13.2188 7.76042 13.6104 8.54375 14.3938C9.32708 15.1771 9.71875 16.1187 9.71875 17.2188C9.71875 18.3188 9.32708 19.2604 8.54375 20.0438C7.76042 20.8271 6.81875 21.2188 5.71875 21.2188ZM16.7188 19.2188C17.2688 19.2188 17.7396 19.0229 18.1313 18.6313C18.5229 18.2396 18.7188 17.7688 18.7188 17.2188C18.7188 16.6687 18.5229 16.1979 18.1313 15.8063C17.7396 15.4146 17.2688 15.2188 16.7188 15.2188C16.1687 15.2188 15.6979 15.4146 15.3063 15.8063C14.9146 16.1979 14.7188 16.6687 14.7188 17.2188C14.7188 17.7688 14.9146 18.2396 15.3063 18.6313C15.6979 19.0229 16.1687 19.2188 16.7188 19.2188ZM5.71875 10.2188C4.61875 10.2188 3.67708 9.82708 2.89375 9.04375C2.11042 8.26042 1.71875 7.31875 1.71875 6.21875C1.71875 5.11875 2.11042 4.17708 2.89375 3.39375C3.67708 2.61042 4.61875 2.21875 5.71875 2.21875C6.81875 2.21875 7.76042 2.61042 8.54375 3.39375C9.32708 4.17708 9.71875 5.11875 9.71875 6.21875C9.71875 7.31875 9.32708 8.26042 8.54375 9.04375C7.76042 9.82708 6.81875 10.2188 5.71875 10.2188ZM16.7188 10.2188C15.6187 10.2188 14.6771 9.82708 13.8938 9.04375C13.1104 8.26042 12.7188 7.31875 12.7188 6.21875C12.7188 5.11875 13.1104 4.17708 13.8938 3.39375C14.6771 2.61042 15.6187 2.21875 16.7188 2.21875C17.8188 2.21875 18.7604 2.61042 19.5438 3.39375C20.3271 4.17708 20.7188 5.11875 20.7188 6.21875C20.7188 7.31875 20.3271 8.26042 19.5438 9.04375C18.7604 9.82708 17.8188 10.2188 16.7188 10.2188Z" fill="#202E3D"/>
                                </g>
                            </svg>
                            <span class="k11-mr-5 k11-font-size-14 k11-font-weight-900">JETZT PRÜFEN</span>
                        </div>
                    {% else %}
                        {% sw_include '@Storefront/storefront/component/product/card/action.html.twig' %}
                    {% endif %}
                {% endif %}
            {% endblock %}

            {% if product.extensions.atProduct.id %}
                <div class="text-center">
                    <a class="at-product-link"
                       href="{{ dummyProduct ? 'javascript:void(0)' : seoUrl('frontend.detail.page', {'productId': product.extensions.atProduct.id}) }}">
                        {{ "listing.boxAtProductDetail"|trans|sw_sanitize }}
                    </a>
                </div>
            {% endif %}

            {% if awsoProduct %}
                {% if product.extension('alternativeProductUrl').url %}
                    <div class="text-center">
                        <a class="at-product-link"
                           href="{{ dummyProduct ? 'javascript:void(0)' : product.extension('alternativeProductUrl').url }}">
                            {{ "listing.aswoBoxAtProductDetail"|trans|sw_sanitize }}
                        </a>
                    </div>
                {% endif %}
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block component_product_box_image %}
    <div class="product-image-wrapper badge-overlay-container-search">
        {# fallback if display mode is not set #}
        {% set displayMode = displayMode ?: 'standard' %}

        {# set display mode 'cover' for box-image with standard display mode #}
        {% if layout == 'image' and displayMode == 'standard' %}
            {% set displayMode = 'cover' %}
        {% endif %}

        <div class="product-image-link is-{{ displayMode }} badge-overlay-image" >
            {% if cover.url %}
                {% set attributes = {
                    'class': 'product-image is-'~displayMode,
                    'alt': (cover.translated.alt ?: name),
                    'title': (cover.translated.title ?: name)
                } %}

                {% if displayMode == 'cover' or displayMode == 'contain' %}
                    {% set attributes = attributes|merge({ 'data-object-fit': displayMode }) %}
                {% endif %}

                {% if thumbnails|length > 0 %}
                    {% for thumbnail in thumbnails %}
                        {% if thumbnail.width is same as(150) or thumbnail.width is same as(120) %}
                            {% set thumbnailUrl = thumbnail.url %}
                        {% endif %}
                    {% endfor %}
                {% endif %}

                {% if thumbnailUrl %}
                    <img width="150px" height="150px" src="{{ thumbnailUrl }}"
                         alt="{{ name }}"
                         onerror="this.src = '{{ noImage }}'">
                {% else %}
                    {% cf_single_thumbnail 'product-list-image-thumbnails' with {
                        load: false,
                        media: product.cover.media,
                        attributes: {
                            'alt': name,
                            'title': name,
                            'src': product.cover.media.url,
                            onerror: 'this.src = ' ~ noImage
                        }
                    } %}
                {% endif %}
            {% else %}
                <div class="product-image-placeholder">
                    <img width="150px" height="150px" src="{{ noImage }}" alt="{{ name }}"/>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block component_product_box_info %}
    <div class="product-info k11-mb-10">
        {% block component_product_box_name %}
            {% if not awsoProduct %}
                {% sw_include "@Storefront/storefront/component/product/atot_badge.html.twig" with
                    {
                        product: product,
                        isProductBox: true
                    }
                %}
            {% elseif awsoProduct  %}
                {% sw_include "@Storefront/storefront/component/product/aswo_atot_badge.html.twig" with
                    {
                        product: product,
                        isProductBox: true
                    }
                %}
            {% endif %}
            <div class="product-name">
                {{ name }}
            </div>
        {% endblock %}

        {% block component_product_box_description %}
        {% endblock %}

        <p class="btn-detail">{{ "listing.boxProductDetails"|trans|sw_sanitize }}</p>
    </div>
{% endblock %}
