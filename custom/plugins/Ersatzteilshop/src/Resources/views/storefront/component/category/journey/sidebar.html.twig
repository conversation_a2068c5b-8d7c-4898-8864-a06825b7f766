{% set parentCategory = productCategory.parent %}

<div class="new-side-bar">
    {# children categories #}
    <div class="page-sidebar-section product-sidebar">
        <div class="page-sidebar-section-title">
            {{ 'ersatzteilshop.page.categorySection'|trans|sw_sanitize }}

            {# Back to Home #}
            {% if productCategory.level == 2 %}
                {% set backTitle = 'ersatzteilshop.breadcrumb.home'|trans|sw_sanitize %}
                {% set parentUrl = '/' %}
            {% else %}
                {% set backTitle = parentCategory.translated.name %}
                {% set parentUrl = '#seoUrls#' ~ (parentCategory ? parentCategory.id : productCategory.id) ~ '#' ~ currentCategory.type ~ '#%manufacturer%' %}
            {% endif %}

            <p class="page-sidebar-category-parent">
                <a href="{{ parentUrl }}"
                   title="{{ parentCategory ? parentCategory.translated.name : productCategory.translated.name }}">
                    <span class="parent-category-name">{{ backTitle }}</span>
                </a>
            </p>

            <div class="page-sidebar-category-active">{{ productCategory.translated.name }}</div>

            <ul class="page-sidebar-category">
                {% for children in productCategory.children.sortByName() %}
                    <li>
                        <a href="#seoUrls#{{ children.id }}#{{ currentCategory.type }}#%manufacturer%"
                           title="{{ children.translated.name }}">
                            {% if children.media %}
                                {% sw_include '@Ersatzteilshop/storefront/utilities/category-icon.html.twig' with {
                                    'category': children
                                } %}
                            {% else %}
                                <img src="{{ asset('bundles/ersatzteilshop/assets/img/no_icon_cat.svg') }}"
                                     alt="{{ children.translated.name }}">
                            {% endif %}
                            <span>{{ children.translated.name }}</span>
                        </a>
                    </li>
                {% endfor %}
            </ul>
        </div>
    </div>

    {# miscs #}
    {% sw_include '@Storefront/storefront/page/category/product/brands.html.twig' %}
    {% sw_include '@Storefront/storefront/page/category/allego.html.twig' %}
</div>
