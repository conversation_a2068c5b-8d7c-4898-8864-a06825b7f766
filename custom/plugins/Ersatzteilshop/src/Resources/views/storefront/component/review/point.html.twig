{% block component_review_point %}
    {% if controllerName is same as ('Product') and controllerAction is same as ('index') %}
         {% set productDetailPage = true %}
    {% endif %}
    {% if size is not defined %}
        {% set size = 'xs' %}
    {% endif %}
    {% if clickableRating is not defined %}
        {% set clickableRating = true %}
    {% endif %}
    {% if type == 'half' %}
        <div class="point-container">
            <div class="point-rating point-partial-placeholder">
                <a class="reviewPoint{% if noScroll %}-no-scroll{% endif %}" {% if clickableRating %} href="{{ productUrl }}{% if productDetailPage %}#review"{% else %}#reviewPanel"{% endif %}{% endif %}>
                     <span class="icon icon-star icon-{{ size}} icon-review">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <mask id="mask0_1664_97550" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
                                    <rect width="24" height="24" fill="#D9D9D9"/>
                                </mask>
                                <g mask="url(#mask0_1664_97550)">
                                    <path d="M8.85 17.825L12 15.925L15.15 17.85L14.325 14.25L17.1 11.85L13.45 11.525L12 8.125L10.55 11.5L6.9 11.825L9.675 14.25L8.85 17.825ZM5.825 22L7.45 14.975L2 10.25L9.2 9.625L12 3L14.8 9.625L22 10.25L16.55 14.975L18.175 22L12 18.275L5.825 22Z" fill="#FDBB33"/>
                                </g>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M12 3L9.2 9.625L2 10.25L7.45 14.975L5.825 22L12 18.275V3Z" fill="#FDBB33"/>
                          </svg>
                     </span>
                </a>
            </div>
            <div class="point-rating point-partial" style="clip-path: inset(0 {{ (1 - left) * 100 }}% 0 0)">
                <a class="reviewPoint{% if noScroll %}-no-scroll{% endif %}" {% if clickableRating %} href="{{ productUrl }}{% if productDetailPage %}#review"{% else %}#reviewPanel"{% endif %}{% endif %}>
                    <span class="icon icon-star icon-{{ size}} icon-review">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <mask id="mask0_1664_97550" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
                                <rect width="24" height="24" fill="#D9D9D9"/>
                            </mask>
                            <g mask="url(#mask0_1664_97550)">
                                <path d="M8.85 17.825L12 15.925L15.15 17.85L14.325 14.25L17.1 11.85L13.45 11.525L12 8.125L10.55 11.5L6.9 11.825L9.675 14.25L8.85 17.825ZM5.825 22L7.45 14.975L2 10.25L9.2 9.625L12 3L14.8 9.625L22 10.25L16.55 14.975L18.175 22L12 18.275L5.825 22Z" fill="#FDBB33"/>
                            </g>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M12 3L9.2 9.625L2 10.25L7.45 14.975L5.825 22L12 18.275V3Z" fill="#FDBB33"/>
                      </svg>
                    </span>
                </a>
            </div>
        </div>
    {% elseif type == 'blank' %}
        <div class="point-container">
            <div class="point-rating point-blank">
                <span class="reviewPoint{% if noScroll %}-no-scroll{% endif %}">
                    {% sw_icon 'star' style { 'color': 'light', 'size': size, 'pack': 'solid' } %}
                </span>
            </div>
        </div>
    {% else %}
        <div class="point-container">
            <div class="point-rating point-full">
                <a class="reviewPoint{% if noScroll %}-no-scroll{% endif %}" {% if clickableRating %} href="{{ productUrl }}{% if productDetailPage %}#review"{% else %}#reviewPanel"{% endif %}{% endif %}>
                     <span class="icon icon-star icon-{{ size}} icon-review">
                       <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <mask id="mask0_1664_97526" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
                                <rect width="24" height="24" fill="#fedc70"/>
                            </mask>
                            <g mask="url(#mask0_1664_97526)">
                                <path d="M5.825 22L7.45 14.975L2 10.25L9.2 9.625L12 3L14.8 9.625L22 10.25L16.55 14.975L18.175 22L12 18.275L5.825 22Z" fill="#fedc70"/>
                            </g>
                        </svg>
                   </span>
                </a>
            </div>
        </div>
    {% endif %}

{% endblock %}
