{% if page.categoryType == "product" %}
    {% set manufacturer = page.manufacturer %}
    {% set category = page.category %}
    {% set level = category.level %}
    {% set parentCategory =  page.extensions.parentCategory %}
    {% set childrenCategories = page.extensions.childrenCategories.sortByName() %}

    {% if manufacturer %}
        {% set categoryUrl = seoUrl('frontend.category.manufacturer.page', { navigationId: category.id, manufacturerId: manufacturer.id }) %}
        {% set parentCategoryUrl = seoUrl('frontend.category.manufacturer.page', { navigationId: parentCategory.id, manufacturerId: manufacturer.id }) %}

        {% if parentCategory.parentId %}
            {% set parentParentCategoryUrl = seoUrl('frontend.category.manufacturer.page', { navigationId: parentCategory.parentId, manufacturerId: manufacturer.id }) %}
        {% endif %}
    {% else %}
        {% set categoryUrl = seoUrl('frontend.navigation.page', { navigationId: category.id }) %}
        {% set parentCategoryUrl = seoUrl('frontend.navigation.page', { navigationId: parentCategory.id }) %}

        {% if parentCategory.parentId %}
            {% set parentParentCategoryUrl = seoUrl('frontend.navigation.page', { navigationId: parentCategory.parentId }) %}
        {% endif %}
    {% endif %}
{% endif %}

<div class="new-side-bar">
    {# selected manufacturer and category #}
    {# if a manufacturer or a child category (level 3) is selected #}
    {% if manufacturer or level > 2 %}
        <div class="page-sidebar-section page-sidebar-selected">
            <div class="page-sidebar-section-title">{{ 'ersatzteilshop.page.yourSelection'|trans|sw_sanitize }}</div>

            <ul class="page-sidebar-category">

                {# Manufacturer #}
                {% if manufacturer %}
                    {% if manufacturer.media %}
                        {% set imgSrc = page.manufacturer.media.url %}
                    {% else %}
                        {% set imgSrc = asset('bundles/ersatzteilshop/assets/img/no_icon_cat.svg') %}
                    {% endif %}

                    <li>
                        <a href="{{ categoryUrl }}"
                           title="{{ manufacturer.translated.name }}">
                            <img src="{{ imgSrc }}"
                                 loading="lazy"
                                 alt="{{ page.manufacturer.translated.name }}">
                            <span>{{ page.manufacturer.translated.name }}</span>
                        </a>
                    </li>
                {% endif %}

                {# Parent category #}
                {% if parentCategory.level > 2 %}
                    <li>
                        <a href="{{ parentParentCategoryUrl }}"
                           title="{{ parentCategory.translated.name }}">
                            {% if parentCategory.media %}
                                {% sw_include '@Ersatzteilshop/storefront/utilities/category-icon.html.twig' with {
                                    'category': parentCategory
                                } %}
                            {% else %}
                                <img src="{{ asset('bundles/ersatzteilshop/assets/img/no_icon_cat.svg') }}"
                                     alt="{{ parentCategory.translated.name }}">
                            {% endif %}
                            <span>{{ parentCategory.translated.name }}</span>
                        </a>
                    </li>
                {% endif %}

                {# Current category #}
                {% if category.level > 2 %}
                    <li>
                        <a href="{{ parentCategoryUrl }}"
                           title="{{ category.translated.name }}">
                            {% if category.media %}
                                {% sw_include '@Ersatzteilshop/storefront/utilities/category-icon.html.twig' with {
                                    'category': category.category
                                } %}
                            {% else %}
                                <img src="{{ asset('bundles/ersatzteilshop/assets/img/no_icon_cat.svg') }}"
                                     alt="{{ category.translated.name }}">
                            {% endif %}
                            <span>{{ category.translated.name }}</span>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </div>
    {% endif %}


    {# children categories #}
    <div class="page-sidebar-section product-sidebar">
        <div class="page-sidebar-section-title">
            {{ 'ersatzteilshop.page.categorySection'|trans|sw_sanitize }}

            {# Back to Home #}
            {% if level == 2 %}
                {% set backTitle = 'ersatzteilshop.breadcrumb.home'|trans|sw_sanitize %}
            {% else %}
                {% set backTitle = parentCategory.translated.name %}
            {% endif %}

            <p class="page-sidebar-category-parent">
                <a href="{{ parentCategoryUrl }}"
                   title="{{ parentCategory.translated.name }}">
                    <span class="parent-category-name">{{ backTitle }}</span>
                </a>
            </p>

            <div class="page-sidebar-category-active">{{ category.translated.name }}</div>

            {% if childrenCategories|length %}
                <ul class="page-sidebar-category">
                    {% for category in childrenCategories %}
                        {% if manufacturer %}
                            {% set href = seoUrl('frontend.category.manufacturer.page', { navigationId: category.id, manufacturerId: manufacturer.id }) %}
                        {% else %}
                            {% set href = seoUrl('frontend.navigation.page', { navigationId: category.id }) %}
                        {% endif %}

                        <li>
                            <a href="{{ href }}"
                               title="{{ category.translated.name }}">
                                {% if category.media %}
                                    {% sw_include '@Ersatzteilshop/storefront/utilities/category-icon.html.twig' with {
                                        'category': category
                                    } %}
                                {% else %}
                                    <img src="{{ asset('bundles/ersatzteilshop/assets/img/no_icon_cat.svg') }}"
                                         alt="{{ category.translated.name }}">
                                {% endif %}
                                <span>{{ category.translated.name }}</span>
                            </a>
                        </li>
                    {% endfor %}
                </ul>
            {% endif %}
        </div>
    </div>

    {# miscs #}
    {% sw_include '@Storefront/storefront/page/category/product/brands.html.twig' %}
    {% sw_include '@Storefront/storefront/page/category/allego.html.twig' %}
</div>
