{% block component_blog_box %}
    {% if blog %}
        {% set id = blog.id %}
        {% set name = blog.translated.customFields.title %}
        {% set coverMediaId = blog.translated.customFields.cover_image %}
        {% set blogCategories = blog.extensions.categories|join(',') %}
        <div class="k11-pb-40 cms-listing-col {{ listingColumns }}" data-attribute-blog-category="{{ blogCategories }}">
            <div class="card product-box box-standard">
                {% block component_blog_box_content %}
                    <div>
                        {% block component_blog_box_image %}
                            <div class="blog-image-wrapper">
                                {% set displayMode = 'cover' %}

                                <a href="{{ seoUrl('frontend.navigation.page', {'navigationId': id}) }}"
                                   title="{{ name }}"
                                   class="product-image-link is-{{ displayMode }}">
                                    {% if coverMediaId %}
                                        {% set attributes = {
                                            'class': 'product-image is-'~displayMode,
                                            'alt': name|striptags,
                                            'title': name|striptags
                                        } %}

                                        {% if displayMode == 'cover' or displayMode == 'contain' %}
                                            {% set attributes = attributes|merge({ 'data-object-fit': displayMode }) %}
                                        {% endif %}

                                        {% sw_thumbnails 'product-image-thumbnails' with {
                                            media: coverMedias.get(coverMediaId),
                                            sizes: {
                                                'xs': '501px',
                                                'sm': '315px',
                                                'md': '427px',
                                                'lg': '333px',
                                                'xl': '333px'
                                            }
                                        } %}
                                    {% else %}
                                        <div class="product-image-placeholder">
                                            {% sw_icon 'placeholder' style {
                                                'size': 'fluid'
                                            } %}
                                        </div>
                                    {% endif %}
                                </a>
                            </div>
                        {% endblock %}

                        {% block component_blog_box_info %}
                            <div class="product-info">
                                {% block component_blog_box_name %}
                                    <a href="{{ seoUrl('frontend.navigation.page', {'navigationId': id}) }}"
                                       class="blog-title"
                                       title="{{ name|striptags }}">
                                        {{ name|raw }}
                                    </a>
                                {% endblock %}
                            </div>
                        {% endblock %}
                    </div>
                {% endblock %}
            </div>
        </div>
    {% endif %}
{% endblock %}