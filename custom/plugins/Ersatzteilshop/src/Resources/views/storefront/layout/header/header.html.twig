{% sw_extends '@Storefront/storefront/layout/header/header.html.twig' %}

{% block layout_top_bar %}
{% endblock %}

{% block layout_header_navigation %}
    <div class="header-nav-container">
        <div class="container" style="padding-left: 10px !important; padding-right: 10px !important;">
            <div class="header-row">
                <!-- Logo Block -->
                {% block layout_header_logo %}
                    <div class="header-logo-element">
                        {% sw_include '@Storefront/storefront/layout/header/logo.html.twig' %}
                    </div>
                {% endblock %}

                <!-- Search Block -->
                {% block layout_header_search %}
                    <div class="header-search">
                        {% sw_include '@Storefront/storefront/layout/header/search.html.twig' %}
                    </div>
                {% endblock %}

                <!-- Actions Block -->
                {% block layout_header_actions %}
                    <div class="action-block">
                        <!-- Wishlist -->
                        {% if config('core.cart.wishlistEnabled') %}
                            {% block layout_header_actions_wishlist %}
                                <div>
                                    <a class="btn header-wishlist-btn header-actions-btn"
                                       href="{{ path('frontend.wishlist.page') }}"
                                       title="{{ 'header.wishlist'|trans|striptags }}"
                                       aria-label="{{ 'header.wishlist'|trans|striptags }}">
                                        {% sw_include '@Storefront/storefront/layout/header/actions/wishlist-widget.html.twig' %}
                                    </a>
                                </div>
                            {% endblock %}
                        {% endif %}

                        {% block layout_header_actions_support %}
                            <div class="contact-class d-md-block">
                                <span class="k11-pointer" data-link-masking=true target="_blank" title="{{ 'ersatzteilshop.footer.pickupContactFormLink'|trans|sw_sanitize }}" data-href="{{ 'https://ersatzteilshophelp.zendesk.com/hc/de/requests/new'|maskHref }}">
                                    <span>
                                         <img
                                                 src="{{ asset('bundles/ersatzteilshop/assets/icons/Asset_icon_contact.svg') }}"
                                                 width="30" height="30" alt="Kontaktformular"
                                         >
                                    </span>
                                </span>
                            </div>

                            <div class="d-block d-md-none" style="top: 4px">
                                <span class="k11-pointer" data-link-masking=true target="_blank" title="Whatsapp" data-href="{{ 'https://api.whatsapp.com/send/?phone=4915122801963&text&type=phone_number&app_absent=0'|maskHref }}">
                                      <span>
                                         <img
                                                 src="{{ asset('bundles/ersatzteilshop/assets/icons/Asset_icon_whatsapp.svg') }}"
                                                 width="30" height="30" alt="Whatsapp"
                                         >
                                     </span>
                                </span>
                            </div>
                        {% endblock %}

                        {% block layout_header_actions_cart %}
                            <div data-offcanvas-cart="true">
                                <span class="k11-pointer" title="{{ 'checkout.cartTitle'|trans|striptags }}" href="{{ path('frontend.checkout.cart.page') }}">
                                      <span>
                                         <img
                                                 src="{{ asset('bundles/ersatzteilshop/assets/icons/Asset_icon_cart.svg') }}"
                                                 width="30" height="30" alt="{{ 'checkout.cartTitle'|trans|striptags }}"
                                         >
                                      </span>
                                </span>
                            </div>
                        {% endblock %}

                        {% block layout_header_actions_account %}
                            <div class="dropdown">
                                <a 
                                   href="#"
                                   id="accountMenuDropdown-topNav"
                                   data-toggle="dropdown"
                                   aria-haspopup="true"
                                   title="Profil"
                                   aria-expanded="false">
                                        <span>
                                             <img
                                                     src="{{ asset('bundles/ersatzteilshop/assets/icons/Asset_icon_account.svg') }}"
                                                     width="30" height="30" alt="Profil"
                                             >
                                        </span>
                                </a>
                                {% sw_include '@Storefront/storefront/layout/header/actions/header-login-widget.html.twig' %}
                            </div>
                        {% endblock %}

                        {% block layout_header_navigation_toggle %}
                            <div class="d-block d-md-none">
                                <div class="header-cart" id="mobile-menu-toggle">
                                    {% block layout_header_navigation_toggle_button %}
                                        <div class=""
                                             aria-label="{{ 'general.menuLink'|trans|striptags }}">
                                            {% block layout_header_navigation_toggle_button_icon %}
                                                <span>
                                                         <img
                                                                 src="{{ asset('bundles/ersatzteilshop/assets/icons/Asset_icon_hamburger.svg') }}"
                                                                 width="30" height="30" alt="Hamburger Icon"
                                                         >
                                                    </span>
                                            {% endblock %}
                                        </div>
                                    {% endblock %}
                                </div>
                            </div>
                        {% endblock %}
                    </div>
                {% endblock %}
            </div>
        </div>
        <div class="k11-p-15" style="text-align: center;">
            <a  {% if not config('Ersatzteilshop.config.showAIScanner') %} class="d-none" {% endif%} style="color: white; text-decoration: underline; padding-bottom: 5px;" href="/blog/typennummer-scannen/">
                <svg class="k11-mr-5" width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <mask id="mask0_5659_22278" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="24">
                        <rect x="0.5" width="24" height="24" fill="#D9D9D9"></rect>
                    </mask>
                    <g mask="url(#mask0_5659_22278)">
                        <path d="M19.5 7V5H17.5V3H19.5V1H21.5V3H23.5V5H21.5V7H19.5ZM11.5 17.5C12.75 17.5 13.8125 17.0625 14.6875 16.1875C15.5625 15.3125 16 14.25 16 13C16 11.75 15.5625 10.6875 14.6875 9.8125C13.8125 8.9375 12.75 8.5 11.5 8.5C10.25 8.5 9.1875 8.9375 8.3125 9.8125C7.4375 10.6875 7 11.75 7 13C7 14.25 7.4375 15.3125 8.3125 16.1875C9.1875 17.0625 10.25 17.5 11.5 17.5ZM11.5 15.5C10.8 15.5 10.2083 15.2583 9.725 14.775C9.24167 14.2917 9 13.7 9 13C9 12.3 9.24167 11.7083 9.725 11.225C10.2083 10.7417 10.8 10.5 11.5 10.5C12.2 10.5 12.7917 10.7417 13.275 11.225C13.7583 11.7083 14 12.3 14 13C14 13.7 13.7583 14.2917 13.275 14.775C12.7917 15.2583 12.2 15.5 11.5 15.5ZM3.5 21C2.95 21 2.47917 20.8042 2.0875 20.4125C1.69583 20.0208 1.5 19.55 1.5 19V7C1.5 6.45 1.69583 5.97917 2.0875 5.5875C2.47917 5.19583 2.95 5 3.5 5H6.65L8.5 3H15.5V7H17.5V9H21.5V19C21.5 19.55 21.3042 20.0208 20.9125 20.4125C20.5208 20.8042 20.05 21 19.5 21H3.5Z" fill="#fff"></path>
                    </g>
                </svg>
                <span>
                Typenschild scannen - alle Ersatzteile finden
            </span>
            </a>
        </div>
    </div>
{% endblock %}


