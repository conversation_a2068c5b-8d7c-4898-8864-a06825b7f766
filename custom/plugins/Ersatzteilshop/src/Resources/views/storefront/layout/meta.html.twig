{% sw_extends '@Storefront/storefront/layout/meta.html.twig' %}

{% if page.appliance.exploitedMediaIds %}
    {% set media = searchMedia(page.appliance.exploitedMediaIds, context.context)|first %}
{% endif %}

{% block layout_head_meta_tags %}
    {{ parent() }}
    <meta name="msvalidate.01" content="07111075BC8ADEEAD33A2145A62A7EA2" />
    {% block layout_head_prev_next_links %}{% endblock %}
    {% sw_include '@Storefront/storefront/component/meta.html.twig' %}
{% endblock %}

{% block layout_head_title_inner %}
    {% if parent() == null %}
        {{ 'error.title'|trans|striptags }}
    {% else %}
        {% set title = parent()|u.truncate(70, '', false)  %}
        {% if title|length > 70 %}
            {% set title = title|u.beforeLast(' ')  %}
        {% endif %}

        {{ title|raw }}
    {% endif %}
{% endblock %}

{% block layout_head_stylesheet %}
    {% if isHMRMode %}
        {# CSS will be loaded from the JS automatically #}
    {% else %}
        {% include '@Ersatzteilshop/storefront/layout/styles.html.twig' %}
    {% endif %}
{% endblock %}

{% block layout_head_meta_tags_image_og %}{% if media.url %}{{ media.url }}{% else %}{{ parent() }}{% endif %}{% endblock %}
{% block layout_head_meta_tags_image_twitter %}{% if media.url %}{{ media.url }}{% else %}{{ parent() }}{% endif %}{% endblock %}
{% block layout_head_meta_tags_image_meta %}{% if media.url %}{{ media.url }}{% else %}{{ parent() }}{% endif %}{% endblock %}
