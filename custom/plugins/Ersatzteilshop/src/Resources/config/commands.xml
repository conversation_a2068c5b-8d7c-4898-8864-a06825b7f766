<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>

        <service id="Ersatzteilshop\Command\SetupCommand">
            <argument type="service" id="custom_field_set.repository"/>
            <argument type="service" id="custom_field.repository"/>
            <argument type="service" id="seo_url_template.repository"/>
            <argument type="service" id="sales_channel.repository"/>
            <argument type="service" id="seo_url.repository"/>
            <argument type="service" id="language.repository"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\GenerateMenuDataCommand">
            <argument type="service" id="sales_channel.repository"/>
            <argument type="service" id="category.repository"/>
            <argument type="service" id="theme.repository"/>
            <argument type="service" id="seo_url.repository"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\GenerateCategoryManufacturerCommand">
            <argument type="service" id="Ersatzteilshop\Service\CategoryManufacturerGenerator"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\TestPHPExceptionCommand">
            <argument type="service" id="monolog.logger"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\TestGetCustomerCommand">
            <argument type="service" id="Ersatzteilshop\Service\EcomCustomer"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\DisableProductCommand">
            <argument type="service" id="product.repository"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\FixSeoUrlCommand">
            <argument type="service" id="monolog.logger"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\FixSeoPathInfoCommand">
            <argument type="service" id="monolog.logger"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\ExportMissingImageCommand">
            <argument type="service" id="media.repository"/>
            <argument type="service" id="media_folder.repository"/>
            <argument type="service" id="Shopware\Core\Content\Media\Pathname\UrlGeneratorInterface"/>
            <argument type="service" id="shopware.filesystem.public"/>
            <argument type="service" id="shopware.filesystem.private"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\ReminderProductStockCommand">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="mail_template.repository"/>
            <argument type="service" id="mail_template_type.repository"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\UpdateProductSoldCommand">
            <argument type="service" id="Ersatzteilshop\Service\ProductSoldUpdater" />
            <argument type="service" id="product.repository"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\UpdateFeaturedProductCommand">
            <argument type="service" id="Ersatzteilshop\Service\Tracker\ProductFeatureUpdater"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\UpdateApplianceRelevanceCommand">
            <argument type="service" id="Ersatzteilshop\Service\Tracker\ApplianceRelevanceUpdater"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\UpdateCategoryRelevanceCommand">
            <argument type="service" id="Ersatzteilshop\Service\Tracker\CategoryRelevanceUpdater"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\SitemapUrlCountCommand">
            <argument type="service" id="Shopware\Core\Framework\Event\BusinessEventDispatcher"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\SitemapUrlListCommand">
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\Cache\ObjectCacheIndexCommand">
            <argument type="service" id="Elasticsearch\Client"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="Ersatzteilshop\Decorator\Elasticsearch\Framework\ElasticsearchHelper"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\UpdateProductAlwaysInStockCommand">
            <argument type="service" id="product.repository"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\UpdateReviewTitleCommand">
            <argument type="service" id="product_review.repository"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\SendRestockReminderEmailCommand">
            <argument id="Pluszwei\BackInStockReminder\MessageQueue\Handler\SendRemindHanlder" type="service"/>
            <argument id="Pluszwei\BackInStockReminder\Service\ReminderLoader" type="service"/>
            <argument id="Pluszwei\BackInStockReminder\Service\RemindService" type="service"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\SendMockMessages">
            <argument id="messenger.default_bus" type="service"/>

            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\UpdateProductCategoryTreeCommand">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service"
                      id="Shopware\Core\Content\Product\DataAbstractionLayer\ProductCategoryDenormalizer"/>
            <argument type="service" id="Shopware\Core\Framework\Adapter\Cache\CacheClearer"/>

            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\RegenerateThumbnailsCommand">
            <argument type="service" id="media.repository"/>
            <argument type="service" id="media_folder.repository"/>
            <argument type="service" id="shopware.filesystem.public"/>
            <argument type="service" id="shopware.filesystem.private"/>
            <argument type="service" id="Shopware\Core\Content\Media\Pathname\UrlGeneratorInterface"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>

            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\IndexEntityCommand">
            <argument type="tagged" tag="shopware.entity_indexer"/>
            <argument type="service" id="Shopware\Core\Framework\DataAbstractionLayer\Indexing\EntityIndexerRegistry"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\Cache\ApplianceProductIdCachingRefreshCommand">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="Ersatzteilshop\MessageQueue\Handler\ApplianceProductIdCachingHandler"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\FixMediaPositionCommand">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\CountAswoNumbersCommand">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\ClearProductHistoryStatusCommand">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\RemoveCustomFieldCommand">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\Cache\MenuCachingRefreshCommand">
            <argument type="service" id="sales_channel.repository"/>
            <argument type="service" id="Shopware\Core\System\SalesChannel\Context\SalesChannelContextService"/>
            <argument type="service" id="Shopware\Storefront\Theme\ThemeService"/>
            <argument type="service" id="Ersatzteilshop\Service\CachedMenuService"/>

            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\UpdateCategoryMappingCommand">
            <argument type="service" id="Doctrine\DBAL\Connection"/>

            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\Cache\CategoryJourneyRefreshCommand">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="category.repository"/>
            <argument type="service" id="category_journey.repository"/>
            <argument type="service" id="router"/>
            <argument type="service" id="twig"/>
            <argument type="service" id="Shopware\Core\Framework\Adapter\Translation\Translator"/>
            <argument type="service" id="Shopware\Core\System\Locale\LanguageLocaleCodeProvider"/>
            <argument type="service" id="event_dispatcher"/>

            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\StatisticRefreshCommand">
            <argument type="service" id="Ersatzteilshop\Service\Statistic\TurnoverRefresher"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\GenerateAiSeoDescriptionCategoryCommand">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="Ersatzteilshop\Service\ChatGPT4Client"/>
            <argument type="service" id="ai_category_seo.repository"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\GenerateAiSeoDescriptionCategoryManufacturerCommand">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="Ersatzteilshop\Service\ChatGPT4Client"/>
            <argument type="service" id="ai_category_seo.repository"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\UpdateCategoryDescriptionsCommand">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="ai_category_seo.repository"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\UpdateSeoUrlApplianceCategoryManufacturerCommand">
            <argument type="service" id="seo_url_template.repository"/>
            <argument type="service" id="sales_channel.repository"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\Appliance\SalesChannel\ApplianceManufacturerCategoryRoute"/>
            <argument type="service" id="Shopware\Core\System\SalesChannel\Context\SalesChannelContextFactory"/>
            <argument type="service" id="Shopware\Core\Content\Seo\SeoUrlRoute\SeoUrlRouteRegistry"/>
            <argument type="service" id="Shopware\Core\Content\Seo\SeoUrlGenerator"/>
            <argument type="service" id="Shopware\Core\Content\Seo\SeoUrlPersister"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\FetchProductInfo\FetchProductInfoCommand">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\DuplicateVideoMappingCommand">
            <argument type="service" id="resource_rule.repository"/>
            <argument type="service" id="video_library.repository"/>
            <argument type="service" id="ers_video.repository"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\AnalyticsFailureRateCheckCommand">
            <argument type="service" id="ersatzteilshop_image_search_analytics.repository"/>
            <tag name="console.command" />
        </service>

        <service id="Ersatzteilshop\Command\UpdateVideoLibraryYoutubeMetadataCommand">
            <argument type="service" id="video_library.repository"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="Ersatzteilshop\Service\ChatGPT4Client"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\UpdateVideoLibraryFaqMetadataCommand">
            <argument type="service" id="video_library.repository"/>
            <argument type="service" id="Ersatzteilshop\Service\ChatGPT4Client"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Command\FixCategoryLinksCommand">
            <argument type="service" id="category.repository"/>
            <argument type="service" id="category_translation.repository"/>
            <argument type="service" id="category_manufacturer_translation.repository"/>
            <argument type="service" id="seo_url.repository"/>

            <tag name="console.command"/>
        </service>

    </services>

</container>
