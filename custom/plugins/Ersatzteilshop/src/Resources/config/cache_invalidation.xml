<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>

        <service id="Ersatzteilshop\Subscriber\Cache\ManufacturerDetailCacheInvalidationSubscriber">
            <argument type="service" id="Shopware\Core\Framework\Adapter\Cache\CacheInvalidator"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Cache\ManufacturerOverviewCacheInvalidationSubscriber">
            <argument type="service" id="Shopware\Core\Framework\Adapter\Cache\CacheInvalidator"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Cache\CategoryCacheInvalidationSubscriber">
            <argument type="service" id="Shopware\Core\Framework\Adapter\Cache\CacheInvalidator"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Cache\ApplianceDetailCacheInvalidationSubscriber">
            <argument type="service" id="Shopware\Core\Framework\Adapter\Cache\CacheInvalidator"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Cache\ApplianceProductCacheInvalidationSubscriber">
            <argument type="service" id="Shopware\Core\Framework\Adapter\Cache\CacheInvalidator"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Cache\ApplianceSearchCacheInvalidationSubscriber">
            <argument type="service" id="Shopware\Core\Framework\Adapter\Cache\CacheInvalidator"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Cache\RepairGuideDetailCacheInvalidationSubscriber">
            <argument id="Shopware\Core\Framework\Adapter\Cache\CacheInvalidator" type="service"/>
            <argument id="Doctrine\DBAL\Connection" type="service"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Cache\RepairInstructionDetailCacheInvalidationSubscriber">
            <argument id="Shopware\Core\Framework\Adapter\Cache\CacheInvalidator" type="service"/>
            <argument id="Doctrine\DBAL\Connection" type="service"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Cache\ProductListCacheInvalidationSubscriber">
            <argument type="service" id="Shopware\Core\Framework\Adapter\Cache\CacheInvalidator"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Cache\ProductDetailCacheInvalidateSubscriber">
            <argument type="service" id="Shopware\Core\Framework\Adapter\Cache\CacheInvalidator"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Cache\ResourceRuleCacheInvalidationSubscriber">
            <argument type="service" id="Shopware\Core\Framework\Adapter\Cache\CacheInvalidator"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Cache\ApplianceGroupCacheInvalidationSubscriber">
            <argument type="service" id="Shopware\Core\Framework\Adapter\Cache\CacheInvalidator"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Cache\CategoryRouteCacheKeySubscriber">
            <argument type="service" id="request_stack"/>
            <tag name="kernel.event_subscriber"/>
        </service>
    </services>
</container>
