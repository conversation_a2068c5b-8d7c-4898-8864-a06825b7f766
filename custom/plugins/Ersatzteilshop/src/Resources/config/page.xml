<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <service id="Ersatzteilshop\Core\Contract\Indexer\AbstractIndexer" abstract="true">
            <argument type="service" id="Shopware\Core\Framework\DataAbstractionLayer\Dbal\Common\IteratorFactory"/>
            <argument type="service" id="Shopware\Core\Framework\DataAbstractionLayer\Indexing\InheritanceUpdater"/>
            <argument type="service" id="event_dispatcher"/>
        </service>

        <service id="Ersatzteilshop\Core\Contract\SeoUrlRoute\TranslationSeoUrlRoute" abstract="true">
            <argument type="service" id="Ersatzteilshop\Service\LanguageService"/>
        </service>

        <!--Category page-->
        <service id="Ersatzteilshop\Storefront\Controller\CategoryController" public="true">
            <argument type="service" id="Ersatzteilshop\Storefront\Page\Category\CategoryPageLoader"/>
            <argument type="service" id="Ersatzteilshop\Service\ManufacturerLoader"/>
            <argument type="service" id="Shopware\Core\Content\Seo\SeoUrlPlaceholderHandlerInterface"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Storefront\Page\Category\CategoryPageLoader">
            <argument type="service" id="Shopware\Storefront\Page\GenericPageLoader"/>
            <argument type="service" id="event_dispatcher"/>
            <argument type="service" id="Shopware\Core\Content\Category\SalesChannel\CategoryRoute"/>
            <argument type="service" id="category_manufacturer.repository"/>
            <argument type="service" id="seo_url.repository"/>
            <argument type="service" id="translator"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\ProductManufacturer\ProductManufacturerRoute"/>
            <argument type="service" id="Shopware\Core\Content\Seo\SeoUrlPlaceholderHandlerInterface"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\Category\CategoryPageSeoUrlRoute" public="true"
                 decorates="Shopware\Storefront\Framework\Seo\SeoUrlRoute\NavigationPageSeoUrlRoute">
            <argument type="service" id="Shopware\Core\Content\Category\CategoryDefinition"/>
            <argument type="service" id="Shopware\Core\Content\Category\Service\CategoryBreadcrumbBuilder"/>
        </service>

        <!--Manufacturer page-->
        <service id="Ersatzteilshop\Storefront\Controller\ManufacturerController" public="true">
            <argument type="service" id="Ersatzteilshop\Storefront\Page\Manufacturer\ManufacturerOverviewPageLoader"/>
            <argument type="service" id="Ersatzteilshop\Storefront\Page\Manufacturer\ManufacturerPageLoader"/>
            <argument type="service" id="Ersatzteilshop\Storefront\Page\Category\CategoryPageLoader"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Core\Content\ProductManufacturer\ProductManufacturerIndexer" parent="Ersatzteilshop\Core\Contract\Indexer\AbstractIndexer">
            <call method="setRepository">
                <argument type="service" id="product_manufacturer.repository"/>
            </call>
            <tag name="shopware.entity_indexer" priority="99"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ProductManufacturer\ProductManufacturerPageSeoUrlRoute"
                 public="true"
                 parent="Ersatzteilshop\Core\Contract\SeoUrlRoute\TranslationSeoUrlRoute">
            <argument type="service" id="Shopware\Core\Content\Product\Aggregate\ProductManufacturer\ProductManufacturerDefinition"/>
            <argument type="service" id="product_manufacturer.repository"/>

            <tag name="shopware.seo_url.route"/>
        </service>

        <service id="Ersatzteilshop\Storefront\Page\Manufacturer\ManufacturerPageLoader" public="true">
            <argument type="service" id="Shopware\Storefront\Page\GenericPageLoader"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\ProductManufacturer\ProductManufacturerRoute"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\Appliance\SalesChannel\ApplianceManufacturerCategoryRoute"/>
            <argument type="service" id="Ersatzteilshop\Service\ProductLoader"/>
            <argument type="service" id="Ersatzteilshop\Service\ResourceRuleLoader"/>
            <argument type="service" id="event_dispatcher"/>
            <argument id="Shopware\Core\Content\Cms\SalesChannel\SalesChannelCmsPageLoader" type="service"/>
        </service>

        <service id="Ersatzteilshop\Storefront\Page\Manufacturer\ManufacturerOverviewPageLoader" public="true">
            <argument type="service" id="Shopware\Storefront\Page\GenericPageLoader"/>
            <argument type="service"
                      id="Ersatzteilshop\Core\Content\Category\CategoryRoute"/>
            <argument type="service"
                      id="Ersatzteilshop\Core\Content\ProductManufacturer\ProductManufacturerOverviewRoute"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\Appliance\SalesChannel\ApplianceManufacturerCategoryRoute"/>
        </service>

        <!--Listing ajax page-->
        <service id="Ersatzteilshop\Storefront\Controller\ListingController" public="true">
            <argument type="service" id="Shopware\Core\Content\Product\SalesChannel\Listing\ProductListingRoute"/>
            <argument type="service" id="Ersatzteilshop\Service\ProductReviewLoader"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="XantenAswo\Service\Aswo"/>
            <argument type="service" id="Twig\Environment"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\TypeNumberController" public="true">
            <argument type="service" id="Ersatzteilshop\Service\CategoryLoader"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <!--CategoryManufacturer-->

        <service id="Ersatzteilshop\Core\Content\CategoryManufacturer\DataAbstractionLayer\CategoryManufacturerPageSeoUrlRoute" public="true">
            <argument type="service" id="Ersatzteilshop\Core\Content\CategoryManufacturer\CategoryManufacturerDefinition"/>
            <argument type="service" id="Shopware\Core\Content\Category\Service\CategoryBreadcrumbBuilder"/>

            <tag name="shopware.seo_url.route"/>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\ApiController" public="true">
            <argument type="service" id="Ersatzteilshop\Service\CategoryManufacturerGenerator"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\ObjectCache\SalesChannel\ObjectCacheRoute"/>
            <argument type="service" id="sales_channel.repository"/>
            <argument type="service" id="Shopware\Core\System\SalesChannel\Context\SalesChannelContextService"/>
            <argument type="service" id="Ersatzteilshop\Service\Statistic\TurnoverRefresher"/>

            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\ApiCategoryBlogController" public="true">
            <argument type="service" id="category.repository" />
            <argument type="service" id="Shopware\Core\Framework\DataAbstractionLayer\Search\RequestCriteriaBuilder" />
            <argument type="service" id="Shopware\Core\Framework\Api\Response\Type\Api\JsonApiType" />
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Core\Content\CategoryManufacturer\DataAbstractionLayer\CategoryManufacturerIndexer"
                 parent="Ersatzteilshop\Core\Contract\Indexer\AbstractIndexer">
            <call method="setRepository">
                <argument type="service" id="category_manufacturer.repository"/>
            </call>
            <tag name="shopware.entity_indexer" priority="100"/>
        </service>

        <!--Repair guide-->
        <service id="Ersatzteilshop\Storefront\Controller\RepairGuideController" public="true">
            <argument type="service" id="Ersatzteilshop\Storefront\Page\RepairGuide\RepairGuidePageLoader"/>
            <argument type="service" id="sales_channel.product.repository"/>
            <argument type="service" id="Ersatzteilshop\Service\ProductReviewLoader"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Core\Content\RepairGuide\DataAbstractionLayer\RepairGuideIndexer" parent="Ersatzteilshop\Core\Contract\Indexer\AbstractIndexer">
            <call method="setRepository">
                <argument type="service" id="repair_guide.repository"/>
            </call>
            <tag name="shopware.entity_indexer" priority="100"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\RepairGuide\DataAbstractionLayer\RepairGuidePageSeoUrlRoute" public="true">
            <argument type="service" id="Ersatzteilshop\Core\Content\RepairGuide\RepairGuideDefinition"/>
            <tag name="shopware.seo_url.route"/>
        </service>

        <service id="Ersatzteilshop\Storefront\Page\RepairGuide\RepairGuidePageLoader" public="true">
            <argument type="service" id="Shopware\Storefront\Page\GenericPageLoader"/>
            <argument type="service" id="Ersatzteilshop\Service\RepairGuideLoader"/>
            <argument type="service" id="Ersatzteilshop\Service\ResourceRuleLoader"/>
            <argument type="service" id="category.repository"/>
            <argument type="service" id="media.repository"/>
            <argument type="service" id="event_dispatcher"/>
        </service>

        <!--Appliance-->
        <service id="Ersatzteilshop\Storefront\Controller\ApplianceController" public="true">
            <argument type="service" id="Ersatzteilshop\Storefront\Page\Appliance\AppliancePageLoader"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\Appliance\SalesChannel\ApplianceProductRoute"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\Appliance\SalesChannel\ApplianceRoute"/>
            <argument type="service" id="Ersatzteilshop\Service\VisitedApplianceService"/>
            <argument type="service" id="Ersatzteilshop\Service\ImageSearch\ApplianceAnalyzer"/>
            <argument type="service" id="Ersatzteilshop\Service\ImageSearch\ImageSearchAnalytics"/>
            <argument type="service" id="Ersatzteilshop\Service\Appliance\ApplianceAggregatorInterface"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\ApplianceManufacturerCategoryController" public="true">
            <argument type="service" id="Ersatzteilshop\Storefront\Page\ApplianceManufacturerCategory\ApplianceManufacturerCategoryPageLoader"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Core\Content\Appliance\DataAbstractionLayer\AppliancePageSeoUrlRoute"
                 public="true"
                 parent="Ersatzteilshop\Core\Contract\SeoUrlRoute\TranslationSeoUrlRoute">
            <call method="setDefinition">
                <argument type="service" id="Ersatzteilshop\Core\Content\Appliance\ApplianceDefinition"/>
            </call>
            <tag name="shopware.seo_url.route"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\Appliance\DataAbstractionLayer\ApplianceCategoryPageSeoUrlRoute"
                 public="true"
                 parent="Ersatzteilshop\Core\Contract\SeoUrlRoute\TranslationSeoUrlRoute">
            <call method="setDefinition">
                <argument type="service" id="Shopware\Core\Content\Category\CategoryDefinition"/>
            </call>
            <tag name="shopware.seo_url.route"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\Appliance\DataAbstractionLayer\ApplianceCategoryManufacturerPageSeoUrlRoute"
                 public="true"
                 parent="Ersatzteilshop\Core\Contract\SeoUrlRoute\TranslationSeoUrlRoute">
            <call method="setDefinition">
                <argument type="service" id="Ersatzteilshop\Core\Content\CategoryManufacturer\CategoryManufacturerDefinition"/>
            </call>
            <call method="setRoute">
                <argument type="service" id="Ersatzteilshop\Core\Content\Appliance\SalesChannel\ApplianceManufacturerCategoryRoute"/>
            </call>
            <tag name="shopware.seo_url.route"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\Appliance\DataAbstractionLayer\ApplianceIndexer" parent="Ersatzteilshop\Core\Contract\Indexer\AbstractIndexer">
            <call method="setRepository">
                <argument type="service" id="appliance.repository"/>
            </call>
            <tag name="shopware.entity_indexer" priority="100"/>
        </service>

        <service id="Ersatzteilshop\Storefront\Page\Appliance\AppliancePageLoader" public="true">
            <argument type="service" id="Shopware\Storefront\Page\GenericPageLoader"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\Appliance\SalesChannel\ApplianceRoute"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\Appliance\SalesChannel\ApplianceProductRoute"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\Appliance\SalesChannel\ApplianceResourceRoute"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\ApplianceGroup\SalesChannel\ApplianceGroupApplianceRoute"/>
            <argument type="service" id="Ersatzteilshop\Service\CategoryLoader"/>
            <argument type="service" id="Ersatzteilshop\Service\ProductATLoader"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="Shopware\Core\Content\Seo\SeoUrlPlaceholderHandlerInterface"/>
            <argument type="service" id="event_dispatcher"/>
        </service>

        <service id="Ersatzteilshop\Storefront\Page\ApplianceManufacturerCategory\ApplianceManufacturerCategoryPageLoader">
            <argument type="service" id="Shopware\Storefront\Page\GenericPageLoader"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\Appliance\SalesChannel\ApplianceManufacturerCategoryRoute"/>
            <argument type="service" id="event_dispatcher"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\ErrorGroupController" public="true">
            <argument type="service" id="Ersatzteilshop\Storefront\Page\ErrorGroup\ErrorGroupPageLoader"/>
            <argument type="service" id="Ersatzteilshop\Storefront\Page\ErrorCode\ErrorCodePageLoader"/>
            <argument type="service" id="error_code.repository"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Core\Content\ErrorGroup\DataAbstractionLayer\ErrorGroupPageSeoUrlRoute"
                 public="true"
                 parent="Ersatzteilshop\Core\Contract\SeoUrlRoute\TranslationSeoUrlRoute">
            <call method="setDefinition">
                <argument type="service" id="Ersatzteilshop\Core\Content\ErrorGroup\ErrorGroupDefinition"/>
            </call>
            <tag name="shopware.seo_url.route"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ErrorGroup\DataAbstractionLayer\ErrorCodePageSeoUrlRoute"
                 public="true"
                 parent="Ersatzteilshop\Core\Contract\SeoUrlRoute\TranslationSeoUrlRoute">
            <call method="setDefinition">
                <argument type="service"
                          id="Ersatzteilshop\Core\Content\ErrorGroup\Aggregate\ErrorCode\ErrorCodeDefinition"/>
            </call>
            <tag name="shopware.seo_url.route"/>
        </service>

        <service id="Ersatzteilshop\Storefront\Page\ErrorGroup\ErrorGroupPageLoader" public="true">
            <argument type="service" id="Shopware\Storefront\Page\GenericPageLoader"/>
            <argument type="service" id="Ersatzteilshop\Service\ErrorGroupLoader"/>
            <argument type="service" id="event_dispatcher"/>
        </service>

        <service id="Ersatzteilshop\Storefront\Page\ErrorCode\ErrorCodePageLoader" public="true">
            <argument type="service" id="Shopware\Storefront\Page\GenericPageLoader"/>
            <argument type="service" id="Ersatzteilshop\Service\ErrorGroupLoader"/>
            <argument type="service" id="Ersatzteilshop\Service\ErrorCodeLoader"/>
            <argument type="service" id="Ersatzteilshop\Service\ResourceRuleLoader"/>
            <argument type="service" id="product.repository"/>
            <argument type="service" id="Ersatzteilshop\Service\ProductReviewLoader"/>
            <argument type="service" id="event_dispatcher"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ErrorGroup\DataAbstractionLayer\ErrorGroupIndexer"
                 parent="Ersatzteilshop\Core\Contract\Indexer\AbstractIndexer">
            <call method="setRepository">
                <argument type="service" id="error_group.repository"/>
            </call>
            <tag name="shopware.entity_indexer" priority="100"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ErrorGroup\DataAbstractionLayer\ErrorCodeIndexer"
                 parent="Ersatzteilshop\Core\Contract\Indexer\AbstractIndexer">
            <call method="setRepository">
                <argument type="service" id="error_code.repository"/>
            </call>
            <tag name="shopware.entity_indexer" priority="100"/>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\AuthController" public="true">
            <argument type="service" id="Shopware\Storefront\Page\Account\Login\AccountLoginPageLoader"/>
            <argument type="service" id="Shopware\Core\Checkout\Customer\SalesChannel\LoginRoute"/>
            <argument type="service" id="Shopware\Core\Checkout\Customer\SalesChannel\SendPasswordRecoveryMailRoute"/>
            <argument type="service" id="Shopware\Core\Checkout\Customer\SalesChannel\ResetPasswordRoute"/>
            <argument type="service" id="Ersatzteilshop\Service\CustomerMigrateRoute"/>
            <argument type="service" id="translator"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <!--Repair instruction-->
        <service id="Ersatzteilshop\Storefront\Page\RepairInstruction\RepairInstructionPageLoader" public="true">
            <argument type="service" id="Shopware\Storefront\Page\GenericPageLoader"/>
            <argument type="service" id="Ersatzteilshop\Service\RepairInstructionLoader"/>
            <argument type="service" id="category.repository"/>
            <argument type="service" id="product.repository"/>
            <argument type="service" id="appliance.repository"/>
            <argument type="service" id="Ersatzteilshop\Service\ProductReviewLoader"/>
            <argument type="service" id="event_dispatcher"/>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\RepairInstructionController" public="true">
            <argument type="service" id="Ersatzteilshop\Storefront\Page\RepairInstruction\RepairInstructionPageLoader"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Core\Content\RepairInstruction\DataAbstractionLayer\RepairInstructionIndexer" parent="Ersatzteilshop\Core\Contract\Indexer\AbstractIndexer">
            <call method="setRepository">
                <argument type="service" id="repair_instruction.repository"/>
            </call>
            <tag name="shopware.entity_indexer" priority="110"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\RepairInstruction\DataAbstractionLayer\RepairInstructionPageSeoUrlRoute"
                 public="true"
                 parent="Ersatzteilshop\Core\Contract\SeoUrlRoute\TranslationSeoUrlRoute">
            <call method="setDefinition">
                <argument type="service" id="Ersatzteilshop\Core\Content\RepairInstruction\RepairInstructionDefinition"/>
            </call>
            <tag name="shopware.seo_url.route"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\Video\DataAbstractionLayer\VideoIndexer" parent="Ersatzteilshop\Core\Contract\Indexer\AbstractIndexer">
            <call method="setRepository">
                <argument type="service" id="ers_video.repository"/>
            </call>
            <tag name="shopware.entity_indexer" priority="110"/>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\RegisterController" public="true">
            <argument type="service" id="Shopware\Core\Checkout\Customer\SalesChannel\RegisterRoute"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="Ersatzteilshop\Service\CustomerMigrateRoute"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\NavigationController" public="true">
            <argument type="service" id="Shopware\Storefront\Page\Navigation\NavigationPageLoader"/>
            <argument type="service" id="translator"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\AddressController" public="true">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\CheckoutController" public="true">
            <argument type="service" id="Shopware\Core\Checkout\Cart\SalesChannel\CartService"/>
            <argument type="service" id="Shopware\Storefront\Page\Checkout\Confirm\CheckoutConfirmPageLoader"/>
            <argument type="service" id="Shopware\Storefront\Page\Checkout\Cart\CheckoutCartPageLoader"/>
            <argument type="service" id="session"/>
            <argument type="service" id="Shopware\Core\Checkout\Cart\LineItemFactoryRegistry"/>
            <argument type="service" id="Shopware\Storefront\Page\Checkout\Offcanvas\OffcanvasCartPageLoader"/>
            <argument type="service" id="product.repository"/>
            <argument type="service" id="Ersatzteilshop\Service\CartService"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>


        <service id="Ersatzteilshop\Storefront\Page\RepairInstruction\CustomerRepairInstructionPageLoader" public="true">
            <argument type="service" id="Shopware\Storefront\Page\GenericPageLoader"/>
            <argument type="service" id="category.repository"/>
        </service>
        <service id="Ersatzteilshop\Storefront\Validation\RepairInstructionFormValidationFactory" public="true">
        </service>
        <service id="Ersatzteilshop\Storefront\Validation\RepairInstructionFormStep2ValidationFactory" public="true">
        </service>
        <service id="Ersatzteilshop\Storefront\Validation\RepairInstructionStepFormValidationFactory" public="true">
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\CustomerRepairInstructionController" public="true">
            <argument type="service" id="Ersatzteilshop\Storefront\Page\RepairInstruction\CustomerRepairInstructionPageLoader"/>
            <argument type="service" id="Ersatzteilshop\Storefront\Validation\RepairInstructionFormValidationFactory"/>
            <argument type="service" id="Ersatzteilshop\Storefront\Validation\RepairInstructionFormStep2ValidationFactory"/>
            <argument type="service" id="Shopware\Core\Framework\Validation\DataValidator"/>
            <argument type="service" id="Ersatzteilshop\Service\CustomerRepairInstructionService"/>
            <argument type="service" id="Ersatzteilshop\Service\CustomerMediaService"/>
            <argument type="service" id="Ersatzteilshop\Service\CustomerRepairInstructionStepService"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\CustomerRepairInstructionStepController" public="true">
            <argument type="service" id="Shopware\Core\Framework\Validation\DataValidator"/>
            <argument type="service" id="Ersatzteilshop\Service\CustomerRepairInstructionStepService"/>
            <argument type="service" id="Ersatzteilshop\Service\CustomerMediaService"/>
            <argument type="service" id="Ersatzteilshop\Service\CustomerRepairInstructionService"/>
            <argument type="service" id="Ersatzteilshop\Storefront\Validation\RepairInstructionStepFormValidationFactory"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\ProductPriceController" public="true">
            <argument type="service" id="Ersatzteilshop\Storefront\Page\Product\PriceLoader"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Storefront\Page\Product\PriceLoader">
            <argument type="service" id="sales_channel.product.repository"/>
            <argument type="service" id="twig"/>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\ProductDeliveryController" public="true">
            <argument type="service" id="Ersatzteilshop\Storefront\Page\Product\DeliveryLoader"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Storefront\Page\Product\DeliveryLoader">
            <argument type="service" id="sales_channel.product.repository"/>
            <argument type="service" id="twig"/>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\ReviewPageController" public="true">
            <argument type="service" id="Shopware\Storefront\Page\GenericPageLoader"/>
            <argument type="service" id="Ersatzteilshop\Service\ShopReviewLoader"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\ShopReviewController" public="true">
            <argument type="service" id="Ersatzteilshop\Service\ShopReviewLoader"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <!--ApplianceGroup -->
        <service id="Ersatzteilshop\Storefront\Controller\ApplianceGroupController" public="true">
            <argument type="service" id="Ersatzteilshop\Storefront\Page\ApplianceGroup\ApplianceGroupPageLoader"/>
            <argument type="service" id="sales_channel.seo_url.repository"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Storefront\Page\ApplianceGroup\ApplianceGroupPageLoader" public="true">
            <argument type="service" id="Shopware\Storefront\Page\GenericPageLoader"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\ApplianceGroup\SalesChannel\ApplianceGroupRoute"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\ApplianceGroup\SalesChannel\ApplianceGroupApplianceRoute"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\ApplianceGroup\SalesChannel\ApplianceGroupResourceRoute"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\Appliance\SalesChannel\ApplianceManufacturerCategoryRoute"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\Appliance\SalesChannel\ApplianceRoute"/>
            <argument type="service" id="translator"/>
            <argument type="service" id="event_dispatcher"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ApplianceGroup\DataAbstractionLayer\ApplianceGroupIndexer"
                 parent="Ersatzteilshop\Core\Contract\Indexer\AbstractIndexer">
            <call method="setRepository">
                <argument type="service" id="appliance_group.repository"/>
            </call>
            <tag name="shopware.entity_indexer" priority="100"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ApplianceGroup\DataAbstractionLayer\ApplianceGroupPageSeoUrlRoute"
                 public="true"
                 parent="Ersatzteilshop\Core\Contract\SeoUrlRoute\TranslationSeoUrlRoute">
            <call method="setDefinition">
                <argument type="service" id="Ersatzteilshop\Core\Content\ApplianceGroup\ApplianceGroupDefinition"/>
            </call>
            <tag name="shopware.seo_url.route"/>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\ProductQuickViewController" public="true">
            <argument type="service" id="Shopware\Storefront\Page\Product\QuickView\MinimalQuickViewPageLoader"/>
            <argument type="service" id="Ersatzteilshop\Service\ProductReviewLoader"/>
            <argument type="service" id="Shopware\Core\Content\Seo\SeoUrlPlaceholderHandlerInterface"/>
            <argument type="service" id="Ersatzteilshop\Service\SchemaOrgService"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Storefront\Page\VideoLibrary\VideoLibraryPageLoader">
            <argument type="service" id="Shopware\Storefront\Page\GenericPageLoader"/>
            <argument type="service" id="video_library.repository"/>
            <argument type="service" id="Symfony\Component\EventDispatcher\EventDispatcherInterface"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\VideoLibrary\SalesChannel\VideoLibraryRoute"/>
            <argument id="Shopware\Core\Content\Cms\SalesChannel\SalesChannelCmsPageLoader" type="service"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\VideoLibrary\DataAbstractionLayer\VideoLibraryIndexer"
                 parent="Ersatzteilshop\Core\Contract\Indexer\AbstractIndexer">
            <call method="setRepository">
                <argument type="service" id="video_library.repository"/>
            </call>
            <tag name="shopware.entity_indexer" priority="100"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\VideoLibrary\DataAbstractionLayer\VideoLibraryPageSeoUrlRoute" public="true">
            <argument type="service" id="Ersatzteilshop\Core\Content\VideoLibrary\VideoLibraryDefinition"/>
            <tag name="shopware.seo_url.route"/>
        </service>
    </services>

</container>
