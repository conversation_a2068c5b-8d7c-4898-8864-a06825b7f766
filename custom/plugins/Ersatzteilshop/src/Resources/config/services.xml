<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <imports>
        <import resource="subscriber.xml"/>
        <import resource="page.xml"/>
        <import resource="cms.xml"/>
        <import resource="commands.xml"/>
        <import resource="definition.xml"/>
        <import resource="decorators.xml"/>
        <import resource="messengers.xml"/>
        <import resource="seo.xml"/>
        <import resource="controllers.xml"/>
        <import resource="scheduled_task.xml"/>
        <import resource="routes_manufacturer.xml"/>
        <import resource="routes_category.xml"/>
        <import resource="routes_appliance.xml"/>
        <import resource="routes_repair_guides.xml"/>
        <import resource="routes_repair_instruction.xml"/>
        <import resource="routes_resource_rule.xml"/>
        <import resource="cache_invalidation.xml"/>
        <import resource="routes_appliance_group.xml"/>
        <import resource="routes_object_cache.xml"/>
        <import resource="seo_generators.xml"/>
        <import resource="statistic.xml"/>
        <import resource="routes_video_library.xml"/>
        <import resource="mitigations.xml"/>
    </imports>

    <parameters>
        <parameter key="shopware.filesystem.local_public" type="collection">
            <parameter key="type">local</parameter>
            <parameter key="config" type="collection">
                <parameter key="root">%kernel.project_dir%/public</parameter>
            </parameter>
        </parameter>
        <parameter key="elasticsearch.username">%env(string:default::SHOPWARE_ES_USERNAME)%</parameter>
        <parameter key="elasticsearch.password">%env(string:default::SHOPWARE_ES_PASSWORD)%</parameter>

        <parameter key="elasticsearch.index.config" type="collection">
            <parameter key="settings" type="collection">
                <parameter key="index" type="collection">
                    <parameter key="number_of_shards">%env(int:default::SHOPWARE_ES_NUMBER_OF_SHARDS)%</parameter>
                    <parameter key="number_of_replicas">%env(int:default::SHOPWARE_ES_NUMBER_OF_REPLICAS)%</parameter>
                    <parameter key="mapping.total_fields.limit">5000</parameter>
                    <parameter key="mapping.nested_fields.limit">500</parameter>
                    <parameter key="mapping.nested_objects.limit">1000000</parameter>
                </parameter>

                <parameter key="analysis" type="collection">
                    <parameter key="normalizer" type="collection">
                        <parameter key="sw_lowercase_normalizer" type="collection">
                            <parameter key="type">custom</parameter>
                            <parameter key="filter" type="collection">
                                <parameter>lowercase</parameter>
                            </parameter>
                        </parameter>
                    </parameter>

                    <parameter key="analyzer" type="collection">
                        <parameter key="sw_ngram_analyzer" type="collection">
                            <parameter key="type">custom</parameter>
                            <parameter key="tokenizer">whitespace</parameter>
                            <parameter key="filter" type="collection">
                                <parameter>lowercase</parameter>
                                <parameter>sw_ngram_filter</parameter>
                            </parameter>
                        </parameter>
                    </parameter>

                    <parameter key="filter" type="collection">
                        <parameter key="sw_ngram_filter" type="collection">
                            <parameter key="type">ngram</parameter>
                            <parameter key="min_gram">4</parameter>
                            <parameter key="max_gram">5</parameter>
                        </parameter>
                    </parameter>
                </parameter>
            </parameter>
        </parameter>

        <parameter key="shopware.cache.invalidation.appliance_search_route" type="collection">
        </parameter>
    </parameters>

    <services>
        <service id="Ersatzteilshop\Service\LanguageService">
            <argument type="service" id="language.repository"/>
        </service>

        <!-- Translations -->
        <service id="Ersatzteilshop\Resources\snippet\en_GB\SnippetFile_en_GB" public="true">
            <tag name="shopware.snippet.file"/>
        </service>

        <service id="Ersatzteilshop\Resources\snippet\de_DE\SnippetFile_de_DE" public="true">
            <tag name="shopware.snippet.file"/>
        </service>

        <service id="Ersatzteilshop\Service\ManufacturerLoader">
            <argument type="service"
                      id="Ersatzteilshop\Core\Content\ProductManufacturer\ProductManufacturerOverviewRoute"/>
        </service>

        <service id="Ersatzteilshop\Service\CategoryLoader">
            <argument type="service" id="product.repository"/>
            <argument type="service" id="category.repository"/>
            <argument type="service" id="category_manufacturer.repository"/>
            <argument type="service"
                      id="Ersatzteilshop\Core\Content\Category\CategoryRoute"/>
        </service>

        <service id="Ersatzteilshop\Service\ProductReviewLoader">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="cache.object"/>
        </service>

        <service id="Ersatzteilshop\Service\ResourceRuleLoader">
            <argument type="service" id="Ersatzteilshop\Core\Content\ResourceRule\SalesChannel\ResourceRuleRoute"/>
        </service>

        <!-- Theme -->
        <service class="League\Flysystem\FilesystemInterface" id="shopware.filesystem.local">
            <factory service="Shopware\Core\Framework\Adapter\Filesystem\FilesystemFactory" method="factory"/>
            <argument>%shopware.filesystem.local_public%</argument>
        </service>

        <service id="Ersatzteilshop\Storefront\Framework\Twig\MeineinkaufShippingExtension">
            <argument type="service" id="country.repository"/>
            <tag name="twig.extension"/>
        </service>

        <service id="Ersatzteilshop\Storefront\Framework\Twig\JsonDecodeExtension">
            <tag name="twig.extension"/>
        </service>

        <service id="Ersatzteilshop\Storefront\Framework\Twig\ProductPropertiesExtension">
            <tag name="twig.extension"/>
        </service>

        <service id="Ersatzteilshop\Storefront\Framework\Twig\ValidImageExtension">
            <tag name="twig.extension"/>
        </service>

        <service id="Ersatzteilshop\Storefront\Framework\Twig\InvalidHtmlExtension">
            <tag name="twig.extension"/>
        </service>

        <service id="Ersatzteilshop\Storefront\Framework\Twig\AswoCategoryIdsExtension">
            <tag name="twig.extension"/>
        </service>

        <service id="Ersatzteilshop\Storefront\Framework\Twig\CfThumbnailExtension">
            <argument type="service" id="Shopware\Core\Framework\Adapter\Twig\TemplateFinder"/>
            <tag name="twig.extension"/>
        </service>

        <service id="Ersatzteilshop\Storefront\Framework\Twig\LinkMaskingExtension">
            <tag name="twig.extension"/>
        </service>

        <!--Rule-->
        <service id="Ersatzteilshop\Core\Content\ResourceRule\DataAbstractionLayer\ResourceRulePayloadUpdater">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="Shopware\Core\Framework\Rule\Collector\RuleConditionRegistry"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ResourceRule\Rule\ProductInCategoryRule">
            <tag name="shopware.rule.definition"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ResourceRule\Rule\ProductOfManufacturerRule">
            <tag name="shopware.rule.definition"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ResourceRule\Rule\ProductItemsRule">
            <tag name="shopware.rule.definition"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ResourceRule\Rule\ProductNameRegexpRule">
            <tag name="shopware.rule.definition"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ResourceRule\Rule\CategoryItemsRule">
            <tag name="shopware.rule.definition"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ResourceRule\Rule\CategoryProductItemsRule">
            <tag name="shopware.rule.definition"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ResourceRule\Rule\CategoryToolsItemsRule">
            <tag name="shopware.rule.definition"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ResourceRule\Rule\CategoryCleaningItemsRule">
            <tag name="shopware.rule.definition"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ResourceRule\Rule\CategoryRepairGuideItemsRule">
            <tag name="shopware.rule.definition"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ResourceRule\Rule\ApplianceItemsRule">
            <tag name="shopware.rule.definition"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ResourceRule\Rule\ApplianceInCategoryRule">
            <tag name="shopware.rule.definition"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ResourceRule\Rule\ApplianceNameCodeRule">
            <tag name="shopware.rule.definition"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ResourceRule\Rule\ApplianceOfManufacturerRule">
            <tag name="shopware.rule.definition"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ResourceRule\Rule\ApplianceGroupItemsRule">
            <tag name="shopware.rule.definition"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ResourceRule\Rule\ApplianceGroupInCategoryRule">
            <tag name="shopware.rule.definition"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ResourceRule\Rule\ApplianceGroupCodeRule">
            <tag name="shopware.rule.definition"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ResourceRule\Rule\ApplianceGroupOfManufacturerRule">
            <tag name="shopware.rule.definition"/>
        </service>


        <service id="Ersatzteilshop\Core\Content\ResourceRule\Rule\ErrorCodesRule">
            <tag name="shopware.rule.definition"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ResourceRule\Rule\ErrorCodesInCategoryRule">
            <tag name="shopware.rule.definition"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ResourceRule\Rule\ProductInToolRule">
            <tag name="shopware.rule.definition"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ResourceRule\Rule\ManufacturerRule">
            <tag name="shopware.rule.definition"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ProductManufacturer\ProductManufacturerExtension">
            <tag name="shopware.entity.extension"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\Category\CategoryExtension">
            <tag name="shopware.entity.extension"/>
        </service>

        <service id="Ersatzteilshop\Service\CategoryManufacturerGenerator">
            <argument type="service" id="category_manufacturer.repository"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>
        </service>

        <service id="Ersatzteilshop\Service\RepairGuideLoader">
            <argument type="service" id="Ersatzteilshop\Core\Content\RepairGuide\SalesChannel\RepairGuideRoute"/>
        </service>

        <service id="Ersatzteilshop\Service\ErrorGroupLoader">
            <argument type="service" id="error_group.repository"/>
            <argument type="service" id="error_code.repository"/>
            <argument type="service" id="product_manufacturer.repository"/>
            <argument type="service" id="category.repository"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>
        </service>

        <service id="Ersatzteilshop\Service\ErrorCodeLoader">
            <argument type="service" id="error_code.repository"/>
            <argument type="service" id="category.repository"/>
        </service>

        <service id="Ersatzteilshop\Service\ProductLoader">
            <argument type="service" id="product.repository"/>
            <argument type="service" id="Shopware\Core\Content\Product\SalesChannel\Listing\ProductListingRoute"/>
            <argument type="service" id="Ersatzteilshop\Service\ProductReviewLoader"/>
        </service>

        <service id="Ersatzteilshop\Service\RepairInstructionLoader">
            <argument type="service" id="Ersatzteilshop\Core\Content\RepairInstruction\SalesChannel\RepairInstructionRoute"/>
            <argument type="service" id="repair_instruction.repository"/>
            <argument type="service" id="repair_instruction_step.repository"/>
        </service>

        <service id="Ersatzteilshop\Service\EcomCustomer">
            <argument type="service" id="country_translation.repository"/>
            <argument type="service" id="salutation_translation.repository"/>
        </service>

        <service id="Ersatzteilshop\Service\CustomerMigrateRoute">
            <argument type="service" id="Ersatzteilshop\Service\EcomCustomer"/>
            <argument type="service" id="customer.repository"/>
            <argument type="service"
                      id="Shopware\Core\System\NumberRange\ValueGenerator\NumberRangeValueGeneratorInterface"/>
            <argument type="service" id="Shopware\Core\Checkout\Customer\SalesChannel\SendPasswordRecoveryMailRoute"/>
            <argument type="service" id="request_stack"/>
        </service>


        <service id="Ersatzteilshop\Core\Content\Sitemap\Provider\ProductUrlProvider"
                 decorates="Shopware\Core\Content\Sitemap\Provider\ProductUrlProvider">
            <argument type="service" id="Shopware\Core\Content\Sitemap\Service\ConfigHandler"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="Shopware\Core\Content\Product\ProductDefinition"/>
            <argument type="service" id="Shopware\Core\Framework\DataAbstractionLayer\Dbal\Common\IteratorFactory"/>
            <argument type="service" id="router"/>

            <argument type="service" id="Ersatzteilshop\Core\Content\Sitemap\Provider\ProductUrlProvider.inner"/>
            <argument type="service" id="monolog.logger"/>

            <tag name="shopware.sitemap_url_provider"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\Sitemap\Provider\CategoryUrlProvider"
                 decorates="Shopware\Core\Content\Sitemap\Provider\CategoryUrlProvider">
            <argument type="service" id="Ersatzteilshop\Core\Content\Sitemap\Provider\CategoryUrlProvider.inner"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="monolog.logger"/>
            <argument type="service" id="router"/>

            <tag name="shopware.sitemap_url_provider"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\Sitemap\Provider\RepairGuideUrlProvider">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="monolog.logger"/>
            <argument type="service" id="router"/>

            <tag name="shopware.sitemap_url_provider"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\Sitemap\Provider\RepairInstructionUrlProvider">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="monolog.logger"/>
            <argument type="service" id="router"/>

            <tag name="shopware.sitemap_url_provider"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\Sitemap\Provider\ProductManufacturerUrlProvider">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="monolog.logger"/>
            <argument type="service" id="router"/>

            <tag name="shopware.sitemap_url_provider"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\Sitemap\Provider\ApplianceUrlProvider">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="monolog.logger"/>
            <tag name="shopware.sitemap_url_provider"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\Sitemap\Provider\ApplianceGroupUrlProvider">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="monolog.logger"/>
            <argument type="service" id="router"/>

            <tag name="shopware.sitemap_url_provider"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\Sitemap\Provider\CategoryManufacturerUrlProvider">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="monolog.logger"/>
            <argument type="service" id="router"/>

            <tag name="shopware.sitemap_url_provider"/>
        </service>

        <service id="Ersatzteilshop\Service\NavigationLoader">
            <argument type="service" id="Shopware\Core\Content\Category\Service\NavigationLoader"/>
            <argument type="service" id="Shopware\Core\Content\Category\SalesChannel\NavigationRoute"/>
            <argument type="service" id="category.repository"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\Sitemap\Provider\VideoLibraryUrlProvider">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="monolog.logger"/>
            <argument type="service" id="router"/>

            <tag name="shopware.sitemap_url_provider"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\Sitemap\Provider\ApplianceManufacturerCategoryUrlProvider">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="monolog.logger"/>
            <argument type="service" id="router"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\Appliance\SalesChannel\ApplianceManufacturerCategoryRoute"/>
            <tag name="shopware.sitemap_url_provider"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\Sitemap\Provider\ZendeskHelpcenterUrlProvider">
            <argument type="service" id="monolog.logger"/>
            <argument type="service" id="router"/>
            <argument type="service" id="Ersatzteilshop\Service\ZendeskHelpcenterClient"/>
            <argument type="service" id="slugify"/>
            <tag name="shopware.sitemap_url_provider"/>
        </service>


        <!--        <service id="Ersatzteilshop\Service\ProductStockSubscriberService" public="true">-->
        <!--            <argument type="service" id="stock_subscriber.repository"/>-->
        <!--            <argument type="service" id="stock_subscriber_product.repository"/>-->
        <!--            <argument type="service" id="product.repository"/>-->
        <!--            <argument type="service" id="validator"/>-->
        <!--            <argument type="service" id="sales_channel.repository"/>-->
        <!--            <argument type="service" id="Ersatzteilshop\Service\ProductStockNotificationMailService"/>-->
        <!--            <argument type="service" id="currency.repository"/>-->
        <!--        </service>-->

        <!--        <service id="Ersatzteilshop\Service\ProductStockNotificationMailService" public="true">-->
        <!--            <argument type="service" id="mail_template.repository"/>-->
        <!--            <argument type="service" id="Shopware\Core\Content\MailTemplate\Service\MailService"/>-->
        <!--            <call method="setLogger">-->
        <!--                <argument type="service" id="monolog.logger"/>-->
        <!--            </call>-->
        <!--        </service>-->

        <service id="Ersatzteilshop\Service\ProductSoldUpdater" public="true">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="product.repository"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>
        </service>

        <service id="Ersatzteilshop\Service\HttpCacheToken">
            <argument>%kernel.cache.hash%</argument>
            <argument type="service" id="Shopware\Storefront\Framework\Routing\RequestTransformer"/>
        </service>

        <service id="Ersatzteilshop\Service\Tracker\Tracker" abstract="true">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="messenger.bus.tracking"/>
        </service>

        <service id="Ersatzteilshop\Service\Tracker\ProductFeatureUpdater" parent="Ersatzteilshop\Service\Tracker\Tracker">
            <call method="setHandler">
                <argument type="service" id="Ersatzteilshop\MessageQueue\Handler\ProductFeatureMessageHandler"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Service\Tracker\ApplianceRelevanceUpdater" parent="Ersatzteilshop\Service\Tracker\Tracker">
        </service>

        <service id="Ersatzteilshop\Service\Tracker\CategoryRelevanceUpdater" parent="Ersatzteilshop\Service\Tracker\Tracker">
        </service>

        <service id="Ersatzteilshop\Service\CustomerRepairInstructionService" public="true">
            <argument type="service" id="Shopware\Storefront\Page\GenericPageLoader"/>
            <argument type="service" id="repair_instruction.repository"/>
            <argument type="service" id="Ersatzteilshop\Service\RepairInstructionLoader"/>

        </service>
        <service id="Ersatzteilshop\Service\CustomerRepairInstructionStepService" public="true">
            <argument type="service" id="repair_instruction_step.repository"/>
        </service>

        <service id="Ersatzteilshop\Service\CustomerMediaService" public="true">
            <argument type="service" id="media.repository"/>
            <argument type="service" id="media_folder.repository"/>
            <argument type="service" id="Shopware\Core\Content\Media\MediaService"/>
            <argument type="service" id="Shopware\Core\Content\Media\File\FileSaver"/>
        </service>

        <service id="Ersatzteilshop\Service\ElasticsearchService">
            <argument type="service" id="Elasticsearch\Client"/>
            <argument type="service" id="Shopware\Elasticsearch\Framework\ElasticsearchHelper"/>
            <argument>%elasticsearch.enabled%</argument>
            <call method="setLogger">
                <argument type="service" id="logger"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Service\ShopReviewLoader">
            <argument type="service" id="location_review.repository"/>
        </service>

        <service id="Ersatzteilshop\Service\HideAppliancesHaveNoProducts">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
        </service>

        <service id="Ersatzteilshop\Service\ProductATLoader">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
        </service>

        <service id="Ersatzteilshop\Service\ApplianceGroupLoader">
            <argument type="service" id="appliance_group.repository"/>
        </service>

        <service id="Ersatzteilshop\Storefront\Framework\Routing\ShopApiScope">
            <tag name="shopware.route_scope"/>
        </service>

        <service id="Ersatzteilshop\Service\MetaInformationShortener">
        </service>

        <service id="Ersatzteilshop\Service\CachedMenuService" public="true">
            <argument type="service" id="twig"/>
            <argument type="service" id="Ersatzteilshop\Service\NavigationLoader"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\ObjectCache\SalesChannel\ObjectCacheRoute"/>
            <argument type="service" id="object_cache.repository"/>
            <argument type="service" id="Shopware\Core\Content\Seo\SeoUrlPlaceholderHandlerInterface"/>
            <argument type="service" id="Shopware\Core\Framework\Adapter\Cache\CacheInvalidator"/>
        </service>

        <service id="Ersatzteilshop\Service\CategoryManufacturerLoader" public="true">
            <argument type="service" id="category_manufacturer.repository"/>
        </service>

        <service id="Ersatzteilshop\Service\ChatGPT4Client" public="true">
        </service>

        <service id="Ersatzteilshop\Service\ImageSearch\GeminiAI" public="true">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="cache.app"/>
        </service>

        <service id="Ersatzteilshop\Service\ImageSearch\AnthropicAI" public="true">
            <argument>%env(ANTHROPIC_API_KEY)%</argument>
        </service>

        <service id="Ersatzteilshop\Service\SchemaOrgService" />

        <service id="Ersatzteilshop\Service\ZendeskHelpcenterClient" public="true">
            <argument type="service" id="twig"/>
        </service>

        <service id="Ersatzteilshop\Service\VisitedApplianceService" public="true">
            <argument type="service" id="session"/>
        </service>

        <service id="Ersatzteilshop\Service\ImageSearch\Interface\AIModelAnalyzerInterface">
            <factory service="Ersatzteilshop\Service\ImageSearch\AIModelAnalyzerFactory" method="createAnalyzer"/>
        </service>

        <service id="Ersatzteilshop\Service\ImageSearch\AIModelAnalyzerFactory">
            <argument type="service" id="Ersatzteilshop\Service\ImageSearch\AnthropicAI"/>
            <argument type="service" id="Ersatzteilshop\Service\ImageSearch\GeminiAI"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
        </service>

        <service id="Ersatzteilshop\Service\ImageSearch\ApplianceAnalyzer">
            <argument type="service" id="Ersatzteilshop\Service\ImageSearch\Interface\AIModelAnalyzerInterface"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\Appliance\SalesChannel\ApplianceRoute"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="Ersatzteilshop\Service\ImageSearch\ImageSearchResult" public="true"/>

        <service id="Ersatzteilshop\Core\Checkout\Cart\LineItem\EnsureItemCountMet">
            <tag name="shopware.cart.validator"/>
        </service>

        <service id="Ersatzteilshop\Core\Checkout\Cart\LineItem\EnsurePrioWhitelistedCountry">
            <argument type="service" id="Ersatzteilshop\Service\CartService"/>
            <argument type="service" id="product.repository"/>
            <tag name="shopware.cart.validator"/>
        </service>

        <service id="Ersatzteilshop\Service\ImageSearch\ImageSearchAnalytics" public="true">
            <argument type="service" id="session"/>
            <argument type="service" id="request_stack"/>
            <argument type="service" id="messenger.bus.priority"/>
        </service>

        <service id="Ersatzteilshop\Command\CategoryImageChangeRandomProductCommand">
            <argument type="service" id="category.repository"/>
            <argument type="service" id="product.repository"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Service\Appliance\ApplianceAggregator" public="true">
            <argument type="service" id="Ersatzteilshop\Core\Content\Appliance\SalesChannel\ApplianceRoute"/>
            <argument type="service" id="XantenAswo\Service\Aswo" />
            <argument type="service" id="appliance.repository"/>
            <argument type="service" id="product.repository"/>
        </service>

        <service id="Ersatzteilshop\Service\Appliance\ApplianceAggregatorInterface" alias="Ersatzteilshop\Service\Appliance\ApplianceAggregator" />

        <service id="Ersatzteilshop\Command\CategoryImageChangeRandomProductThumbnailCommand">
            <argument type="service" id="category.repository"/>
            <argument type="service" id="product.repository"/>
            <argument type="service" id="media_thumbnail.repository"/>
            <tag name="console.command"/>
        </service>

        <service id="Ersatzteilshop\Service\CartService">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="Shopware\Core\Checkout\Cart\SalesChannel\CartService"/>
        </service>

        <service id="Ersatzteilshop\Core\Framework\Adapter\Twig\Extension\CartServiceExtension">
            <argument type="service" id="Ersatzteilshop\Service\CartService"/>
            <tag name="twig.extension"/>
        </service>
    </services>

</container>
