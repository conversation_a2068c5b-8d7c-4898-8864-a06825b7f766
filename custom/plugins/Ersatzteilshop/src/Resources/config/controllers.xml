<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>

        <service id="Ersatzteilshop\Core\Framework\Api\Controller\ApiController"
                 decorates="Shopware\Core\Framework\Api\Controller\ApiController"
                 public="true">
            <argument type="service" id="Ersatzteilshop\Core\Framework\Api\Controller\ApiController.inner"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

<!--        <service id="Ersatzteilshop\Storefront\Controller\ProductStockSubscriberController" public="true">-->
<!--            <argument type="service" id="Ersatzteilshop\Service\ProductStockSubscriberService"/>-->
<!--            <argument type="service" id="Shopware\Core\Checkout\Cart\SalesChannel\CartService"/>-->
<!--            <argument type="service" id="Shopware\Core\Content\Product\Cart\ProductLineItemFactory"/>-->
<!--            <argument type="service" id="sales_channel.product.repository"/>-->
<!--            <call method="setContainer">-->
<!--                <argument type="service" id="service_container"/>-->
<!--            </call>-->
<!--        </service>-->

        <service id="Ersatzteilshop\Storefront\Controller\TrackerController" public="true">
            <argument type="service" id="tracker.repository"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\CustomerMediaUploadController" public="true">
            <argument type="service" id="Shopware\Core\Content\Media\MediaService"/>
            <argument type="service" id="Shopware\Core\Content\Media\File\FileSaver"/>
            <argument type="service" id="Ersatzteilshop\Service\CustomerMediaService"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\SearchAjaxController" public="true">
            <argument type="service" id="Shopware\Storefront\Page\Search\SearchPageLoader"/>
            <argument type="service" id="Ersatzteilshop\Service\ProductReviewLoader"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\ImageDownloadController" public="true">
            <argument type="service" id="media.repository"/>
            <argument type="service" id="slugify"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\CountryShippingCostController" public="true">
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\GoogleCustomerReviewController" public="true">
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\AswoGoogleExportController" public="true">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="XantenAswo\Service\Config"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\ChatGPT4Controller" public="true">
            <argument type="service" id="Ersatzteilshop\Service\ChatGPT4Client"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\VideosController" public="true">
            <argument type="service" id="Ersatzteilshop\Storefront\Page\VideoLibrary\VideoLibraryPageLoader"/>
            <argument>%env(YOUTUBE_API_KEY)%</argument>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Storefront\Controller\ZendeskHelpcenterController" public="true">
            <argument type="service" id="Ersatzteilshop\Service\ZendeskHelpcenterClient"/>
            <argument type="service" id="slugify"/>
            <argument type="service" id="Shopware\Storefront\Page\Navigation\NavigationPageLoader"/>
            <argument type="service" id="session"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>
    </services>

</container>
