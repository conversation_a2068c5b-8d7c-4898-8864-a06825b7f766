<?php declare(strict_types=1);

namespace Ersatzteilshop\Service;

class MetaInformationShortener
{
    public const TITLE = 60;

    public const DESCRIPTION = 150;

    public static function shorten(?string $text, int $length = self::TITLE): ?string
    {
        if (null === $text) {
            return null;
        }

        if (mb_strlen($text) < $length) {
            return $text;
        }

        $substring = mb_substr($text, 0, $length);

        $shortenText = mb_substr($substring, 0, mb_strrpos($substring, ' '));

        if (mb_strlen($shortenText) < ($length - 10)) {
            $shortenText = $substring;
        }

        return $shortenText;
    }
}
