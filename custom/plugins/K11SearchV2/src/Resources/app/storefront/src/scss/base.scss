@import 'page/search/product-search';
@import 'component/pagination';
.appliance-result {
  .panel {
    .panel-head {
      h3 {
        line-height: 1.4;
      }
    }
    @include media-breakpoint-up(md) {
      padding-right: 20px;
      padding-left: 20px;
      &:first-child {
        padding-left: 10px;
      }
      &:last-child {
        padding-right: 10px;
      }
      .panel-head {
        cursor: default;
        background: none;
        border-bottom: none;
        margin-bottom: 0;
      }
      .panel-body {
        padding: 0;
      }
      &.hide {
        .panel-head {
          background: none;
          border-bottom: none;
          margin-bottom: 0;
        }
        .panel-body {
          display: block !important;
        }
      }
    }

  }
}
.search-widget-top {
  padding: 40px 30px 20px;
  margin-top: 15px
}
.search-model-number-form {
  max-width: 650px;
  margin: 1.5rem auto 0;
}
.search-model-number-form form {
  margin: 1.5rem 0;
}
.search-model-number-form form button.text-label {
  background: #ccc;
  border: #ccc;
  border-bottom-left-radius: 3px;
  border-top-left-radius: 3px;
  color: #000;
  font-weight: 500;
  padding: 0 20px;
}
.search-model-number-form form button[type="submit"] {
  padding: 0 30px;
}
@include media-breakpoint-down(md) {
  .search-model-number-form form button[disabled="disabled"] {
    display: none;
  }
}
.table-responsive-sm {
  display: table;
}
.search-model-number-form .cms-element-search-widget-link {
  color: #000;
  font-size: 13px;
  text-decoration: underline;
}

.button-wrapper {
    margin-top: 20px;
    text-align: center;
}
#product-result {
    .error-page-content-desktop {
        .col-4 {
            padding-left: 20px;
            padding-right: 20px;
            &:first-child {
                padding-left: 10px;
            }
            &:last-child {
                padding-right: 10px;
            }
        }
        .cms-element-search-widget-link {
            color: #37ad27;
        }
    }
    .appliance-result-info {
        .panel {
            padding-right: 0;
            padding-left: 0;
        }
    }
}
