<?php declare(strict_types=1);

namespace XantenOrder\Service;

use Psr\Log\LoggerInterface;
use Psr\Http\Message\ResponseInterface;
use GuzzleHttp\Client as GuzzleHttpClient;
use Shopware\Core\Checkout\Customer\CustomerEntity;
use Shopware\Core\System\SystemConfig\SystemConfigService;

class Client
{
    private const CUSTOMER_EMAIL_ATTRIBUTE = 'customerEmail';

    private const URI_ORDER = 'order';

    private const URI_INVOICE = 'download-invoice';

    private GuzzleHttpClient $client;

    private LoggerInterface $logger;

    public function __construct(SystemConfigService $config, LoggerInterface $logger)
    {
        $this->client = new GuzzleHttpClient([
            'base_uri' => rtrim($config->getString('XantenOrder.config.apiUrl'), '/') . '/',
            'headers' => [
                'x-api-key' => $config->getString('XantenOrder.config.apiKey'),
                'Authorization' => sprintf(
                    'Basic %s',
                    base64_encode(
                        $config->getString('XantenOrder.config.username')
                        . ':'
                        . $config->getString('XantenOrder.config.password'))
                )
            ]
        ]);
        $this->logger = $logger;
    }

    public function loadOrders(CustomerEntity $customer, array $definedOrderIds = []): array
    {
        try {
            $url = sprintf(
                '%s?%s=%s',
                static::URI_ORDER,
                static::CUSTOMER_EMAIL_ATTRIBUTE,
                $customer->getEmail()
            );

            // Debug logging
            $this->logger->info('XantenOrder: Loading orders for customer', [
                'customerEmail' => $customer->getEmail(),
                'customerId' => $customer->getId(),
                'url' => $url,
                'definedOrderIds' => $definedOrderIds
            ]);

            $response = $this->client->get($url);
            $responseBody = (string)$response->getBody();

            $this->logger->info('XantenOrder: API Response received', [
                'statusCode' => $response->getStatusCode(),
                'responseLength' => strlen($responseBody),
                'responseBody' => $responseBody
            ]);

            $orders = \GuzzleHttp\json_decode($responseBody, true);

            if (!empty($definedOrderIds)) {
                $orders = array_values(array_filter($orders, static function (array $item) use ($definedOrderIds) {
                    return in_array($item['reference'], $definedOrderIds, true);
                }));
            }

            $this->logger->info('XantenOrder: Orders processed', [
                'orderCount' => count($orders),
                'orders' => $orders
            ]);

            return $orders;
        } catch (\Throwable $e) {
            $this->logger->error('XantenOrder: Error loading orders', [
                'exception' => $e,
                'customerEmail' => $customer->getEmail(),
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [];
        }
    }

    public function loadInvoice(string $orderId): ?ResponseInterface
    {
        try {
            return $this->client->get(sprintf(
                '%s?swOrderId=%s',
                static::URI_INVOICE,
                $orderId
            ));
        } catch (\Throwable $e) {
            $this->logger->error(
                $e->getMessage(),
                ['exception' => $e, 'orderId' => $orderId]
            );

            return null;
        }
    }
}
